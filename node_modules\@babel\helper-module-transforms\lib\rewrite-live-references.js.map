{"version": 3, "names": ["_assert", "require", "_core", "_helperSimpleAccess", "isInType", "path", "parent", "type", "parentPath", "exportKind", "isStatement", "isExpression", "rewriteLiveReferences", "programPath", "metadata", "wrapReference", "imported", "Map", "exported", "requeueInParent", "requeue", "source", "data", "localName", "importName", "imports", "set", "importsNamespace", "local", "exportMeta", "get", "push", "names", "rewriteBindingInitVisitorState", "scope", "traverse", "rewriteBindingInitVisitor", "bindingNames", "Set", "Array", "from", "keys", "simplifyAccess", "rewriteReferencesVisitorState", "seen", "WeakSet", "buildImportReference", "identNode", "meta", "referenced", "wrap", "_wrapReference", "namespace", "t", "identifier", "name", "_wrapReference2", "interop", "computed", "stringSpecifiers", "has", "memberExpression", "stringLiteral", "rewriteReferencesVisitor", "<PERSON><PERSON>", "skip", "ClassDeclaration", "id", "node", "Error", "exportNames", "length", "statement", "expressionStatement", "buildBindingExportAssignmentExpression", "_blockHoist", "insertAfter", "VariableDeclaration", "isVar", "kind", "decl", "init", "isIdentifier", "isArrowFunctionExpression", "isFunctionExpression", "isClassExpression", "buildUndefinedNode", "Object", "getOuterBindingIdentifiers", "localExpr", "exportsObjectName", "exportName", "currentScope", "hasOwnBinding", "rename", "reduce", "expr", "assignmentExpression", "buildImportThrow", "template", "expression", "ast", "ReferencedIdentifier", "add", "importData", "buildCodeFrameError", "localBinding", "getBinding", "rootBinding", "ref", "loc", "isCallExpression", "callee", "isOptionalCallExpression", "isTaggedTemplateExpression", "tag", "isMemberExpression", "replaceWith", "sequenceExpression", "numericLiteral", "isJSXIdentifier", "object", "property", "jsxMemberExpression", "jsxIdentifier", "UpdateExpression", "arg", "update", "exportedNames", "operator", "prefix", "cloneNode", "generateDeclaredUidIdentifier", "AssignmentExpression", "exit", "left", "assert", "assignment", "right", "ids", "programScopeIds", "filter", "find", "items", "for<PERSON>ach", "isExpressionStatement", "ForOfStatement|ForInStatement", "programScope", "isVariableDeclaration", "didTransformExport", "importConstViolationName", "loopBodyScope", "ensureBlock", "bodyPath", "newLoopId", "generateUidIdentifierBasedOnNode", "variableDeclaration", "variableDeclarator", "registerDeclaration", "unshiftContainer"], "sources": ["../src/rewrite-live-references.ts"], "sourcesContent": ["import assert from \"assert\";\nimport { template, types as t } from \"@babel/core\";\nimport type { NodeP<PERSON>, Visitor, Scope } from \"@babel/core\";\nimport simplifyAccess from \"@babel/helper-simple-access\";\n\nimport type { ModuleMetadata } from \"./normalize-and-load-metadata.ts\";\n\ninterface RewriteReferencesVisitorState {\n  exported: Map<any, any>;\n  metadata: ModuleMetadata;\n  requeueInParent: (path: NodePath) => void;\n  scope: Scope;\n  imported: Map<any, any>;\n  buildImportReference: (\n    [source, importName, localName]: readonly [string, string, string],\n    identNode: t.Identifier | t.CallExpression | t.JSXIdentifier,\n  ) => any;\n  seen: WeakSet<object>;\n}\n\ninterface RewriteBindingInitVisitorState {\n  exported: Map<any, any>;\n  metadata: ModuleMetadata;\n  requeueInParent: (path: NodePath) => void;\n  scope: Scope;\n}\n\nfunction isInType(path: NodePath) {\n  do {\n    switch (path.parent.type) {\n      case \"TSTypeAnnotation\":\n      case \"TSTypeAliasDeclaration\":\n      case \"TSTypeReference\":\n      case \"TypeAnnotation\":\n      case \"TypeAlias\":\n        return true;\n      case \"ExportSpecifier\":\n        return (\n          (\n            path.parentPath.parent as\n              | t.ExportDefaultDeclaration\n              | t.ExportNamedDeclaration\n          ).exportKind === \"type\"\n        );\n      default:\n        if (path.parentPath.isStatement() || path.parentPath.isExpression()) {\n          return false;\n        }\n    }\n  } while ((path = path.parentPath));\n}\n\nexport default function rewriteLiveReferences(\n  programPath: NodePath<t.Program>,\n  metadata: ModuleMetadata,\n  wrapReference: (ref: t.Expression, payload: unknown) => null | t.Expression,\n) {\n  const imported = new Map();\n  const exported = new Map();\n  const requeueInParent = (path: NodePath) => {\n    // Manually re-queue `exports.default =` expressions so that the ES3\n    // transform has an opportunity to convert them. Ideally this would\n    // happen automatically from the replaceWith above. See #4140 for\n    // more info.\n    programPath.requeue(path);\n  };\n\n  for (const [source, data] of metadata.source) {\n    for (const [localName, importName] of data.imports) {\n      imported.set(localName, [source, importName, null]);\n    }\n    for (const localName of data.importsNamespace) {\n      imported.set(localName, [source, null, localName]);\n    }\n  }\n\n  for (const [local, data] of metadata.local) {\n    let exportMeta = exported.get(local);\n    if (!exportMeta) {\n      exportMeta = [];\n      exported.set(local, exportMeta);\n    }\n\n    exportMeta.push(...data.names);\n  }\n\n  // Rewrite initialization of bindings to update exports.\n  const rewriteBindingInitVisitorState: RewriteBindingInitVisitorState = {\n    metadata,\n    requeueInParent,\n    scope: programPath.scope,\n    exported, // local name => exported name list\n  };\n  programPath.traverse(\n    // eslint-disable-next-line @typescript-eslint/no-use-before-define\n    rewriteBindingInitVisitor,\n    rewriteBindingInitVisitorState,\n  );\n\n  // NOTE(logan): The 'Array.from' calls are to make this code with in loose mode.\n  const bindingNames = new Set([\n    ...Array.from(imported.keys()),\n    ...Array.from(exported.keys()),\n  ]);\n  if (process.env.BABEL_8_BREAKING) {\n    simplifyAccess(programPath, bindingNames);\n  } else {\n    // @ts-ignore(Babel 7 vs Babel 8) The third param has been removed in Babel 8.\n    simplifyAccess(programPath, bindingNames, false);\n  }\n\n  // Rewrite reads/writes from imports and exports to have the correct behavior.\n  const rewriteReferencesVisitorState: RewriteReferencesVisitorState = {\n    seen: new WeakSet(),\n    metadata,\n    requeueInParent,\n    scope: programPath.scope,\n    imported, // local / import\n    exported, // local name => exported name list\n    buildImportReference([source, importName, localName], identNode) {\n      const meta = metadata.source.get(source);\n      meta.referenced = true;\n\n      if (localName) {\n        if (meta.wrap) {\n          // @ts-expect-error Fixme: we should handle the case when identNode is a JSXIdentifier\n          identNode = wrapReference(identNode, meta.wrap) ?? identNode;\n        }\n        return identNode;\n      }\n\n      let namespace: t.Expression = t.identifier(meta.name);\n      if (meta.wrap) {\n        namespace = wrapReference(namespace, meta.wrap) ?? namespace;\n      }\n\n      if (importName === \"default\" && meta.interop === \"node-default\") {\n        return namespace;\n      }\n\n      const computed = metadata.stringSpecifiers.has(importName);\n\n      return t.memberExpression(\n        namespace,\n        computed ? t.stringLiteral(importName) : t.identifier(importName),\n        computed,\n      );\n    },\n  };\n  // eslint-disable-next-line @typescript-eslint/no-use-before-define\n  programPath.traverse(rewriteReferencesVisitor, rewriteReferencesVisitorState);\n}\n\n/**\n * A visitor to inject export update statements during binding initialization.\n */\nconst rewriteBindingInitVisitor: Visitor<RewriteBindingInitVisitorState> = {\n  Scope(path) {\n    path.skip();\n  },\n  ClassDeclaration(path) {\n    const { requeueInParent, exported, metadata } = this;\n\n    const { id } = path.node;\n    if (!id) throw new Error(\"Expected class to have a name\");\n    const localName = id.name;\n\n    const exportNames = exported.get(localName) || [];\n    if (exportNames.length > 0) {\n      const statement = t.expressionStatement(\n        // eslint-disable-next-line @typescript-eslint/no-use-before-define\n        buildBindingExportAssignmentExpression(\n          metadata,\n          exportNames,\n          t.identifier(localName),\n          path.scope,\n        ),\n      );\n      // @ts-expect-error todo(flow->ts): avoid mutations\n      statement._blockHoist = path.node._blockHoist;\n\n      requeueInParent(path.insertAfter(statement)[0]);\n    }\n  },\n  VariableDeclaration(path) {\n    const { requeueInParent, exported, metadata } = this;\n\n    const isVar = path.node.kind === \"var\";\n\n    for (const decl of path.get(\"declarations\")) {\n      const { id } = decl.node;\n      let { init } = decl.node;\n      if (\n        t.isIdentifier(id) &&\n        exported.has(id.name) &&\n        !t.isArrowFunctionExpression(init) &&\n        (!t.isFunctionExpression(init) || init.id) &&\n        (!t.isClassExpression(init) || init.id)\n      ) {\n        if (!init) {\n          if (isVar) {\n            // This variable might have already been assigned to, and the\n            // uninitalized declaration doesn't set it to `undefined` and does\n            // not updated the exported value.\n            continue;\n          } else {\n            init = path.scope.buildUndefinedNode();\n          }\n        }\n        // eslint-disable-next-line @typescript-eslint/no-use-before-define\n        decl.node.init = buildBindingExportAssignmentExpression(\n          metadata,\n          exported.get(id.name),\n          init,\n          path.scope,\n        );\n        requeueInParent(decl.get(\"init\"));\n      } else {\n        for (const localName of Object.keys(\n          decl.getOuterBindingIdentifiers(),\n        )) {\n          if (exported.has(localName)) {\n            const statement = t.expressionStatement(\n              // eslint-disable-next-line @typescript-eslint/no-use-before-define\n              buildBindingExportAssignmentExpression(\n                metadata,\n                exported.get(localName),\n                t.identifier(localName),\n                path.scope,\n              ),\n            );\n            // @ts-expect-error todo(flow->ts): avoid mutations\n            statement._blockHoist = path.node._blockHoist;\n\n            requeueInParent(path.insertAfter(statement)[0]);\n          }\n        }\n      }\n    }\n  },\n};\n\nconst buildBindingExportAssignmentExpression = (\n  metadata: ModuleMetadata,\n  exportNames: string[],\n  localExpr: t.Expression,\n  scope: Scope,\n) => {\n  const exportsObjectName = metadata.exportName;\n  for (\n    let currentScope = scope;\n    currentScope != null;\n    currentScope = currentScope.parent\n  ) {\n    if (currentScope.hasOwnBinding(exportsObjectName)) {\n      currentScope.rename(exportsObjectName);\n    }\n  }\n  return (exportNames || []).reduce((expr, exportName) => {\n    // class Foo {} export { Foo, Foo as Bar };\n    // as\n    // class Foo {} exports.Foo = exports.Bar = Foo;\n    const { stringSpecifiers } = metadata;\n    const computed = stringSpecifiers.has(exportName);\n    return t.assignmentExpression(\n      \"=\",\n      t.memberExpression(\n        t.identifier(exportsObjectName),\n        computed ? t.stringLiteral(exportName) : t.identifier(exportName),\n        /* computed */ computed,\n      ),\n      expr,\n    );\n  }, localExpr);\n};\n\nconst buildImportThrow = (localName: string) => {\n  return template.expression.ast`\n    (function() {\n      throw new Error('\"' + '${localName}' + '\" is read-only.');\n    })()\n  `;\n};\n\nconst rewriteReferencesVisitor: Visitor<RewriteReferencesVisitorState> = {\n  ReferencedIdentifier(path) {\n    const { seen, buildImportReference, scope, imported, requeueInParent } =\n      this;\n    if (seen.has(path.node)) return;\n    seen.add(path.node);\n\n    const localName = path.node.name;\n\n    const importData = imported.get(localName);\n    if (importData) {\n      if (isInType(path)) {\n        throw path.buildCodeFrameError(\n          `Cannot transform the imported binding \"${localName}\" since it's also used in a type annotation. ` +\n            `Please strip type annotations using @babel/preset-typescript or @babel/preset-flow.`,\n        );\n      }\n\n      const localBinding = path.scope.getBinding(localName);\n      const rootBinding = scope.getBinding(localName);\n\n      // redeclared in this scope\n      if (rootBinding !== localBinding) return;\n\n      const ref = buildImportReference(importData, path.node);\n\n      // Preserve the binding location so that sourcemaps are nicer.\n      ref.loc = path.node.loc;\n\n      if (\n        (path.parentPath.isCallExpression({ callee: path.node }) ||\n          path.parentPath.isOptionalCallExpression({ callee: path.node }) ||\n          path.parentPath.isTaggedTemplateExpression({ tag: path.node })) &&\n        t.isMemberExpression(ref)\n      ) {\n        path.replaceWith(t.sequenceExpression([t.numericLiteral(0), ref]));\n      } else if (path.isJSXIdentifier() && t.isMemberExpression(ref)) {\n        const { object, property } = ref;\n        path.replaceWith(\n          t.jsxMemberExpression(\n            // @ts-expect-error todo(flow->ts): possible bug `object` might not have a name\n            t.jsxIdentifier(object.name),\n            // @ts-expect-error todo(flow->ts): possible bug `property` might not have a name\n            t.jsxIdentifier(property.name),\n          ),\n        );\n      } else {\n        path.replaceWith(ref);\n      }\n\n      requeueInParent(path);\n\n      // The path could have been replaced with an identifier that would\n      // otherwise be re-visited, so we skip processing its children.\n      path.skip();\n    }\n  },\n\n  UpdateExpression(path) {\n    const {\n      scope,\n      seen,\n      imported,\n      exported,\n      requeueInParent,\n      buildImportReference,\n    } = this;\n\n    if (seen.has(path.node)) return;\n\n    seen.add(path.node);\n\n    const arg = path.get(\"argument\");\n\n    // No change needed\n    if (arg.isMemberExpression()) return;\n\n    const update = path.node;\n\n    if (arg.isIdentifier()) {\n      const localName = arg.node.name;\n\n      // redeclared in this scope\n      if (scope.getBinding(localName) !== path.scope.getBinding(localName)) {\n        return;\n      }\n\n      const exportedNames = exported.get(localName);\n      const importData = imported.get(localName);\n\n      if (exportedNames?.length > 0 || importData) {\n        if (importData) {\n          path.replaceWith(\n            t.assignmentExpression(\n              // eslint-disable-next-line @typescript-eslint/no-unnecessary-type-assertion\n              (update.operator[0] + \"=\") as t.AssignmentExpression[\"operator\"],\n              buildImportReference(importData, arg.node),\n              buildImportThrow(localName),\n            ),\n          );\n        } else if (update.prefix) {\n          // ++foo\n          // =>   exports.foo = ++foo\n          path.replaceWith(\n            buildBindingExportAssignmentExpression(\n              this.metadata,\n              exportedNames,\n              t.cloneNode(update),\n              path.scope,\n            ),\n          );\n        } else {\n          // foo++\n          // =>   (ref = i++, exports.i = i, ref)\n          const ref = scope.generateDeclaredUidIdentifier(localName);\n\n          path.replaceWith(\n            t.sequenceExpression([\n              t.assignmentExpression(\n                \"=\",\n                t.cloneNode(ref),\n                t.cloneNode(update),\n              ),\n              buildBindingExportAssignmentExpression(\n                this.metadata,\n                exportedNames,\n                t.identifier(localName),\n                path.scope,\n              ),\n              t.cloneNode(ref),\n            ]),\n          );\n        }\n      }\n    }\n\n    requeueInParent(path);\n    path.skip();\n  },\n\n  AssignmentExpression: {\n    exit(path) {\n      const {\n        scope,\n        seen,\n        imported,\n        exported,\n        requeueInParent,\n        buildImportReference,\n      } = this;\n\n      if (seen.has(path.node)) return;\n      seen.add(path.node);\n\n      const left = path.get(\"left\");\n\n      // No change needed\n      if (left.isMemberExpression()) return;\n\n      if (left.isIdentifier()) {\n        // Simple update-assign foo += 1; export { foo };\n        // =>   exports.foo =  (foo += 1);\n        const localName = left.node.name;\n\n        // redeclared in this scope\n        if (scope.getBinding(localName) !== path.scope.getBinding(localName)) {\n          return;\n        }\n\n        const exportedNames = exported.get(localName);\n        const importData = imported.get(localName);\n        if (exportedNames?.length > 0 || importData) {\n          assert(path.node.operator === \"=\", \"Path was not simplified\");\n\n          const assignment = path.node;\n\n          if (importData) {\n            assignment.left = buildImportReference(importData, left.node);\n\n            assignment.right = t.sequenceExpression([\n              assignment.right,\n              buildImportThrow(localName),\n            ]);\n          }\n\n          path.replaceWith(\n            buildBindingExportAssignmentExpression(\n              this.metadata,\n              exportedNames,\n              assignment,\n              path.scope,\n            ),\n          );\n          requeueInParent(path);\n        }\n      } else {\n        const ids = left.getOuterBindingIdentifiers();\n        const programScopeIds = Object.keys(ids).filter(\n          localName =>\n            scope.getBinding(localName) === path.scope.getBinding(localName),\n        );\n        const id = programScopeIds.find(localName => imported.has(localName));\n\n        if (id) {\n          path.node.right = t.sequenceExpression([\n            path.node.right,\n            buildImportThrow(id),\n          ]);\n        }\n\n        // Complex ({a, b, c} = {}); export { a, c };\n        // =>   ({a, b, c} = {}), (exports.a = a, exports.c = c);\n        const items: t.Expression[] = [];\n        programScopeIds.forEach(localName => {\n          const exportedNames = exported.get(localName) || [];\n          if (exportedNames.length > 0) {\n            items.push(\n              buildBindingExportAssignmentExpression(\n                this.metadata,\n                exportedNames,\n                t.identifier(localName),\n                path.scope,\n              ),\n            );\n          }\n        });\n\n        if (items.length > 0) {\n          let node: t.Node = t.sequenceExpression(items);\n          if (path.parentPath.isExpressionStatement()) {\n            node = t.expressionStatement(node);\n            // @ts-expect-error todo(flow->ts): avoid mutations\n            node._blockHoist = path.parentPath.node._blockHoist;\n          }\n\n          const statement = path.insertAfter(node)[0];\n          requeueInParent(statement);\n        }\n      }\n    },\n  },\n  \"ForOfStatement|ForInStatement\"(\n    path: NodePath<t.ForOfStatement | t.ForInStatement>,\n  ) {\n    const { scope, node } = path;\n    const { left } = node;\n    const { exported, imported, scope: programScope } = this;\n\n    if (!t.isVariableDeclaration(left)) {\n      let didTransformExport = false,\n        importConstViolationName;\n      const loopBodyScope = path.get(\"body\").scope;\n      for (const name of Object.keys(t.getOuterBindingIdentifiers(left))) {\n        if (programScope.getBinding(name) === scope.getBinding(name)) {\n          if (exported.has(name)) {\n            didTransformExport = true;\n            if (loopBodyScope.hasOwnBinding(name)) {\n              loopBodyScope.rename(name);\n            }\n          }\n          if (imported.has(name) && !importConstViolationName) {\n            importConstViolationName = name;\n          }\n        }\n      }\n      if (!didTransformExport && !importConstViolationName) {\n        return;\n      }\n\n      path.ensureBlock();\n      const bodyPath = path.get(\"body\") as NodePath<t.BlockStatement>;\n\n      const newLoopId = scope.generateUidIdentifierBasedOnNode(left);\n      path\n        .get(\"left\")\n        .replaceWith(\n          t.variableDeclaration(\"let\", [\n            t.variableDeclarator(t.cloneNode(newLoopId)),\n          ]),\n        );\n      scope.registerDeclaration(path.get(\"left\"));\n\n      if (didTransformExport) {\n        bodyPath.unshiftContainer(\n          \"body\",\n          t.expressionStatement(t.assignmentExpression(\"=\", left, newLoopId)),\n        );\n      }\n      if (importConstViolationName) {\n        bodyPath.unshiftContainer(\n          \"body\",\n          t.expressionStatement(buildImportThrow(importConstViolationName)),\n        );\n      }\n    }\n  },\n};\n"], "mappings": ";;;;;;AAAA,IAAAA,OAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;AAEA,IAAAE,mBAAA,GAAAF,OAAA;AAwBA,SAASG,QAAQA,CAACC,IAAc,EAAE;EAChC,GAAG;IACD,QAAQA,IAAI,CAACC,MAAM,CAACC,IAAI;MACtB,KAAK,kBAAkB;MACvB,KAAK,wBAAwB;MAC7B,KAAK,iBAAiB;MACtB,KAAK,gBAAgB;MACrB,KAAK,WAAW;QACd,OAAO,IAAI;MACb,KAAK,iBAAiB;QACpB,OAEIF,IAAI,CAACG,UAAU,CAACF,MAAM,CAGtBG,UAAU,KAAK,MAAM;MAE3B;QACE,IAAIJ,IAAI,CAACG,UAAU,CAACE,WAAW,CAAC,CAAC,IAAIL,IAAI,CAACG,UAAU,CAACG,YAAY,CAAC,CAAC,EAAE;UACnE,OAAO,KAAK;QACd;IACJ;EACF,CAAC,QAASN,IAAI,GAAGA,IAAI,CAACG,UAAU;AAClC;AAEe,SAASI,qBAAqBA,CAC3CC,WAAgC,EAChCC,QAAwB,EACxBC,aAA2E,EAC3E;EACA,MAAMC,QAAQ,GAAG,IAAIC,GAAG,CAAC,CAAC;EAC1B,MAAMC,QAAQ,GAAG,IAAID,GAAG,CAAC,CAAC;EAC1B,MAAME,eAAe,GAAId,IAAc,IAAK;IAK1CQ,WAAW,CAACO,OAAO,CAACf,IAAI,CAAC;EAC3B,CAAC;EAED,KAAK,MAAM,CAACgB,MAAM,EAAEC,IAAI,CAAC,IAAIR,QAAQ,CAACO,MAAM,EAAE;IAC5C,KAAK,MAAM,CAACE,SAAS,EAAEC,UAAU,CAAC,IAAIF,IAAI,CAACG,OAAO,EAAE;MAClDT,QAAQ,CAACU,GAAG,CAACH,SAAS,EAAE,CAACF,MAAM,EAAEG,UAAU,EAAE,IAAI,CAAC,CAAC;IACrD;IACA,KAAK,MAAMD,SAAS,IAAID,IAAI,CAACK,gBAAgB,EAAE;MAC7CX,QAAQ,CAACU,GAAG,CAACH,SAAS,EAAE,CAACF,MAAM,EAAE,IAAI,EAAEE,SAAS,CAAC,CAAC;IACpD;EACF;EAEA,KAAK,MAAM,CAACK,KAAK,EAAEN,IAAI,CAAC,IAAIR,QAAQ,CAACc,KAAK,EAAE;IAC1C,IAAIC,UAAU,GAAGX,QAAQ,CAACY,GAAG,CAACF,KAAK,CAAC;IACpC,IAAI,CAACC,UAAU,EAAE;MACfA,UAAU,GAAG,EAAE;MACfX,QAAQ,CAACQ,GAAG,CAACE,KAAK,EAAEC,UAAU,CAAC;IACjC;IAEAA,UAAU,CAACE,IAAI,CAAC,GAAGT,IAAI,CAACU,KAAK,CAAC;EAChC;EAGA,MAAMC,8BAA8D,GAAG;IACrEnB,QAAQ;IACRK,eAAe;IACfe,KAAK,EAAErB,WAAW,CAACqB,KAAK;IACxBhB;EACF,CAAC;EACDL,WAAW,CAACsB,QAAQ,CAElBC,yBAAyB,EACzBH,8BACF,CAAC;EAGD,MAAMI,YAAY,GAAG,IAAIC,GAAG,CAAC,CAC3B,GAAGC,KAAK,CAACC,IAAI,CAACxB,QAAQ,CAACyB,IAAI,CAAC,CAAC,CAAC,EAC9B,GAAGF,KAAK,CAACC,IAAI,CAACtB,QAAQ,CAACuB,IAAI,CAAC,CAAC,CAAC,CAC/B,CAAC;EAGK;IAEL,IAAAC,2BAAc,EAAC7B,WAAW,EAAEwB,YAAY,EAAE,KAAK,CAAC;EAClD;EAGA,MAAMM,6BAA4D,GAAG;IACnEC,IAAI,EAAE,IAAIC,OAAO,CAAC,CAAC;IACnB/B,QAAQ;IACRK,eAAe;IACfe,KAAK,EAAErB,WAAW,CAACqB,KAAK;IACxBlB,QAAQ;IACRE,QAAQ;IACR4B,oBAAoBA,CAAC,CAACzB,MAAM,EAAEG,UAAU,EAAED,SAAS,CAAC,EAAEwB,SAAS,EAAE;MAC/D,MAAMC,IAAI,GAAGlC,QAAQ,CAACO,MAAM,CAACS,GAAG,CAACT,MAAM,CAAC;MACxC2B,IAAI,CAACC,UAAU,GAAG,IAAI;MAEtB,IAAI1B,SAAS,EAAE;QACb,IAAIyB,IAAI,CAACE,IAAI,EAAE;UAAA,IAAAC,cAAA;UAEbJ,SAAS,IAAAI,cAAA,GAAGpC,aAAa,CAACgC,SAAS,EAAEC,IAAI,CAACE,IAAI,CAAC,YAAAC,cAAA,GAAIJ,SAAS;QAC9D;QACA,OAAOA,SAAS;MAClB;MAEA,IAAIK,SAAuB,GAAGC,WAAC,CAACC,UAAU,CAACN,IAAI,CAACO,IAAI,CAAC;MACrD,IAAIP,IAAI,CAACE,IAAI,EAAE;QAAA,IAAAM,eAAA;QACbJ,SAAS,IAAAI,eAAA,GAAGzC,aAAa,CAACqC,SAAS,EAAEJ,IAAI,CAACE,IAAI,CAAC,YAAAM,eAAA,GAAIJ,SAAS;MAC9D;MAEA,IAAI5B,UAAU,KAAK,SAAS,IAAIwB,IAAI,CAACS,OAAO,KAAK,cAAc,EAAE;QAC/D,OAAOL,SAAS;MAClB;MAEA,MAAMM,QAAQ,GAAG5C,QAAQ,CAAC6C,gBAAgB,CAACC,GAAG,CAACpC,UAAU,CAAC;MAE1D,OAAO6B,WAAC,CAACQ,gBAAgB,CACvBT,SAAS,EACTM,QAAQ,GAAGL,WAAC,CAACS,aAAa,CAACtC,UAAU,CAAC,GAAG6B,WAAC,CAACC,UAAU,CAAC9B,UAAU,CAAC,EACjEkC,QACF,CAAC;IACH;EACF,CAAC;EAED7C,WAAW,CAACsB,QAAQ,CAAC4B,wBAAwB,EAAEpB,6BAA6B,CAAC;AAC/E;AAKA,MAAMP,yBAAkE,GAAG;EACzE4B,KAAKA,CAAC3D,IAAI,EAAE;IACVA,IAAI,CAAC4D,IAAI,CAAC,CAAC;EACb,CAAC;EACDC,gBAAgBA,CAAC7D,IAAI,EAAE;IACrB,MAAM;MAAEc,eAAe;MAAED,QAAQ;MAAEJ;IAAS,CAAC,GAAG,IAAI;IAEpD,MAAM;MAAEqD;IAAG,CAAC,GAAG9D,IAAI,CAAC+D,IAAI;IACxB,IAAI,CAACD,EAAE,EAAE,MAAM,IAAIE,KAAK,CAAC,+BAA+B,CAAC;IACzD,MAAM9C,SAAS,GAAG4C,EAAE,CAACZ,IAAI;IAEzB,MAAMe,WAAW,GAAGpD,QAAQ,CAACY,GAAG,CAACP,SAAS,CAAC,IAAI,EAAE;IACjD,IAAI+C,WAAW,CAACC,MAAM,GAAG,CAAC,EAAE;MAC1B,MAAMC,SAAS,GAAGnB,WAAC,CAACoB,mBAAmB,CAErCC,sCAAsC,CACpC5D,QAAQ,EACRwD,WAAW,EACXjB,WAAC,CAACC,UAAU,CAAC/B,SAAS,CAAC,EACvBlB,IAAI,CAAC6B,KACP,CACF,CAAC;MAEDsC,SAAS,CAACG,WAAW,GAAGtE,IAAI,CAAC+D,IAAI,CAACO,WAAW;MAE7CxD,eAAe,CAACd,IAAI,CAACuE,WAAW,CAACJ,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;IACjD;EACF,CAAC;EACDK,mBAAmBA,CAACxE,IAAI,EAAE;IACxB,MAAM;MAAEc,eAAe;MAAED,QAAQ;MAAEJ;IAAS,CAAC,GAAG,IAAI;IAEpD,MAAMgE,KAAK,GAAGzE,IAAI,CAAC+D,IAAI,CAACW,IAAI,KAAK,KAAK;IAEtC,KAAK,MAAMC,IAAI,IAAI3E,IAAI,CAACyB,GAAG,CAAC,cAAc,CAAC,EAAE;MAC3C,MAAM;QAAEqC;MAAG,CAAC,GAAGa,IAAI,CAACZ,IAAI;MACxB,IAAI;QAAEa;MAAK,CAAC,GAAGD,IAAI,CAACZ,IAAI;MACxB,IACEf,WAAC,CAAC6B,YAAY,CAACf,EAAE,CAAC,IAClBjD,QAAQ,CAAC0C,GAAG,CAACO,EAAE,CAACZ,IAAI,CAAC,IACrB,CAACF,WAAC,CAAC8B,yBAAyB,CAACF,IAAI,CAAC,KACjC,CAAC5B,WAAC,CAAC+B,oBAAoB,CAACH,IAAI,CAAC,IAAIA,IAAI,CAACd,EAAE,CAAC,KACzC,CAACd,WAAC,CAACgC,iBAAiB,CAACJ,IAAI,CAAC,IAAIA,IAAI,CAACd,EAAE,CAAC,EACvC;QACA,IAAI,CAACc,IAAI,EAAE;UACT,IAAIH,KAAK,EAAE;YAIT;UACF,CAAC,MAAM;YACLG,IAAI,GAAG5E,IAAI,CAAC6B,KAAK,CAACoD,kBAAkB,CAAC,CAAC;UACxC;QACF;QAEAN,IAAI,CAACZ,IAAI,CAACa,IAAI,GAAGP,sCAAsC,CACrD5D,QAAQ,EACRI,QAAQ,CAACY,GAAG,CAACqC,EAAE,CAACZ,IAAI,CAAC,EACrB0B,IAAI,EACJ5E,IAAI,CAAC6B,KACP,CAAC;QACDf,eAAe,CAAC6D,IAAI,CAAClD,GAAG,CAAC,MAAM,CAAC,CAAC;MACnC,CAAC,MAAM;QACL,KAAK,MAAMP,SAAS,IAAIgE,MAAM,CAAC9C,IAAI,CACjCuC,IAAI,CAACQ,0BAA0B,CAAC,CAClC,CAAC,EAAE;UACD,IAAItE,QAAQ,CAAC0C,GAAG,CAACrC,SAAS,CAAC,EAAE;YAC3B,MAAMiD,SAAS,GAAGnB,WAAC,CAACoB,mBAAmB,CAErCC,sCAAsC,CACpC5D,QAAQ,EACRI,QAAQ,CAACY,GAAG,CAACP,SAAS,CAAC,EACvB8B,WAAC,CAACC,UAAU,CAAC/B,SAAS,CAAC,EACvBlB,IAAI,CAAC6B,KACP,CACF,CAAC;YAEDsC,SAAS,CAACG,WAAW,GAAGtE,IAAI,CAAC+D,IAAI,CAACO,WAAW;YAE7CxD,eAAe,CAACd,IAAI,CAACuE,WAAW,CAACJ,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;UACjD;QACF;MACF;IACF;EACF;AACF,CAAC;AAED,MAAME,sCAAsC,GAAGA,CAC7C5D,QAAwB,EACxBwD,WAAqB,EACrBmB,SAAuB,EACvBvD,KAAY,KACT;EACH,MAAMwD,iBAAiB,GAAG5E,QAAQ,CAAC6E,UAAU;EAC7C,KACE,IAAIC,YAAY,GAAG1D,KAAK,EACxB0D,YAAY,IAAI,IAAI,EACpBA,YAAY,GAAGA,YAAY,CAACtF,MAAM,EAClC;IACA,IAAIsF,YAAY,CAACC,aAAa,CAACH,iBAAiB,CAAC,EAAE;MACjDE,YAAY,CAACE,MAAM,CAACJ,iBAAiB,CAAC;IACxC;EACF;EACA,OAAO,CAACpB,WAAW,IAAI,EAAE,EAAEyB,MAAM,CAAC,CAACC,IAAI,EAAEL,UAAU,KAAK;IAItD,MAAM;MAAEhC;IAAiB,CAAC,GAAG7C,QAAQ;IACrC,MAAM4C,QAAQ,GAAGC,gBAAgB,CAACC,GAAG,CAAC+B,UAAU,CAAC;IACjD,OAAOtC,WAAC,CAAC4C,oBAAoB,CAC3B,GAAG,EACH5C,WAAC,CAACQ,gBAAgB,CAChBR,WAAC,CAACC,UAAU,CAACoC,iBAAiB,CAAC,EAC/BhC,QAAQ,GAAGL,WAAC,CAACS,aAAa,CAAC6B,UAAU,CAAC,GAAGtC,WAAC,CAACC,UAAU,CAACqC,UAAU,CAAC,EAClDjC,QACjB,CAAC,EACDsC,IACF,CAAC;EACH,CAAC,EAAEP,SAAS,CAAC;AACf,CAAC;AAED,MAAMS,gBAAgB,GAAI3E,SAAiB,IAAK;EAC9C,OAAO4E,cAAQ,CAACC,UAAU,CAACC,GAAG;AAChC;AACA,+BAA+B9E,SAAS;AACxC;AACA,GAAG;AACH,CAAC;AAED,MAAMwC,wBAAgE,GAAG;EACvEuC,oBAAoBA,CAACjG,IAAI,EAAE;IACzB,MAAM;MAAEuC,IAAI;MAAEE,oBAAoB;MAAEZ,KAAK;MAAElB,QAAQ;MAAEG;IAAgB,CAAC,GACpE,IAAI;IACN,IAAIyB,IAAI,CAACgB,GAAG,CAACvD,IAAI,CAAC+D,IAAI,CAAC,EAAE;IACzBxB,IAAI,CAAC2D,GAAG,CAAClG,IAAI,CAAC+D,IAAI,CAAC;IAEnB,MAAM7C,SAAS,GAAGlB,IAAI,CAAC+D,IAAI,CAACb,IAAI;IAEhC,MAAMiD,UAAU,GAAGxF,QAAQ,CAACc,GAAG,CAACP,SAAS,CAAC;IAC1C,IAAIiF,UAAU,EAAE;MACd,IAAIpG,QAAQ,CAACC,IAAI,CAAC,EAAE;QAClB,MAAMA,IAAI,CAACoG,mBAAmB,CAC5B,0CAA0ClF,SAAS,+CAA+C,GAChG,qFACJ,CAAC;MACH;MAEA,MAAMmF,YAAY,GAAGrG,IAAI,CAAC6B,KAAK,CAACyE,UAAU,CAACpF,SAAS,CAAC;MACrD,MAAMqF,WAAW,GAAG1E,KAAK,CAACyE,UAAU,CAACpF,SAAS,CAAC;MAG/C,IAAIqF,WAAW,KAAKF,YAAY,EAAE;MAElC,MAAMG,GAAG,GAAG/D,oBAAoB,CAAC0D,UAAU,EAAEnG,IAAI,CAAC+D,IAAI,CAAC;MAGvDyC,GAAG,CAACC,GAAG,GAAGzG,IAAI,CAAC+D,IAAI,CAAC0C,GAAG;MAEvB,IACE,CAACzG,IAAI,CAACG,UAAU,CAACuG,gBAAgB,CAAC;QAAEC,MAAM,EAAE3G,IAAI,CAAC+D;MAAK,CAAC,CAAC,IACtD/D,IAAI,CAACG,UAAU,CAACyG,wBAAwB,CAAC;QAAED,MAAM,EAAE3G,IAAI,CAAC+D;MAAK,CAAC,CAAC,IAC/D/D,IAAI,CAACG,UAAU,CAAC0G,0BAA0B,CAAC;QAAEC,GAAG,EAAE9G,IAAI,CAAC+D;MAAK,CAAC,CAAC,KAChEf,WAAC,CAAC+D,kBAAkB,CAACP,GAAG,CAAC,EACzB;QACAxG,IAAI,CAACgH,WAAW,CAAChE,WAAC,CAACiE,kBAAkB,CAAC,CAACjE,WAAC,CAACkE,cAAc,CAAC,CAAC,CAAC,EAAEV,GAAG,CAAC,CAAC,CAAC;MACpE,CAAC,MAAM,IAAIxG,IAAI,CAACmH,eAAe,CAAC,CAAC,IAAInE,WAAC,CAAC+D,kBAAkB,CAACP,GAAG,CAAC,EAAE;QAC9D,MAAM;UAAEY,MAAM;UAAEC;QAAS,CAAC,GAAGb,GAAG;QAChCxG,IAAI,CAACgH,WAAW,CACdhE,WAAC,CAACsE,mBAAmB,CAEnBtE,WAAC,CAACuE,aAAa,CAACH,MAAM,CAAClE,IAAI,CAAC,EAE5BF,WAAC,CAACuE,aAAa,CAACF,QAAQ,CAACnE,IAAI,CAC/B,CACF,CAAC;MACH,CAAC,MAAM;QACLlD,IAAI,CAACgH,WAAW,CAACR,GAAG,CAAC;MACvB;MAEA1F,eAAe,CAACd,IAAI,CAAC;MAIrBA,IAAI,CAAC4D,IAAI,CAAC,CAAC;IACb;EACF,CAAC;EAED4D,gBAAgBA,CAACxH,IAAI,EAAE;IACrB,MAAM;MACJ6B,KAAK;MACLU,IAAI;MACJ5B,QAAQ;MACRE,QAAQ;MACRC,eAAe;MACf2B;IACF,CAAC,GAAG,IAAI;IAER,IAAIF,IAAI,CAACgB,GAAG,CAACvD,IAAI,CAAC+D,IAAI,CAAC,EAAE;IAEzBxB,IAAI,CAAC2D,GAAG,CAAClG,IAAI,CAAC+D,IAAI,CAAC;IAEnB,MAAM0D,GAAG,GAAGzH,IAAI,CAACyB,GAAG,CAAC,UAAU,CAAC;IAGhC,IAAIgG,GAAG,CAACV,kBAAkB,CAAC,CAAC,EAAE;IAE9B,MAAMW,MAAM,GAAG1H,IAAI,CAAC+D,IAAI;IAExB,IAAI0D,GAAG,CAAC5C,YAAY,CAAC,CAAC,EAAE;MACtB,MAAM3D,SAAS,GAAGuG,GAAG,CAAC1D,IAAI,CAACb,IAAI;MAG/B,IAAIrB,KAAK,CAACyE,UAAU,CAACpF,SAAS,CAAC,KAAKlB,IAAI,CAAC6B,KAAK,CAACyE,UAAU,CAACpF,SAAS,CAAC,EAAE;QACpE;MACF;MAEA,MAAMyG,aAAa,GAAG9G,QAAQ,CAACY,GAAG,CAACP,SAAS,CAAC;MAC7C,MAAMiF,UAAU,GAAGxF,QAAQ,CAACc,GAAG,CAACP,SAAS,CAAC;MAE1C,IAAI,CAAAyG,aAAa,oBAAbA,aAAa,CAAEzD,MAAM,IAAG,CAAC,IAAIiC,UAAU,EAAE;QAC3C,IAAIA,UAAU,EAAE;UACdnG,IAAI,CAACgH,WAAW,CACdhE,WAAC,CAAC4C,oBAAoB,CAEnB8B,MAAM,CAACE,QAAQ,CAAC,CAAC,CAAC,GAAG,GAAG,EACzBnF,oBAAoB,CAAC0D,UAAU,EAAEsB,GAAG,CAAC1D,IAAI,CAAC,EAC1C8B,gBAAgB,CAAC3E,SAAS,CAC5B,CACF,CAAC;QACH,CAAC,MAAM,IAAIwG,MAAM,CAACG,MAAM,EAAE;UAGxB7H,IAAI,CAACgH,WAAW,CACd3C,sCAAsC,CACpC,IAAI,CAAC5D,QAAQ,EACbkH,aAAa,EACb3E,WAAC,CAAC8E,SAAS,CAACJ,MAAM,CAAC,EACnB1H,IAAI,CAAC6B,KACP,CACF,CAAC;QACH,CAAC,MAAM;UAGL,MAAM2E,GAAG,GAAG3E,KAAK,CAACkG,6BAA6B,CAAC7G,SAAS,CAAC;UAE1DlB,IAAI,CAACgH,WAAW,CACdhE,WAAC,CAACiE,kBAAkB,CAAC,CACnBjE,WAAC,CAAC4C,oBAAoB,CACpB,GAAG,EACH5C,WAAC,CAAC8E,SAAS,CAACtB,GAAG,CAAC,EAChBxD,WAAC,CAAC8E,SAAS,CAACJ,MAAM,CACpB,CAAC,EACDrD,sCAAsC,CACpC,IAAI,CAAC5D,QAAQ,EACbkH,aAAa,EACb3E,WAAC,CAACC,UAAU,CAAC/B,SAAS,CAAC,EACvBlB,IAAI,CAAC6B,KACP,CAAC,EACDmB,WAAC,CAAC8E,SAAS,CAACtB,GAAG,CAAC,CACjB,CACH,CAAC;QACH;MACF;IACF;IAEA1F,eAAe,CAACd,IAAI,CAAC;IACrBA,IAAI,CAAC4D,IAAI,CAAC,CAAC;EACb,CAAC;EAEDoE,oBAAoB,EAAE;IACpBC,IAAIA,CAACjI,IAAI,EAAE;MACT,MAAM;QACJ6B,KAAK;QACLU,IAAI;QACJ5B,QAAQ;QACRE,QAAQ;QACRC,eAAe;QACf2B;MACF,CAAC,GAAG,IAAI;MAER,IAAIF,IAAI,CAACgB,GAAG,CAACvD,IAAI,CAAC+D,IAAI,CAAC,EAAE;MACzBxB,IAAI,CAAC2D,GAAG,CAAClG,IAAI,CAAC+D,IAAI,CAAC;MAEnB,MAAMmE,IAAI,GAAGlI,IAAI,CAACyB,GAAG,CAAC,MAAM,CAAC;MAG7B,IAAIyG,IAAI,CAACnB,kBAAkB,CAAC,CAAC,EAAE;MAE/B,IAAImB,IAAI,CAACrD,YAAY,CAAC,CAAC,EAAE;QAGvB,MAAM3D,SAAS,GAAGgH,IAAI,CAACnE,IAAI,CAACb,IAAI;QAGhC,IAAIrB,KAAK,CAACyE,UAAU,CAACpF,SAAS,CAAC,KAAKlB,IAAI,CAAC6B,KAAK,CAACyE,UAAU,CAACpF,SAAS,CAAC,EAAE;UACpE;QACF;QAEA,MAAMyG,aAAa,GAAG9G,QAAQ,CAACY,GAAG,CAACP,SAAS,CAAC;QAC7C,MAAMiF,UAAU,GAAGxF,QAAQ,CAACc,GAAG,CAACP,SAAS,CAAC;QAC1C,IAAI,CAAAyG,aAAa,oBAAbA,aAAa,CAAEzD,MAAM,IAAG,CAAC,IAAIiC,UAAU,EAAE;UAC3CgC,OAAM,CAACnI,IAAI,CAAC+D,IAAI,CAAC6D,QAAQ,KAAK,GAAG,EAAE,yBAAyB,CAAC;UAE7D,MAAMQ,UAAU,GAAGpI,IAAI,CAAC+D,IAAI;UAE5B,IAAIoC,UAAU,EAAE;YACdiC,UAAU,CAACF,IAAI,GAAGzF,oBAAoB,CAAC0D,UAAU,EAAE+B,IAAI,CAACnE,IAAI,CAAC;YAE7DqE,UAAU,CAACC,KAAK,GAAGrF,WAAC,CAACiE,kBAAkB,CAAC,CACtCmB,UAAU,CAACC,KAAK,EAChBxC,gBAAgB,CAAC3E,SAAS,CAAC,CAC5B,CAAC;UACJ;UAEAlB,IAAI,CAACgH,WAAW,CACd3C,sCAAsC,CACpC,IAAI,CAAC5D,QAAQ,EACbkH,aAAa,EACbS,UAAU,EACVpI,IAAI,CAAC6B,KACP,CACF,CAAC;UACDf,eAAe,CAACd,IAAI,CAAC;QACvB;MACF,CAAC,MAAM;QACL,MAAMsI,GAAG,GAAGJ,IAAI,CAAC/C,0BAA0B,CAAC,CAAC;QAC7C,MAAMoD,eAAe,GAAGrD,MAAM,CAAC9C,IAAI,CAACkG,GAAG,CAAC,CAACE,MAAM,CAC7CtH,SAAS,IACPW,KAAK,CAACyE,UAAU,CAACpF,SAAS,CAAC,KAAKlB,IAAI,CAAC6B,KAAK,CAACyE,UAAU,CAACpF,SAAS,CACnE,CAAC;QACD,MAAM4C,EAAE,GAAGyE,eAAe,CAACE,IAAI,CAACvH,SAAS,IAAIP,QAAQ,CAAC4C,GAAG,CAACrC,SAAS,CAAC,CAAC;QAErE,IAAI4C,EAAE,EAAE;UACN9D,IAAI,CAAC+D,IAAI,CAACsE,KAAK,GAAGrF,WAAC,CAACiE,kBAAkB,CAAC,CACrCjH,IAAI,CAAC+D,IAAI,CAACsE,KAAK,EACfxC,gBAAgB,CAAC/B,EAAE,CAAC,CACrB,CAAC;QACJ;QAIA,MAAM4E,KAAqB,GAAG,EAAE;QAChCH,eAAe,CAACI,OAAO,CAACzH,SAAS,IAAI;UACnC,MAAMyG,aAAa,GAAG9G,QAAQ,CAACY,GAAG,CAACP,SAAS,CAAC,IAAI,EAAE;UACnD,IAAIyG,aAAa,CAACzD,MAAM,GAAG,CAAC,EAAE;YAC5BwE,KAAK,CAAChH,IAAI,CACR2C,sCAAsC,CACpC,IAAI,CAAC5D,QAAQ,EACbkH,aAAa,EACb3E,WAAC,CAACC,UAAU,CAAC/B,SAAS,CAAC,EACvBlB,IAAI,CAAC6B,KACP,CACF,CAAC;UACH;QACF,CAAC,CAAC;QAEF,IAAI6G,KAAK,CAACxE,MAAM,GAAG,CAAC,EAAE;UACpB,IAAIH,IAAY,GAAGf,WAAC,CAACiE,kBAAkB,CAACyB,KAAK,CAAC;UAC9C,IAAI1I,IAAI,CAACG,UAAU,CAACyI,qBAAqB,CAAC,CAAC,EAAE;YAC3C7E,IAAI,GAAGf,WAAC,CAACoB,mBAAmB,CAACL,IAAI,CAAC;YAElCA,IAAI,CAACO,WAAW,GAAGtE,IAAI,CAACG,UAAU,CAAC4D,IAAI,CAACO,WAAW;UACrD;UAEA,MAAMH,SAAS,GAAGnE,IAAI,CAACuE,WAAW,CAACR,IAAI,CAAC,CAAC,CAAC,CAAC;UAC3CjD,eAAe,CAACqD,SAAS,CAAC;QAC5B;MACF;IACF;EACF,CAAC;EACD,+BAA+B0E,CAC7B7I,IAAmD,EACnD;IACA,MAAM;MAAE6B,KAAK;MAAEkC;IAAK,CAAC,GAAG/D,IAAI;IAC5B,MAAM;MAAEkI;IAAK,CAAC,GAAGnE,IAAI;IACrB,MAAM;MAAElD,QAAQ;MAAEF,QAAQ;MAAEkB,KAAK,EAAEiH;IAAa,CAAC,GAAG,IAAI;IAExD,IAAI,CAAC9F,WAAC,CAAC+F,qBAAqB,CAACb,IAAI,CAAC,EAAE;MAClC,IAAIc,kBAAkB,GAAG,KAAK;QAC5BC,wBAAwB;MAC1B,MAAMC,aAAa,GAAGlJ,IAAI,CAACyB,GAAG,CAAC,MAAM,CAAC,CAACI,KAAK;MAC5C,KAAK,MAAMqB,IAAI,IAAIgC,MAAM,CAAC9C,IAAI,CAACY,WAAC,CAACmC,0BAA0B,CAAC+C,IAAI,CAAC,CAAC,EAAE;QAClE,IAAIY,YAAY,CAACxC,UAAU,CAACpD,IAAI,CAAC,KAAKrB,KAAK,CAACyE,UAAU,CAACpD,IAAI,CAAC,EAAE;UAC5D,IAAIrC,QAAQ,CAAC0C,GAAG,CAACL,IAAI,CAAC,EAAE;YACtB8F,kBAAkB,GAAG,IAAI;YACzB,IAAIE,aAAa,CAAC1D,aAAa,CAACtC,IAAI,CAAC,EAAE;cACrCgG,aAAa,CAACzD,MAAM,CAACvC,IAAI,CAAC;YAC5B;UACF;UACA,IAAIvC,QAAQ,CAAC4C,GAAG,CAACL,IAAI,CAAC,IAAI,CAAC+F,wBAAwB,EAAE;YACnDA,wBAAwB,GAAG/F,IAAI;UACjC;QACF;MACF;MACA,IAAI,CAAC8F,kBAAkB,IAAI,CAACC,wBAAwB,EAAE;QACpD;MACF;MAEAjJ,IAAI,CAACmJ,WAAW,CAAC,CAAC;MAClB,MAAMC,QAAQ,GAAGpJ,IAAI,CAACyB,GAAG,CAAC,MAAM,CAA+B;MAE/D,MAAM4H,SAAS,GAAGxH,KAAK,CAACyH,gCAAgC,CAACpB,IAAI,CAAC;MAC9DlI,IAAI,CACDyB,GAAG,CAAC,MAAM,CAAC,CACXuF,WAAW,CACVhE,WAAC,CAACuG,mBAAmB,CAAC,KAAK,EAAE,CAC3BvG,WAAC,CAACwG,kBAAkB,CAACxG,WAAC,CAAC8E,SAAS,CAACuB,SAAS,CAAC,CAAC,CAC7C,CACH,CAAC;MACHxH,KAAK,CAAC4H,mBAAmB,CAACzJ,IAAI,CAACyB,GAAG,CAAC,MAAM,CAAC,CAAC;MAE3C,IAAIuH,kBAAkB,EAAE;QACtBI,QAAQ,CAACM,gBAAgB,CACvB,MAAM,EACN1G,WAAC,CAACoB,mBAAmB,CAACpB,WAAC,CAAC4C,oBAAoB,CAAC,GAAG,EAAEsC,IAAI,EAAEmB,SAAS,CAAC,CACpE,CAAC;MACH;MACA,IAAIJ,wBAAwB,EAAE;QAC5BG,QAAQ,CAACM,gBAAgB,CACvB,MAAM,EACN1G,WAAC,CAACoB,mBAAmB,CAACyB,gBAAgB,CAACoD,wBAAwB,CAAC,CAClE,CAAC;MACH;IACF;EACF;AACF,CAAC", "ignoreList": []}