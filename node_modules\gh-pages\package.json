{"name": "gh-pages", "version": "6.3.0", "description": "Publish to a gh-pages branch on GitHub (or any other branch on any other remote)", "keywords": ["git", "gh-pages", "github"], "author": {"name": "<PERSON>", "url": "http://tschaub.net/"}, "license": "MIT", "homepage": "https://github.com/tschaub/gh-pages", "repository": {"type": "git", "url": "git://github.com/tschaub/gh-pages.git"}, "bugs": {"url": "https://github.com/tschaub/gh-pages/issues"}, "main": "lib/index.js", "scripts": {"lint": "eslint lib test bin plugin.js", "pretest": "npm run lint", "test": "mocha --recursive test"}, "files": ["lib", "bin"], "engines": {"node": ">=10"}, "dependencies": {"async": "^3.2.4", "commander": "^13.0.0", "email-addresses": "^5.0.0", "filenamify": "^4.3.0", "find-cache-dir": "^3.3.1", "fs-extra": "^11.1.1", "globby": "^11.1.0"}, "devDependencies": {"chai": "^4.3.7", "dir-compare": "^5.0.0", "eslint": "^9.16.0", "eslint-config-tschaub": "^15.2.0", "mocha": "^11.0.1", "sinon": "^19.0.2", "tmp": "^0.2.1"}, "bin": {"gh-pages": "bin/gh-pages.js", "gh-pages-clean": "bin/gh-pages-clean.js"}}