var ky=Object.defineProperty;var Sy=(e,t,n)=>t in e?ky(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n;var j=(e,t,n)=>Sy(e,typeof t!="symbol"?t+"":t,n);function by(e,t){for(var n=0;n<t.length;n++){const r=t[n];if(typeof r!="string"&&!Array.isArray(r)){for(const s in r)if(s!=="default"&&!(s in e)){const o=Object.getOwnPropertyDescriptor(r,s);o&&Object.defineProperty(e,s,o.get?o:{enumerable:!0,get:()=>r[s]})}}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}))}(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const s of document.querySelectorAll('link[rel="modulepreload"]'))r(s);new MutationObserver(s=>{for(const o of s)if(o.type==="childList")for(const i of o.addedNodes)i.tagName==="LINK"&&i.rel==="modulepreload"&&r(i)}).observe(document,{childList:!0,subtree:!0});function n(s){const o={};return s.integrity&&(o.integrity=s.integrity),s.referrerPolicy&&(o.referrerPolicy=s.referrerPolicy),s.crossOrigin==="use-credentials"?o.credentials="include":s.crossOrigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function r(s){if(s.ep)return;s.ep=!0;const o=n(s);fetch(s.href,o)}})();var mt=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function Lh(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}function Dy(e){if(e.__esModule)return e;var t=e.default;if(typeof t=="function"){var n=function r(){return this instanceof r?Reflect.construct(t,arguments,this.constructor):t.apply(this,arguments)};n.prototype=t.prototype}else n={};return Object.defineProperty(n,"__esModule",{value:!0}),Object.keys(e).forEach(function(r){var s=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(n,r,s.get?s:{enumerable:!0,get:function(){return e[r]}})}),n}var Ah={exports:{}},va={},$h={exports:{}},B={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var So=Symbol.for("react.element"),Cy=Symbol.for("react.portal"),Ey=Symbol.for("react.fragment"),Py=Symbol.for("react.strict_mode"),Ny=Symbol.for("react.profiler"),Ty=Symbol.for("react.provider"),Oy=Symbol.for("react.context"),My=Symbol.for("react.forward_ref"),jy=Symbol.for("react.suspense"),Ry=Symbol.for("react.memo"),Iy=Symbol.for("react.lazy"),_d=Symbol.iterator;function Ly(e){return e===null||typeof e!="object"?null:(e=_d&&e[_d]||e["@@iterator"],typeof e=="function"?e:null)}var Fh={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},Yh=Object.assign,Uh={};function ds(e,t,n){this.props=e,this.context=t,this.refs=Uh,this.updater=n||Fh}ds.prototype.isReactComponent={};ds.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};ds.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function Wh(){}Wh.prototype=ds.prototype;function sc(e,t,n){this.props=e,this.context=t,this.refs=Uh,this.updater=n||Fh}var oc=sc.prototype=new Wh;oc.constructor=sc;Yh(oc,ds.prototype);oc.isPureReactComponent=!0;var xd=Array.isArray,Bh=Object.prototype.hasOwnProperty,ic={current:null},zh={key:!0,ref:!0,__self:!0,__source:!0};function Hh(e,t,n){var r,s={},o=null,i=null;if(t!=null)for(r in t.ref!==void 0&&(i=t.ref),t.key!==void 0&&(o=""+t.key),t)Bh.call(t,r)&&!zh.hasOwnProperty(r)&&(s[r]=t[r]);var a=arguments.length-2;if(a===1)s.children=n;else if(1<a){for(var l=Array(a),u=0;u<a;u++)l[u]=arguments[u+2];s.children=l}if(e&&e.defaultProps)for(r in a=e.defaultProps,a)s[r]===void 0&&(s[r]=a[r]);return{$$typeof:So,type:e,key:o,ref:i,props:s,_owner:ic.current}}function Ay(e,t){return{$$typeof:So,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function ac(e){return typeof e=="object"&&e!==null&&e.$$typeof===So}function $y(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var kd=/\/+/g;function tl(e,t){return typeof e=="object"&&e!==null&&e.key!=null?$y(""+e.key):t.toString(36)}function ci(e,t,n,r,s){var o=typeof e;(o==="undefined"||o==="boolean")&&(e=null);var i=!1;if(e===null)i=!0;else switch(o){case"string":case"number":i=!0;break;case"object":switch(e.$$typeof){case So:case Cy:i=!0}}if(i)return i=e,s=s(i),e=r===""?"."+tl(i,0):r,xd(s)?(n="",e!=null&&(n=e.replace(kd,"$&/")+"/"),ci(s,t,n,"",function(u){return u})):s!=null&&(ac(s)&&(s=Ay(s,n+(!s.key||i&&i.key===s.key?"":(""+s.key).replace(kd,"$&/")+"/")+e)),t.push(s)),1;if(i=0,r=r===""?".":r+":",xd(e))for(var a=0;a<e.length;a++){o=e[a];var l=r+tl(o,a);i+=ci(o,t,n,l,s)}else if(l=Ly(e),typeof l=="function")for(e=l.call(e),a=0;!(o=e.next()).done;)o=o.value,l=r+tl(o,a++),i+=ci(o,t,n,l,s);else if(o==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return i}function Lo(e,t,n){if(e==null)return e;var r=[],s=0;return ci(e,r,"","",function(o){return t.call(n,o,s++)}),r}function Fy(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var ze={current:null},di={transition:null},Yy={ReactCurrentDispatcher:ze,ReactCurrentBatchConfig:di,ReactCurrentOwner:ic};function Vh(){throw Error("act(...) is not supported in production builds of React.")}B.Children={map:Lo,forEach:function(e,t,n){Lo(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return Lo(e,function(){t++}),t},toArray:function(e){return Lo(e,function(t){return t})||[]},only:function(e){if(!ac(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};B.Component=ds;B.Fragment=Ey;B.Profiler=Ny;B.PureComponent=sc;B.StrictMode=Py;B.Suspense=jy;B.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Yy;B.act=Vh;B.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=Yh({},e.props),s=e.key,o=e.ref,i=e._owner;if(t!=null){if(t.ref!==void 0&&(o=t.ref,i=ic.current),t.key!==void 0&&(s=""+t.key),e.type&&e.type.defaultProps)var a=e.type.defaultProps;for(l in t)Bh.call(t,l)&&!zh.hasOwnProperty(l)&&(r[l]=t[l]===void 0&&a!==void 0?a[l]:t[l])}var l=arguments.length-2;if(l===1)r.children=n;else if(1<l){a=Array(l);for(var u=0;u<l;u++)a[u]=arguments[u+2];r.children=a}return{$$typeof:So,type:e.type,key:s,ref:o,props:r,_owner:i}};B.createContext=function(e){return e={$$typeof:Oy,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:Ty,_context:e},e.Consumer=e};B.createElement=Hh;B.createFactory=function(e){var t=Hh.bind(null,e);return t.type=e,t};B.createRef=function(){return{current:null}};B.forwardRef=function(e){return{$$typeof:My,render:e}};B.isValidElement=ac;B.lazy=function(e){return{$$typeof:Iy,_payload:{_status:-1,_result:e},_init:Fy}};B.memo=function(e,t){return{$$typeof:Ry,type:e,compare:t===void 0?null:t}};B.startTransition=function(e){var t=di.transition;di.transition={};try{e()}finally{di.transition=t}};B.unstable_act=Vh;B.useCallback=function(e,t){return ze.current.useCallback(e,t)};B.useContext=function(e){return ze.current.useContext(e)};B.useDebugValue=function(){};B.useDeferredValue=function(e){return ze.current.useDeferredValue(e)};B.useEffect=function(e,t){return ze.current.useEffect(e,t)};B.useId=function(){return ze.current.useId()};B.useImperativeHandle=function(e,t,n){return ze.current.useImperativeHandle(e,t,n)};B.useInsertionEffect=function(e,t){return ze.current.useInsertionEffect(e,t)};B.useLayoutEffect=function(e,t){return ze.current.useLayoutEffect(e,t)};B.useMemo=function(e,t){return ze.current.useMemo(e,t)};B.useReducer=function(e,t,n){return ze.current.useReducer(e,t,n)};B.useRef=function(e){return ze.current.useRef(e)};B.useState=function(e){return ze.current.useState(e)};B.useSyncExternalStore=function(e,t,n){return ze.current.useSyncExternalStore(e,t,n)};B.useTransition=function(){return ze.current.useTransition()};B.version="18.3.1";$h.exports=B;var S=$h.exports;const C=Lh(S),Qh=by({__proto__:null,default:C},[S]);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Uy=S,Wy=Symbol.for("react.element"),By=Symbol.for("react.fragment"),zy=Object.prototype.hasOwnProperty,Hy=Uy.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,Vy={key:!0,ref:!0,__self:!0,__source:!0};function Kh(e,t,n){var r,s={},o=null,i=null;n!==void 0&&(o=""+n),t.key!==void 0&&(o=""+t.key),t.ref!==void 0&&(i=t.ref);for(r in t)zy.call(t,r)&&!Vy.hasOwnProperty(r)&&(s[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)s[r]===void 0&&(s[r]=t[r]);return{$$typeof:Wy,type:e,key:o,ref:i,props:s,_owner:Hy.current}}va.Fragment=By;va.jsx=Kh;va.jsxs=Kh;Ah.exports=va;var h=Ah.exports,qh={exports:{}},lt={},Gh={exports:{}},Jh={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(N,L){var $=N.length;N.push(L);e:for(;0<$;){var le=$-1>>>1,we=N[le];if(0<s(we,L))N[le]=L,N[$]=we,$=le;else break e}}function n(N){return N.length===0?null:N[0]}function r(N){if(N.length===0)return null;var L=N[0],$=N.pop();if($!==L){N[0]=$;e:for(var le=0,we=N.length,xr=we>>>1;le<xr;){var zn=2*(le+1)-1,el=N[zn],Hn=zn+1,Io=N[Hn];if(0>s(el,$))Hn<we&&0>s(Io,el)?(N[le]=Io,N[Hn]=$,le=Hn):(N[le]=el,N[zn]=$,le=zn);else if(Hn<we&&0>s(Io,$))N[le]=Io,N[Hn]=$,le=Hn;else break e}}return L}function s(N,L){var $=N.sortIndex-L.sortIndex;return $!==0?$:N.id-L.id}if(typeof performance=="object"&&typeof performance.now=="function"){var o=performance;e.unstable_now=function(){return o.now()}}else{var i=Date,a=i.now();e.unstable_now=function(){return i.now()-a}}var l=[],u=[],c=1,d=null,f=3,p=!1,y=!1,_=!1,x=typeof setTimeout=="function"?setTimeout:null,g=typeof clearTimeout=="function"?clearTimeout:null,m=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function v(N){for(var L=n(u);L!==null;){if(L.callback===null)r(u);else if(L.startTime<=N)r(u),L.sortIndex=L.expirationTime,t(l,L);else break;L=n(u)}}function k(N){if(_=!1,v(N),!y)if(n(l)!==null)y=!0,Y(E);else{var L=n(u);L!==null&&ie(k,L.startTime-N)}}function E(N,L){y=!1,_&&(_=!1,g(b),b=-1),p=!0;var $=f;try{for(v(L),d=n(l);d!==null&&(!(d.expirationTime>L)||N&&!F());){var le=d.callback;if(typeof le=="function"){d.callback=null,f=d.priorityLevel;var we=le(d.expirationTime<=L);L=e.unstable_now(),typeof we=="function"?d.callback=we:d===n(l)&&r(l),v(L)}else r(l);d=n(l)}if(d!==null)var xr=!0;else{var zn=n(u);zn!==null&&ie(k,zn.startTime-L),xr=!1}return xr}finally{d=null,f=$,p=!1}}var P=!1,D=null,b=-1,M=5,O=-1;function F(){return!(e.unstable_now()-O<M)}function H(){if(D!==null){var N=e.unstable_now();O=N;var L=!0;try{L=D(!0,N)}finally{L?ee():(P=!1,D=null)}}else P=!1}var ee;if(typeof m=="function")ee=function(){m(H)};else if(typeof MessageChannel<"u"){var A=new MessageChannel,V=A.port2;A.port1.onmessage=H,ee=function(){V.postMessage(null)}}else ee=function(){x(H,0)};function Y(N){D=N,P||(P=!0,ee())}function ie(N,L){b=x(function(){N(e.unstable_now())},L)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(N){N.callback=null},e.unstable_continueExecution=function(){y||p||(y=!0,Y(E))},e.unstable_forceFrameRate=function(N){0>N||125<N?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):M=0<N?Math.floor(1e3/N):5},e.unstable_getCurrentPriorityLevel=function(){return f},e.unstable_getFirstCallbackNode=function(){return n(l)},e.unstable_next=function(N){switch(f){case 1:case 2:case 3:var L=3;break;default:L=f}var $=f;f=L;try{return N()}finally{f=$}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(N,L){switch(N){case 1:case 2:case 3:case 4:case 5:break;default:N=3}var $=f;f=N;try{return L()}finally{f=$}},e.unstable_scheduleCallback=function(N,L,$){var le=e.unstable_now();switch(typeof $=="object"&&$!==null?($=$.delay,$=typeof $=="number"&&0<$?le+$:le):$=le,N){case 1:var we=-1;break;case 2:we=250;break;case 5:we=**********;break;case 4:we=1e4;break;default:we=5e3}return we=$+we,N={id:c++,callback:L,priorityLevel:N,startTime:$,expirationTime:we,sortIndex:-1},$>le?(N.sortIndex=$,t(u,N),n(l)===null&&N===n(u)&&(_?(g(b),b=-1):_=!0,ie(k,$-le))):(N.sortIndex=we,t(l,N),y||p||(y=!0,Y(E))),N},e.unstable_shouldYield=F,e.unstable_wrapCallback=function(N){var L=f;return function(){var $=f;f=L;try{return N.apply(this,arguments)}finally{f=$}}}})(Jh);Gh.exports=Jh;var Qy=Gh.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Ky=S,at=Qy;function T(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var Xh=new Set,qs={};function mr(e,t){qr(e,t),qr(e+"Capture",t)}function qr(e,t){for(qs[e]=t,e=0;e<t.length;e++)Xh.add(t[e])}var en=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),Wl=Object.prototype.hasOwnProperty,qy=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,Sd={},bd={};function Gy(e){return Wl.call(bd,e)?!0:Wl.call(Sd,e)?!1:qy.test(e)?bd[e]=!0:(Sd[e]=!0,!1)}function Jy(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function Xy(e,t,n,r){if(t===null||typeof t>"u"||Jy(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function He(e,t,n,r,s,o,i){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=s,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=o,this.removeEmptyString=i}var Re={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){Re[e]=new He(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];Re[t]=new He(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){Re[e]=new He(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){Re[e]=new He(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){Re[e]=new He(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){Re[e]=new He(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){Re[e]=new He(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){Re[e]=new He(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){Re[e]=new He(e,5,!1,e.toLowerCase(),null,!1,!1)});var lc=/[\-:]([a-z])/g;function uc(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(lc,uc);Re[t]=new He(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(lc,uc);Re[t]=new He(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(lc,uc);Re[t]=new He(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){Re[e]=new He(e,1,!1,e.toLowerCase(),null,!1,!1)});Re.xlinkHref=new He("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){Re[e]=new He(e,1,!1,e.toLowerCase(),null,!0,!0)});function cc(e,t,n,r){var s=Re.hasOwnProperty(t)?Re[t]:null;(s!==null?s.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(Xy(t,n,s,r)&&(n=null),r||s===null?Gy(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):s.mustUseProperty?e[s.propertyName]=n===null?s.type===3?!1:"":n:(t=s.attributeName,r=s.attributeNamespace,n===null?e.removeAttribute(t):(s=s.type,n=s===3||s===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var an=Ky.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,Ao=Symbol.for("react.element"),Pr=Symbol.for("react.portal"),Nr=Symbol.for("react.fragment"),dc=Symbol.for("react.strict_mode"),Bl=Symbol.for("react.profiler"),Zh=Symbol.for("react.provider"),ep=Symbol.for("react.context"),fc=Symbol.for("react.forward_ref"),zl=Symbol.for("react.suspense"),Hl=Symbol.for("react.suspense_list"),hc=Symbol.for("react.memo"),fn=Symbol.for("react.lazy"),tp=Symbol.for("react.offscreen"),Dd=Symbol.iterator;function vs(e){return e===null||typeof e!="object"?null:(e=Dd&&e[Dd]||e["@@iterator"],typeof e=="function"?e:null)}var fe=Object.assign,nl;function Os(e){if(nl===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);nl=t&&t[1]||""}return`
`+nl+e}var rl=!1;function sl(e,t){if(!e||rl)return"";rl=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(u){var r=u}Reflect.construct(e,[],t)}else{try{t.call()}catch(u){r=u}e.call(t.prototype)}else{try{throw Error()}catch(u){r=u}e()}}catch(u){if(u&&r&&typeof u.stack=="string"){for(var s=u.stack.split(`
`),o=r.stack.split(`
`),i=s.length-1,a=o.length-1;1<=i&&0<=a&&s[i]!==o[a];)a--;for(;1<=i&&0<=a;i--,a--)if(s[i]!==o[a]){if(i!==1||a!==1)do if(i--,a--,0>a||s[i]!==o[a]){var l=`
`+s[i].replace(" at new "," at ");return e.displayName&&l.includes("<anonymous>")&&(l=l.replace("<anonymous>",e.displayName)),l}while(1<=i&&0<=a);break}}}finally{rl=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?Os(e):""}function Zy(e){switch(e.tag){case 5:return Os(e.type);case 16:return Os("Lazy");case 13:return Os("Suspense");case 19:return Os("SuspenseList");case 0:case 2:case 15:return e=sl(e.type,!1),e;case 11:return e=sl(e.type.render,!1),e;case 1:return e=sl(e.type,!0),e;default:return""}}function Vl(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case Nr:return"Fragment";case Pr:return"Portal";case Bl:return"Profiler";case dc:return"StrictMode";case zl:return"Suspense";case Hl:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case ep:return(e.displayName||"Context")+".Consumer";case Zh:return(e._context.displayName||"Context")+".Provider";case fc:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case hc:return t=e.displayName||null,t!==null?t:Vl(e.type)||"Memo";case fn:t=e._payload,e=e._init;try{return Vl(e(t))}catch{}}return null}function ev(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return Vl(t);case 8:return t===dc?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function Rn(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function np(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function tv(e){var t=np(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var s=n.get,o=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return s.call(this)},set:function(i){r=""+i,o.call(this,i)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(i){r=""+i},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function $o(e){e._valueTracker||(e._valueTracker=tv(e))}function rp(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=np(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function Ti(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function Ql(e,t){var n=t.checked;return fe({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function Cd(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=Rn(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function sp(e,t){t=t.checked,t!=null&&cc(e,"checked",t,!1)}function Kl(e,t){sp(e,t);var n=Rn(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?ql(e,t.type,n):t.hasOwnProperty("defaultValue")&&ql(e,t.type,Rn(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function Ed(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function ql(e,t,n){(t!=="number"||Ti(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var Ms=Array.isArray;function Yr(e,t,n,r){if(e=e.options,t){t={};for(var s=0;s<n.length;s++)t["$"+n[s]]=!0;for(n=0;n<e.length;n++)s=t.hasOwnProperty("$"+e[n].value),e[n].selected!==s&&(e[n].selected=s),s&&r&&(e[n].defaultSelected=!0)}else{for(n=""+Rn(n),t=null,s=0;s<e.length;s++){if(e[s].value===n){e[s].selected=!0,r&&(e[s].defaultSelected=!0);return}t!==null||e[s].disabled||(t=e[s])}t!==null&&(t.selected=!0)}}function Gl(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(T(91));return fe({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function Pd(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(T(92));if(Ms(n)){if(1<n.length)throw Error(T(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:Rn(n)}}function op(e,t){var n=Rn(t.value),r=Rn(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function Nd(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function ip(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function Jl(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?ip(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var Fo,ap=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,s){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,s)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(Fo=Fo||document.createElement("div"),Fo.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=Fo.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function Gs(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var $s={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},nv=["Webkit","ms","Moz","O"];Object.keys($s).forEach(function(e){nv.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),$s[t]=$s[e]})});function lp(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||$s.hasOwnProperty(e)&&$s[e]?(""+t).trim():t+"px"}function up(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,s=lp(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,s):e[n]=s}}var rv=fe({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function Xl(e,t){if(t){if(rv[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(T(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(T(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(T(61))}if(t.style!=null&&typeof t.style!="object")throw Error(T(62))}}function Zl(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var eu=null;function pc(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var tu=null,Ur=null,Wr=null;function Td(e){if(e=Co(e)){if(typeof tu!="function")throw Error(T(280));var t=e.stateNode;t&&(t=Sa(t),tu(e.stateNode,e.type,t))}}function cp(e){Ur?Wr?Wr.push(e):Wr=[e]:Ur=e}function dp(){if(Ur){var e=Ur,t=Wr;if(Wr=Ur=null,Td(e),t)for(e=0;e<t.length;e++)Td(t[e])}}function fp(e,t){return e(t)}function hp(){}var ol=!1;function pp(e,t,n){if(ol)return e(t,n);ol=!0;try{return fp(e,t,n)}finally{ol=!1,(Ur!==null||Wr!==null)&&(hp(),dp())}}function Js(e,t){var n=e.stateNode;if(n===null)return null;var r=Sa(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(T(231,t,typeof n));return n}var nu=!1;if(en)try{var ws={};Object.defineProperty(ws,"passive",{get:function(){nu=!0}}),window.addEventListener("test",ws,ws),window.removeEventListener("test",ws,ws)}catch{nu=!1}function sv(e,t,n,r,s,o,i,a,l){var u=Array.prototype.slice.call(arguments,3);try{t.apply(n,u)}catch(c){this.onError(c)}}var Fs=!1,Oi=null,Mi=!1,ru=null,ov={onError:function(e){Fs=!0,Oi=e}};function iv(e,t,n,r,s,o,i,a,l){Fs=!1,Oi=null,sv.apply(ov,arguments)}function av(e,t,n,r,s,o,i,a,l){if(iv.apply(this,arguments),Fs){if(Fs){var u=Oi;Fs=!1,Oi=null}else throw Error(T(198));Mi||(Mi=!0,ru=u)}}function gr(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function mp(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function Od(e){if(gr(e)!==e)throw Error(T(188))}function lv(e){var t=e.alternate;if(!t){if(t=gr(e),t===null)throw Error(T(188));return t!==e?null:e}for(var n=e,r=t;;){var s=n.return;if(s===null)break;var o=s.alternate;if(o===null){if(r=s.return,r!==null){n=r;continue}break}if(s.child===o.child){for(o=s.child;o;){if(o===n)return Od(s),e;if(o===r)return Od(s),t;o=o.sibling}throw Error(T(188))}if(n.return!==r.return)n=s,r=o;else{for(var i=!1,a=s.child;a;){if(a===n){i=!0,n=s,r=o;break}if(a===r){i=!0,r=s,n=o;break}a=a.sibling}if(!i){for(a=o.child;a;){if(a===n){i=!0,n=o,r=s;break}if(a===r){i=!0,r=o,n=s;break}a=a.sibling}if(!i)throw Error(T(189))}}if(n.alternate!==r)throw Error(T(190))}if(n.tag!==3)throw Error(T(188));return n.stateNode.current===n?e:t}function gp(e){return e=lv(e),e!==null?yp(e):null}function yp(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=yp(e);if(t!==null)return t;e=e.sibling}return null}var vp=at.unstable_scheduleCallback,Md=at.unstable_cancelCallback,uv=at.unstable_shouldYield,cv=at.unstable_requestPaint,ge=at.unstable_now,dv=at.unstable_getCurrentPriorityLevel,mc=at.unstable_ImmediatePriority,wp=at.unstable_UserBlockingPriority,ji=at.unstable_NormalPriority,fv=at.unstable_LowPriority,_p=at.unstable_IdlePriority,wa=null,At=null;function hv(e){if(At&&typeof At.onCommitFiberRoot=="function")try{At.onCommitFiberRoot(wa,e,void 0,(e.current.flags&128)===128)}catch{}}var bt=Math.clz32?Math.clz32:gv,pv=Math.log,mv=Math.LN2;function gv(e){return e>>>=0,e===0?32:31-(pv(e)/mv|0)|0}var Yo=64,Uo=4194304;function js(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function Ri(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,s=e.suspendedLanes,o=e.pingedLanes,i=n&268435455;if(i!==0){var a=i&~s;a!==0?r=js(a):(o&=i,o!==0&&(r=js(o)))}else i=n&~s,i!==0?r=js(i):o!==0&&(r=js(o));if(r===0)return 0;if(t!==0&&t!==r&&!(t&s)&&(s=r&-r,o=t&-t,s>=o||s===16&&(o&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-bt(t),s=1<<n,r|=e[n],t&=~s;return r}function yv(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function vv(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,s=e.expirationTimes,o=e.pendingLanes;0<o;){var i=31-bt(o),a=1<<i,l=s[i];l===-1?(!(a&n)||a&r)&&(s[i]=yv(a,t)):l<=t&&(e.expiredLanes|=a),o&=~a}}function su(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function xp(){var e=Yo;return Yo<<=1,!(Yo&4194240)&&(Yo=64),e}function il(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function bo(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-bt(t),e[t]=n}function wv(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var s=31-bt(n),o=1<<s;t[s]=0,r[s]=-1,e[s]=-1,n&=~o}}function gc(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-bt(n),s=1<<r;s&t|e[r]&t&&(e[r]|=t),n&=~s}}var Z=0;function kp(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var Sp,yc,bp,Dp,Cp,ou=!1,Wo=[],Sn=null,bn=null,Dn=null,Xs=new Map,Zs=new Map,gn=[],_v="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function jd(e,t){switch(e){case"focusin":case"focusout":Sn=null;break;case"dragenter":case"dragleave":bn=null;break;case"mouseover":case"mouseout":Dn=null;break;case"pointerover":case"pointerout":Xs.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Zs.delete(t.pointerId)}}function _s(e,t,n,r,s,o){return e===null||e.nativeEvent!==o?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:o,targetContainers:[s]},t!==null&&(t=Co(t),t!==null&&yc(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,s!==null&&t.indexOf(s)===-1&&t.push(s),e)}function xv(e,t,n,r,s){switch(t){case"focusin":return Sn=_s(Sn,e,t,n,r,s),!0;case"dragenter":return bn=_s(bn,e,t,n,r,s),!0;case"mouseover":return Dn=_s(Dn,e,t,n,r,s),!0;case"pointerover":var o=s.pointerId;return Xs.set(o,_s(Xs.get(o)||null,e,t,n,r,s)),!0;case"gotpointercapture":return o=s.pointerId,Zs.set(o,_s(Zs.get(o)||null,e,t,n,r,s)),!0}return!1}function Ep(e){var t=Xn(e.target);if(t!==null){var n=gr(t);if(n!==null){if(t=n.tag,t===13){if(t=mp(n),t!==null){e.blockedOn=t,Cp(e.priority,function(){bp(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function fi(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=iu(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);eu=r,n.target.dispatchEvent(r),eu=null}else return t=Co(n),t!==null&&yc(t),e.blockedOn=n,!1;t.shift()}return!0}function Rd(e,t,n){fi(e)&&n.delete(t)}function kv(){ou=!1,Sn!==null&&fi(Sn)&&(Sn=null),bn!==null&&fi(bn)&&(bn=null),Dn!==null&&fi(Dn)&&(Dn=null),Xs.forEach(Rd),Zs.forEach(Rd)}function xs(e,t){e.blockedOn===t&&(e.blockedOn=null,ou||(ou=!0,at.unstable_scheduleCallback(at.unstable_NormalPriority,kv)))}function eo(e){function t(s){return xs(s,e)}if(0<Wo.length){xs(Wo[0],e);for(var n=1;n<Wo.length;n++){var r=Wo[n];r.blockedOn===e&&(r.blockedOn=null)}}for(Sn!==null&&xs(Sn,e),bn!==null&&xs(bn,e),Dn!==null&&xs(Dn,e),Xs.forEach(t),Zs.forEach(t),n=0;n<gn.length;n++)r=gn[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<gn.length&&(n=gn[0],n.blockedOn===null);)Ep(n),n.blockedOn===null&&gn.shift()}var Br=an.ReactCurrentBatchConfig,Ii=!0;function Sv(e,t,n,r){var s=Z,o=Br.transition;Br.transition=null;try{Z=1,vc(e,t,n,r)}finally{Z=s,Br.transition=o}}function bv(e,t,n,r){var s=Z,o=Br.transition;Br.transition=null;try{Z=4,vc(e,t,n,r)}finally{Z=s,Br.transition=o}}function vc(e,t,n,r){if(Ii){var s=iu(e,t,n,r);if(s===null)gl(e,t,r,Li,n),jd(e,r);else if(xv(s,e,t,n,r))r.stopPropagation();else if(jd(e,r),t&4&&-1<_v.indexOf(e)){for(;s!==null;){var o=Co(s);if(o!==null&&Sp(o),o=iu(e,t,n,r),o===null&&gl(e,t,r,Li,n),o===s)break;s=o}s!==null&&r.stopPropagation()}else gl(e,t,r,null,n)}}var Li=null;function iu(e,t,n,r){if(Li=null,e=pc(r),e=Xn(e),e!==null)if(t=gr(e),t===null)e=null;else if(n=t.tag,n===13){if(e=mp(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return Li=e,null}function Pp(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(dv()){case mc:return 1;case wp:return 4;case ji:case fv:return 16;case _p:return 536870912;default:return 16}default:return 16}}var wn=null,wc=null,hi=null;function Np(){if(hi)return hi;var e,t=wc,n=t.length,r,s="value"in wn?wn.value:wn.textContent,o=s.length;for(e=0;e<n&&t[e]===s[e];e++);var i=n-e;for(r=1;r<=i&&t[n-r]===s[o-r];r++);return hi=s.slice(e,1<r?1-r:void 0)}function pi(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Bo(){return!0}function Id(){return!1}function ut(e){function t(n,r,s,o,i){this._reactName=n,this._targetInst=s,this.type=r,this.nativeEvent=o,this.target=i,this.currentTarget=null;for(var a in e)e.hasOwnProperty(a)&&(n=e[a],this[a]=n?n(o):o[a]);return this.isDefaultPrevented=(o.defaultPrevented!=null?o.defaultPrevented:o.returnValue===!1)?Bo:Id,this.isPropagationStopped=Id,this}return fe(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=Bo)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=Bo)},persist:function(){},isPersistent:Bo}),t}var fs={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},_c=ut(fs),Do=fe({},fs,{view:0,detail:0}),Dv=ut(Do),al,ll,ks,_a=fe({},Do,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:xc,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==ks&&(ks&&e.type==="mousemove"?(al=e.screenX-ks.screenX,ll=e.screenY-ks.screenY):ll=al=0,ks=e),al)},movementY:function(e){return"movementY"in e?e.movementY:ll}}),Ld=ut(_a),Cv=fe({},_a,{dataTransfer:0}),Ev=ut(Cv),Pv=fe({},Do,{relatedTarget:0}),ul=ut(Pv),Nv=fe({},fs,{animationName:0,elapsedTime:0,pseudoElement:0}),Tv=ut(Nv),Ov=fe({},fs,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),Mv=ut(Ov),jv=fe({},fs,{data:0}),Ad=ut(jv),Rv={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Iv={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Lv={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Av(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=Lv[e])?!!t[e]:!1}function xc(){return Av}var $v=fe({},Do,{key:function(e){if(e.key){var t=Rv[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=pi(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?Iv[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:xc,charCode:function(e){return e.type==="keypress"?pi(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?pi(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),Fv=ut($v),Yv=fe({},_a,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),$d=ut(Yv),Uv=fe({},Do,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:xc}),Wv=ut(Uv),Bv=fe({},fs,{propertyName:0,elapsedTime:0,pseudoElement:0}),zv=ut(Bv),Hv=fe({},_a,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),Vv=ut(Hv),Qv=[9,13,27,32],kc=en&&"CompositionEvent"in window,Ys=null;en&&"documentMode"in document&&(Ys=document.documentMode);var Kv=en&&"TextEvent"in window&&!Ys,Tp=en&&(!kc||Ys&&8<Ys&&11>=Ys),Fd=" ",Yd=!1;function Op(e,t){switch(e){case"keyup":return Qv.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Mp(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var Tr=!1;function qv(e,t){switch(e){case"compositionend":return Mp(t);case"keypress":return t.which!==32?null:(Yd=!0,Fd);case"textInput":return e=t.data,e===Fd&&Yd?null:e;default:return null}}function Gv(e,t){if(Tr)return e==="compositionend"||!kc&&Op(e,t)?(e=Np(),hi=wc=wn=null,Tr=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Tp&&t.locale!=="ko"?null:t.data;default:return null}}var Jv={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Ud(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!Jv[e.type]:t==="textarea"}function jp(e,t,n,r){cp(r),t=Ai(t,"onChange"),0<t.length&&(n=new _c("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var Us=null,to=null;function Xv(e){zp(e,0)}function xa(e){var t=jr(e);if(rp(t))return e}function Zv(e,t){if(e==="change")return t}var Rp=!1;if(en){var cl;if(en){var dl="oninput"in document;if(!dl){var Wd=document.createElement("div");Wd.setAttribute("oninput","return;"),dl=typeof Wd.oninput=="function"}cl=dl}else cl=!1;Rp=cl&&(!document.documentMode||9<document.documentMode)}function Bd(){Us&&(Us.detachEvent("onpropertychange",Ip),to=Us=null)}function Ip(e){if(e.propertyName==="value"&&xa(to)){var t=[];jp(t,to,e,pc(e)),pp(Xv,t)}}function e0(e,t,n){e==="focusin"?(Bd(),Us=t,to=n,Us.attachEvent("onpropertychange",Ip)):e==="focusout"&&Bd()}function t0(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return xa(to)}function n0(e,t){if(e==="click")return xa(t)}function r0(e,t){if(e==="input"||e==="change")return xa(t)}function s0(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var Ct=typeof Object.is=="function"?Object.is:s0;function no(e,t){if(Ct(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var s=n[r];if(!Wl.call(t,s)||!Ct(e[s],t[s]))return!1}return!0}function zd(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Hd(e,t){var n=zd(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=zd(n)}}function Lp(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?Lp(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function Ap(){for(var e=window,t=Ti();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=Ti(e.document)}return t}function Sc(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function o0(e){var t=Ap(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&Lp(n.ownerDocument.documentElement,n)){if(r!==null&&Sc(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var s=n.textContent.length,o=Math.min(r.start,s);r=r.end===void 0?o:Math.min(r.end,s),!e.extend&&o>r&&(s=r,r=o,o=s),s=Hd(n,o);var i=Hd(n,r);s&&i&&(e.rangeCount!==1||e.anchorNode!==s.node||e.anchorOffset!==s.offset||e.focusNode!==i.node||e.focusOffset!==i.offset)&&(t=t.createRange(),t.setStart(s.node,s.offset),e.removeAllRanges(),o>r?(e.addRange(t),e.extend(i.node,i.offset)):(t.setEnd(i.node,i.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var i0=en&&"documentMode"in document&&11>=document.documentMode,Or=null,au=null,Ws=null,lu=!1;function Vd(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;lu||Or==null||Or!==Ti(r)||(r=Or,"selectionStart"in r&&Sc(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),Ws&&no(Ws,r)||(Ws=r,r=Ai(au,"onSelect"),0<r.length&&(t=new _c("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=Or)))}function zo(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var Mr={animationend:zo("Animation","AnimationEnd"),animationiteration:zo("Animation","AnimationIteration"),animationstart:zo("Animation","AnimationStart"),transitionend:zo("Transition","TransitionEnd")},fl={},$p={};en&&($p=document.createElement("div").style,"AnimationEvent"in window||(delete Mr.animationend.animation,delete Mr.animationiteration.animation,delete Mr.animationstart.animation),"TransitionEvent"in window||delete Mr.transitionend.transition);function ka(e){if(fl[e])return fl[e];if(!Mr[e])return e;var t=Mr[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in $p)return fl[e]=t[n];return e}var Fp=ka("animationend"),Yp=ka("animationiteration"),Up=ka("animationstart"),Wp=ka("transitionend"),Bp=new Map,Qd="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Yn(e,t){Bp.set(e,t),mr(t,[e])}for(var hl=0;hl<Qd.length;hl++){var pl=Qd[hl],a0=pl.toLowerCase(),l0=pl[0].toUpperCase()+pl.slice(1);Yn(a0,"on"+l0)}Yn(Fp,"onAnimationEnd");Yn(Yp,"onAnimationIteration");Yn(Up,"onAnimationStart");Yn("dblclick","onDoubleClick");Yn("focusin","onFocus");Yn("focusout","onBlur");Yn(Wp,"onTransitionEnd");qr("onMouseEnter",["mouseout","mouseover"]);qr("onMouseLeave",["mouseout","mouseover"]);qr("onPointerEnter",["pointerout","pointerover"]);qr("onPointerLeave",["pointerout","pointerover"]);mr("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));mr("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));mr("onBeforeInput",["compositionend","keypress","textInput","paste"]);mr("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));mr("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));mr("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Rs="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),u0=new Set("cancel close invalid load scroll toggle".split(" ").concat(Rs));function Kd(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,av(r,t,void 0,e),e.currentTarget=null}function zp(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],s=r.event;r=r.listeners;e:{var o=void 0;if(t)for(var i=r.length-1;0<=i;i--){var a=r[i],l=a.instance,u=a.currentTarget;if(a=a.listener,l!==o&&s.isPropagationStopped())break e;Kd(s,a,u),o=l}else for(i=0;i<r.length;i++){if(a=r[i],l=a.instance,u=a.currentTarget,a=a.listener,l!==o&&s.isPropagationStopped())break e;Kd(s,a,u),o=l}}}if(Mi)throw e=ru,Mi=!1,ru=null,e}function re(e,t){var n=t[hu];n===void 0&&(n=t[hu]=new Set);var r=e+"__bubble";n.has(r)||(Hp(t,e,2,!1),n.add(r))}function ml(e,t,n){var r=0;t&&(r|=4),Hp(n,e,r,t)}var Ho="_reactListening"+Math.random().toString(36).slice(2);function ro(e){if(!e[Ho]){e[Ho]=!0,Xh.forEach(function(n){n!=="selectionchange"&&(u0.has(n)||ml(n,!1,e),ml(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[Ho]||(t[Ho]=!0,ml("selectionchange",!1,t))}}function Hp(e,t,n,r){switch(Pp(t)){case 1:var s=Sv;break;case 4:s=bv;break;default:s=vc}n=s.bind(null,t,n,e),s=void 0,!nu||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(s=!0),r?s!==void 0?e.addEventListener(t,n,{capture:!0,passive:s}):e.addEventListener(t,n,!0):s!==void 0?e.addEventListener(t,n,{passive:s}):e.addEventListener(t,n,!1)}function gl(e,t,n,r,s){var o=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var i=r.tag;if(i===3||i===4){var a=r.stateNode.containerInfo;if(a===s||a.nodeType===8&&a.parentNode===s)break;if(i===4)for(i=r.return;i!==null;){var l=i.tag;if((l===3||l===4)&&(l=i.stateNode.containerInfo,l===s||l.nodeType===8&&l.parentNode===s))return;i=i.return}for(;a!==null;){if(i=Xn(a),i===null)return;if(l=i.tag,l===5||l===6){r=o=i;continue e}a=a.parentNode}}r=r.return}pp(function(){var u=o,c=pc(n),d=[];e:{var f=Bp.get(e);if(f!==void 0){var p=_c,y=e;switch(e){case"keypress":if(pi(n)===0)break e;case"keydown":case"keyup":p=Fv;break;case"focusin":y="focus",p=ul;break;case"focusout":y="blur",p=ul;break;case"beforeblur":case"afterblur":p=ul;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":p=Ld;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":p=Ev;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":p=Wv;break;case Fp:case Yp:case Up:p=Tv;break;case Wp:p=zv;break;case"scroll":p=Dv;break;case"wheel":p=Vv;break;case"copy":case"cut":case"paste":p=Mv;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":p=$d}var _=(t&4)!==0,x=!_&&e==="scroll",g=_?f!==null?f+"Capture":null:f;_=[];for(var m=u,v;m!==null;){v=m;var k=v.stateNode;if(v.tag===5&&k!==null&&(v=k,g!==null&&(k=Js(m,g),k!=null&&_.push(so(m,k,v)))),x)break;m=m.return}0<_.length&&(f=new p(f,y,null,n,c),d.push({event:f,listeners:_}))}}if(!(t&7)){e:{if(f=e==="mouseover"||e==="pointerover",p=e==="mouseout"||e==="pointerout",f&&n!==eu&&(y=n.relatedTarget||n.fromElement)&&(Xn(y)||y[tn]))break e;if((p||f)&&(f=c.window===c?c:(f=c.ownerDocument)?f.defaultView||f.parentWindow:window,p?(y=n.relatedTarget||n.toElement,p=u,y=y?Xn(y):null,y!==null&&(x=gr(y),y!==x||y.tag!==5&&y.tag!==6)&&(y=null)):(p=null,y=u),p!==y)){if(_=Ld,k="onMouseLeave",g="onMouseEnter",m="mouse",(e==="pointerout"||e==="pointerover")&&(_=$d,k="onPointerLeave",g="onPointerEnter",m="pointer"),x=p==null?f:jr(p),v=y==null?f:jr(y),f=new _(k,m+"leave",p,n,c),f.target=x,f.relatedTarget=v,k=null,Xn(c)===u&&(_=new _(g,m+"enter",y,n,c),_.target=v,_.relatedTarget=x,k=_),x=k,p&&y)t:{for(_=p,g=y,m=0,v=_;v;v=kr(v))m++;for(v=0,k=g;k;k=kr(k))v++;for(;0<m-v;)_=kr(_),m--;for(;0<v-m;)g=kr(g),v--;for(;m--;){if(_===g||g!==null&&_===g.alternate)break t;_=kr(_),g=kr(g)}_=null}else _=null;p!==null&&qd(d,f,p,_,!1),y!==null&&x!==null&&qd(d,x,y,_,!0)}}e:{if(f=u?jr(u):window,p=f.nodeName&&f.nodeName.toLowerCase(),p==="select"||p==="input"&&f.type==="file")var E=Zv;else if(Ud(f))if(Rp)E=r0;else{E=t0;var P=e0}else(p=f.nodeName)&&p.toLowerCase()==="input"&&(f.type==="checkbox"||f.type==="radio")&&(E=n0);if(E&&(E=E(e,u))){jp(d,E,n,c);break e}P&&P(e,f,u),e==="focusout"&&(P=f._wrapperState)&&P.controlled&&f.type==="number"&&ql(f,"number",f.value)}switch(P=u?jr(u):window,e){case"focusin":(Ud(P)||P.contentEditable==="true")&&(Or=P,au=u,Ws=null);break;case"focusout":Ws=au=Or=null;break;case"mousedown":lu=!0;break;case"contextmenu":case"mouseup":case"dragend":lu=!1,Vd(d,n,c);break;case"selectionchange":if(i0)break;case"keydown":case"keyup":Vd(d,n,c)}var D;if(kc)e:{switch(e){case"compositionstart":var b="onCompositionStart";break e;case"compositionend":b="onCompositionEnd";break e;case"compositionupdate":b="onCompositionUpdate";break e}b=void 0}else Tr?Op(e,n)&&(b="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(b="onCompositionStart");b&&(Tp&&n.locale!=="ko"&&(Tr||b!=="onCompositionStart"?b==="onCompositionEnd"&&Tr&&(D=Np()):(wn=c,wc="value"in wn?wn.value:wn.textContent,Tr=!0)),P=Ai(u,b),0<P.length&&(b=new Ad(b,e,null,n,c),d.push({event:b,listeners:P}),D?b.data=D:(D=Mp(n),D!==null&&(b.data=D)))),(D=Kv?qv(e,n):Gv(e,n))&&(u=Ai(u,"onBeforeInput"),0<u.length&&(c=new Ad("onBeforeInput","beforeinput",null,n,c),d.push({event:c,listeners:u}),c.data=D))}zp(d,t)})}function so(e,t,n){return{instance:e,listener:t,currentTarget:n}}function Ai(e,t){for(var n=t+"Capture",r=[];e!==null;){var s=e,o=s.stateNode;s.tag===5&&o!==null&&(s=o,o=Js(e,n),o!=null&&r.unshift(so(e,o,s)),o=Js(e,t),o!=null&&r.push(so(e,o,s))),e=e.return}return r}function kr(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function qd(e,t,n,r,s){for(var o=t._reactName,i=[];n!==null&&n!==r;){var a=n,l=a.alternate,u=a.stateNode;if(l!==null&&l===r)break;a.tag===5&&u!==null&&(a=u,s?(l=Js(n,o),l!=null&&i.unshift(so(n,l,a))):s||(l=Js(n,o),l!=null&&i.push(so(n,l,a)))),n=n.return}i.length!==0&&e.push({event:t,listeners:i})}var c0=/\r\n?/g,d0=/\u0000|\uFFFD/g;function Gd(e){return(typeof e=="string"?e:""+e).replace(c0,`
`).replace(d0,"")}function Vo(e,t,n){if(t=Gd(t),Gd(e)!==t&&n)throw Error(T(425))}function $i(){}var uu=null,cu=null;function du(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var fu=typeof setTimeout=="function"?setTimeout:void 0,f0=typeof clearTimeout=="function"?clearTimeout:void 0,Jd=typeof Promise=="function"?Promise:void 0,h0=typeof queueMicrotask=="function"?queueMicrotask:typeof Jd<"u"?function(e){return Jd.resolve(null).then(e).catch(p0)}:fu;function p0(e){setTimeout(function(){throw e})}function yl(e,t){var n=t,r=0;do{var s=n.nextSibling;if(e.removeChild(n),s&&s.nodeType===8)if(n=s.data,n==="/$"){if(r===0){e.removeChild(s),eo(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=s}while(n);eo(t)}function Cn(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function Xd(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var hs=Math.random().toString(36).slice(2),jt="__reactFiber$"+hs,oo="__reactProps$"+hs,tn="__reactContainer$"+hs,hu="__reactEvents$"+hs,m0="__reactListeners$"+hs,g0="__reactHandles$"+hs;function Xn(e){var t=e[jt];if(t)return t;for(var n=e.parentNode;n;){if(t=n[tn]||n[jt]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=Xd(e);e!==null;){if(n=e[jt])return n;e=Xd(e)}return t}e=n,n=e.parentNode}return null}function Co(e){return e=e[jt]||e[tn],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function jr(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(T(33))}function Sa(e){return e[oo]||null}var pu=[],Rr=-1;function Un(e){return{current:e}}function se(e){0>Rr||(e.current=pu[Rr],pu[Rr]=null,Rr--)}function ne(e,t){Rr++,pu[Rr]=e.current,e.current=t}var In={},$e=Un(In),Xe=Un(!1),lr=In;function Gr(e,t){var n=e.type.contextTypes;if(!n)return In;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var s={},o;for(o in n)s[o]=t[o];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=s),s}function Ze(e){return e=e.childContextTypes,e!=null}function Fi(){se(Xe),se($e)}function Zd(e,t,n){if($e.current!==In)throw Error(T(168));ne($e,t),ne(Xe,n)}function Vp(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var s in r)if(!(s in t))throw Error(T(108,ev(e)||"Unknown",s));return fe({},n,r)}function Yi(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||In,lr=$e.current,ne($e,e),ne(Xe,Xe.current),!0}function ef(e,t,n){var r=e.stateNode;if(!r)throw Error(T(169));n?(e=Vp(e,t,lr),r.__reactInternalMemoizedMergedChildContext=e,se(Xe),se($e),ne($e,e)):se(Xe),ne(Xe,n)}var Qt=null,ba=!1,vl=!1;function Qp(e){Qt===null?Qt=[e]:Qt.push(e)}function y0(e){ba=!0,Qp(e)}function Wn(){if(!vl&&Qt!==null){vl=!0;var e=0,t=Z;try{var n=Qt;for(Z=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}Qt=null,ba=!1}catch(s){throw Qt!==null&&(Qt=Qt.slice(e+1)),vp(mc,Wn),s}finally{Z=t,vl=!1}}return null}var Ir=[],Lr=0,Ui=null,Wi=0,dt=[],ft=0,ur=null,qt=1,Gt="";function Qn(e,t){Ir[Lr++]=Wi,Ir[Lr++]=Ui,Ui=e,Wi=t}function Kp(e,t,n){dt[ft++]=qt,dt[ft++]=Gt,dt[ft++]=ur,ur=e;var r=qt;e=Gt;var s=32-bt(r)-1;r&=~(1<<s),n+=1;var o=32-bt(t)+s;if(30<o){var i=s-s%5;o=(r&(1<<i)-1).toString(32),r>>=i,s-=i,qt=1<<32-bt(t)+s|n<<s|r,Gt=o+e}else qt=1<<o|n<<s|r,Gt=e}function bc(e){e.return!==null&&(Qn(e,1),Kp(e,1,0))}function Dc(e){for(;e===Ui;)Ui=Ir[--Lr],Ir[Lr]=null,Wi=Ir[--Lr],Ir[Lr]=null;for(;e===ur;)ur=dt[--ft],dt[ft]=null,Gt=dt[--ft],dt[ft]=null,qt=dt[--ft],dt[ft]=null}var ot=null,st=null,ae=!1,St=null;function qp(e,t){var n=ht(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function tf(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,ot=e,st=Cn(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,ot=e,st=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=ur!==null?{id:qt,overflow:Gt}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=ht(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,ot=e,st=null,!0):!1;default:return!1}}function mu(e){return(e.mode&1)!==0&&(e.flags&128)===0}function gu(e){if(ae){var t=st;if(t){var n=t;if(!tf(e,t)){if(mu(e))throw Error(T(418));t=Cn(n.nextSibling);var r=ot;t&&tf(e,t)?qp(r,n):(e.flags=e.flags&-4097|2,ae=!1,ot=e)}}else{if(mu(e))throw Error(T(418));e.flags=e.flags&-4097|2,ae=!1,ot=e}}}function nf(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;ot=e}function Qo(e){if(e!==ot)return!1;if(!ae)return nf(e),ae=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!du(e.type,e.memoizedProps)),t&&(t=st)){if(mu(e))throw Gp(),Error(T(418));for(;t;)qp(e,t),t=Cn(t.nextSibling)}if(nf(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(T(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){st=Cn(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}st=null}}else st=ot?Cn(e.stateNode.nextSibling):null;return!0}function Gp(){for(var e=st;e;)e=Cn(e.nextSibling)}function Jr(){st=ot=null,ae=!1}function Cc(e){St===null?St=[e]:St.push(e)}var v0=an.ReactCurrentBatchConfig;function Ss(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(T(309));var r=n.stateNode}if(!r)throw Error(T(147,e));var s=r,o=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===o?t.ref:(t=function(i){var a=s.refs;i===null?delete a[o]:a[o]=i},t._stringRef=o,t)}if(typeof e!="string")throw Error(T(284));if(!n._owner)throw Error(T(290,e))}return e}function Ko(e,t){throw e=Object.prototype.toString.call(t),Error(T(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function rf(e){var t=e._init;return t(e._payload)}function Jp(e){function t(g,m){if(e){var v=g.deletions;v===null?(g.deletions=[m],g.flags|=16):v.push(m)}}function n(g,m){if(!e)return null;for(;m!==null;)t(g,m),m=m.sibling;return null}function r(g,m){for(g=new Map;m!==null;)m.key!==null?g.set(m.key,m):g.set(m.index,m),m=m.sibling;return g}function s(g,m){return g=Tn(g,m),g.index=0,g.sibling=null,g}function o(g,m,v){return g.index=v,e?(v=g.alternate,v!==null?(v=v.index,v<m?(g.flags|=2,m):v):(g.flags|=2,m)):(g.flags|=1048576,m)}function i(g){return e&&g.alternate===null&&(g.flags|=2),g}function a(g,m,v,k){return m===null||m.tag!==6?(m=Dl(v,g.mode,k),m.return=g,m):(m=s(m,v),m.return=g,m)}function l(g,m,v,k){var E=v.type;return E===Nr?c(g,m,v.props.children,k,v.key):m!==null&&(m.elementType===E||typeof E=="object"&&E!==null&&E.$$typeof===fn&&rf(E)===m.type)?(k=s(m,v.props),k.ref=Ss(g,m,v),k.return=g,k):(k=xi(v.type,v.key,v.props,null,g.mode,k),k.ref=Ss(g,m,v),k.return=g,k)}function u(g,m,v,k){return m===null||m.tag!==4||m.stateNode.containerInfo!==v.containerInfo||m.stateNode.implementation!==v.implementation?(m=Cl(v,g.mode,k),m.return=g,m):(m=s(m,v.children||[]),m.return=g,m)}function c(g,m,v,k,E){return m===null||m.tag!==7?(m=rr(v,g.mode,k,E),m.return=g,m):(m=s(m,v),m.return=g,m)}function d(g,m,v){if(typeof m=="string"&&m!==""||typeof m=="number")return m=Dl(""+m,g.mode,v),m.return=g,m;if(typeof m=="object"&&m!==null){switch(m.$$typeof){case Ao:return v=xi(m.type,m.key,m.props,null,g.mode,v),v.ref=Ss(g,null,m),v.return=g,v;case Pr:return m=Cl(m,g.mode,v),m.return=g,m;case fn:var k=m._init;return d(g,k(m._payload),v)}if(Ms(m)||vs(m))return m=rr(m,g.mode,v,null),m.return=g,m;Ko(g,m)}return null}function f(g,m,v,k){var E=m!==null?m.key:null;if(typeof v=="string"&&v!==""||typeof v=="number")return E!==null?null:a(g,m,""+v,k);if(typeof v=="object"&&v!==null){switch(v.$$typeof){case Ao:return v.key===E?l(g,m,v,k):null;case Pr:return v.key===E?u(g,m,v,k):null;case fn:return E=v._init,f(g,m,E(v._payload),k)}if(Ms(v)||vs(v))return E!==null?null:c(g,m,v,k,null);Ko(g,v)}return null}function p(g,m,v,k,E){if(typeof k=="string"&&k!==""||typeof k=="number")return g=g.get(v)||null,a(m,g,""+k,E);if(typeof k=="object"&&k!==null){switch(k.$$typeof){case Ao:return g=g.get(k.key===null?v:k.key)||null,l(m,g,k,E);case Pr:return g=g.get(k.key===null?v:k.key)||null,u(m,g,k,E);case fn:var P=k._init;return p(g,m,v,P(k._payload),E)}if(Ms(k)||vs(k))return g=g.get(v)||null,c(m,g,k,E,null);Ko(m,k)}return null}function y(g,m,v,k){for(var E=null,P=null,D=m,b=m=0,M=null;D!==null&&b<v.length;b++){D.index>b?(M=D,D=null):M=D.sibling;var O=f(g,D,v[b],k);if(O===null){D===null&&(D=M);break}e&&D&&O.alternate===null&&t(g,D),m=o(O,m,b),P===null?E=O:P.sibling=O,P=O,D=M}if(b===v.length)return n(g,D),ae&&Qn(g,b),E;if(D===null){for(;b<v.length;b++)D=d(g,v[b],k),D!==null&&(m=o(D,m,b),P===null?E=D:P.sibling=D,P=D);return ae&&Qn(g,b),E}for(D=r(g,D);b<v.length;b++)M=p(D,g,b,v[b],k),M!==null&&(e&&M.alternate!==null&&D.delete(M.key===null?b:M.key),m=o(M,m,b),P===null?E=M:P.sibling=M,P=M);return e&&D.forEach(function(F){return t(g,F)}),ae&&Qn(g,b),E}function _(g,m,v,k){var E=vs(v);if(typeof E!="function")throw Error(T(150));if(v=E.call(v),v==null)throw Error(T(151));for(var P=E=null,D=m,b=m=0,M=null,O=v.next();D!==null&&!O.done;b++,O=v.next()){D.index>b?(M=D,D=null):M=D.sibling;var F=f(g,D,O.value,k);if(F===null){D===null&&(D=M);break}e&&D&&F.alternate===null&&t(g,D),m=o(F,m,b),P===null?E=F:P.sibling=F,P=F,D=M}if(O.done)return n(g,D),ae&&Qn(g,b),E;if(D===null){for(;!O.done;b++,O=v.next())O=d(g,O.value,k),O!==null&&(m=o(O,m,b),P===null?E=O:P.sibling=O,P=O);return ae&&Qn(g,b),E}for(D=r(g,D);!O.done;b++,O=v.next())O=p(D,g,b,O.value,k),O!==null&&(e&&O.alternate!==null&&D.delete(O.key===null?b:O.key),m=o(O,m,b),P===null?E=O:P.sibling=O,P=O);return e&&D.forEach(function(H){return t(g,H)}),ae&&Qn(g,b),E}function x(g,m,v,k){if(typeof v=="object"&&v!==null&&v.type===Nr&&v.key===null&&(v=v.props.children),typeof v=="object"&&v!==null){switch(v.$$typeof){case Ao:e:{for(var E=v.key,P=m;P!==null;){if(P.key===E){if(E=v.type,E===Nr){if(P.tag===7){n(g,P.sibling),m=s(P,v.props.children),m.return=g,g=m;break e}}else if(P.elementType===E||typeof E=="object"&&E!==null&&E.$$typeof===fn&&rf(E)===P.type){n(g,P.sibling),m=s(P,v.props),m.ref=Ss(g,P,v),m.return=g,g=m;break e}n(g,P);break}else t(g,P);P=P.sibling}v.type===Nr?(m=rr(v.props.children,g.mode,k,v.key),m.return=g,g=m):(k=xi(v.type,v.key,v.props,null,g.mode,k),k.ref=Ss(g,m,v),k.return=g,g=k)}return i(g);case Pr:e:{for(P=v.key;m!==null;){if(m.key===P)if(m.tag===4&&m.stateNode.containerInfo===v.containerInfo&&m.stateNode.implementation===v.implementation){n(g,m.sibling),m=s(m,v.children||[]),m.return=g,g=m;break e}else{n(g,m);break}else t(g,m);m=m.sibling}m=Cl(v,g.mode,k),m.return=g,g=m}return i(g);case fn:return P=v._init,x(g,m,P(v._payload),k)}if(Ms(v))return y(g,m,v,k);if(vs(v))return _(g,m,v,k);Ko(g,v)}return typeof v=="string"&&v!==""||typeof v=="number"?(v=""+v,m!==null&&m.tag===6?(n(g,m.sibling),m=s(m,v),m.return=g,g=m):(n(g,m),m=Dl(v,g.mode,k),m.return=g,g=m),i(g)):n(g,m)}return x}var Xr=Jp(!0),Xp=Jp(!1),Bi=Un(null),zi=null,Ar=null,Ec=null;function Pc(){Ec=Ar=zi=null}function Nc(e){var t=Bi.current;se(Bi),e._currentValue=t}function yu(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function zr(e,t){zi=e,Ec=Ar=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(Je=!0),e.firstContext=null)}function gt(e){var t=e._currentValue;if(Ec!==e)if(e={context:e,memoizedValue:t,next:null},Ar===null){if(zi===null)throw Error(T(308));Ar=e,zi.dependencies={lanes:0,firstContext:e}}else Ar=Ar.next=e;return t}var Zn=null;function Tc(e){Zn===null?Zn=[e]:Zn.push(e)}function Zp(e,t,n,r){var s=t.interleaved;return s===null?(n.next=n,Tc(t)):(n.next=s.next,s.next=n),t.interleaved=n,nn(e,r)}function nn(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var hn=!1;function Oc(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function em(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function Jt(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function En(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,Q&2){var s=r.pending;return s===null?t.next=t:(t.next=s.next,s.next=t),r.pending=t,nn(e,n)}return s=r.interleaved,s===null?(t.next=t,Tc(r)):(t.next=s.next,s.next=t),r.interleaved=t,nn(e,n)}function mi(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,gc(e,n)}}function sf(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var s=null,o=null;if(n=n.firstBaseUpdate,n!==null){do{var i={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};o===null?s=o=i:o=o.next=i,n=n.next}while(n!==null);o===null?s=o=t:o=o.next=t}else s=o=t;n={baseState:r.baseState,firstBaseUpdate:s,lastBaseUpdate:o,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function Hi(e,t,n,r){var s=e.updateQueue;hn=!1;var o=s.firstBaseUpdate,i=s.lastBaseUpdate,a=s.shared.pending;if(a!==null){s.shared.pending=null;var l=a,u=l.next;l.next=null,i===null?o=u:i.next=u,i=l;var c=e.alternate;c!==null&&(c=c.updateQueue,a=c.lastBaseUpdate,a!==i&&(a===null?c.firstBaseUpdate=u:a.next=u,c.lastBaseUpdate=l))}if(o!==null){var d=s.baseState;i=0,c=u=l=null,a=o;do{var f=a.lane,p=a.eventTime;if((r&f)===f){c!==null&&(c=c.next={eventTime:p,lane:0,tag:a.tag,payload:a.payload,callback:a.callback,next:null});e:{var y=e,_=a;switch(f=t,p=n,_.tag){case 1:if(y=_.payload,typeof y=="function"){d=y.call(p,d,f);break e}d=y;break e;case 3:y.flags=y.flags&-65537|128;case 0:if(y=_.payload,f=typeof y=="function"?y.call(p,d,f):y,f==null)break e;d=fe({},d,f);break e;case 2:hn=!0}}a.callback!==null&&a.lane!==0&&(e.flags|=64,f=s.effects,f===null?s.effects=[a]:f.push(a))}else p={eventTime:p,lane:f,tag:a.tag,payload:a.payload,callback:a.callback,next:null},c===null?(u=c=p,l=d):c=c.next=p,i|=f;if(a=a.next,a===null){if(a=s.shared.pending,a===null)break;f=a,a=f.next,f.next=null,s.lastBaseUpdate=f,s.shared.pending=null}}while(!0);if(c===null&&(l=d),s.baseState=l,s.firstBaseUpdate=u,s.lastBaseUpdate=c,t=s.shared.interleaved,t!==null){s=t;do i|=s.lane,s=s.next;while(s!==t)}else o===null&&(s.shared.lanes=0);dr|=i,e.lanes=i,e.memoizedState=d}}function of(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],s=r.callback;if(s!==null){if(r.callback=null,r=n,typeof s!="function")throw Error(T(191,s));s.call(r)}}}var Eo={},$t=Un(Eo),io=Un(Eo),ao=Un(Eo);function er(e){if(e===Eo)throw Error(T(174));return e}function Mc(e,t){switch(ne(ao,t),ne(io,e),ne($t,Eo),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:Jl(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=Jl(t,e)}se($t),ne($t,t)}function Zr(){se($t),se(io),se(ao)}function tm(e){er(ao.current);var t=er($t.current),n=Jl(t,e.type);t!==n&&(ne(io,e),ne($t,n))}function jc(e){io.current===e&&(se($t),se(io))}var ue=Un(0);function Vi(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var wl=[];function Rc(){for(var e=0;e<wl.length;e++)wl[e]._workInProgressVersionPrimary=null;wl.length=0}var gi=an.ReactCurrentDispatcher,_l=an.ReactCurrentBatchConfig,cr=0,de=null,ke=null,Ce=null,Qi=!1,Bs=!1,lo=0,w0=0;function Ie(){throw Error(T(321))}function Ic(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!Ct(e[n],t[n]))return!1;return!0}function Lc(e,t,n,r,s,o){if(cr=o,de=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,gi.current=e===null||e.memoizedState===null?S0:b0,e=n(r,s),Bs){o=0;do{if(Bs=!1,lo=0,25<=o)throw Error(T(301));o+=1,Ce=ke=null,t.updateQueue=null,gi.current=D0,e=n(r,s)}while(Bs)}if(gi.current=Ki,t=ke!==null&&ke.next!==null,cr=0,Ce=ke=de=null,Qi=!1,t)throw Error(T(300));return e}function Ac(){var e=lo!==0;return lo=0,e}function Tt(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Ce===null?de.memoizedState=Ce=e:Ce=Ce.next=e,Ce}function yt(){if(ke===null){var e=de.alternate;e=e!==null?e.memoizedState:null}else e=ke.next;var t=Ce===null?de.memoizedState:Ce.next;if(t!==null)Ce=t,ke=e;else{if(e===null)throw Error(T(310));ke=e,e={memoizedState:ke.memoizedState,baseState:ke.baseState,baseQueue:ke.baseQueue,queue:ke.queue,next:null},Ce===null?de.memoizedState=Ce=e:Ce=Ce.next=e}return Ce}function uo(e,t){return typeof t=="function"?t(e):t}function xl(e){var t=yt(),n=t.queue;if(n===null)throw Error(T(311));n.lastRenderedReducer=e;var r=ke,s=r.baseQueue,o=n.pending;if(o!==null){if(s!==null){var i=s.next;s.next=o.next,o.next=i}r.baseQueue=s=o,n.pending=null}if(s!==null){o=s.next,r=r.baseState;var a=i=null,l=null,u=o;do{var c=u.lane;if((cr&c)===c)l!==null&&(l=l.next={lane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),r=u.hasEagerState?u.eagerState:e(r,u.action);else{var d={lane:c,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null};l===null?(a=l=d,i=r):l=l.next=d,de.lanes|=c,dr|=c}u=u.next}while(u!==null&&u!==o);l===null?i=r:l.next=a,Ct(r,t.memoizedState)||(Je=!0),t.memoizedState=r,t.baseState=i,t.baseQueue=l,n.lastRenderedState=r}if(e=n.interleaved,e!==null){s=e;do o=s.lane,de.lanes|=o,dr|=o,s=s.next;while(s!==e)}else s===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function kl(e){var t=yt(),n=t.queue;if(n===null)throw Error(T(311));n.lastRenderedReducer=e;var r=n.dispatch,s=n.pending,o=t.memoizedState;if(s!==null){n.pending=null;var i=s=s.next;do o=e(o,i.action),i=i.next;while(i!==s);Ct(o,t.memoizedState)||(Je=!0),t.memoizedState=o,t.baseQueue===null&&(t.baseState=o),n.lastRenderedState=o}return[o,r]}function nm(){}function rm(e,t){var n=de,r=yt(),s=t(),o=!Ct(r.memoizedState,s);if(o&&(r.memoizedState=s,Je=!0),r=r.queue,$c(im.bind(null,n,r,e),[e]),r.getSnapshot!==t||o||Ce!==null&&Ce.memoizedState.tag&1){if(n.flags|=2048,co(9,om.bind(null,n,r,s,t),void 0,null),Ee===null)throw Error(T(349));cr&30||sm(n,t,s)}return s}function sm(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=de.updateQueue,t===null?(t={lastEffect:null,stores:null},de.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function om(e,t,n,r){t.value=n,t.getSnapshot=r,am(t)&&lm(e)}function im(e,t,n){return n(function(){am(t)&&lm(e)})}function am(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!Ct(e,n)}catch{return!0}}function lm(e){var t=nn(e,1);t!==null&&Dt(t,e,1,-1)}function af(e){var t=Tt();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:uo,lastRenderedState:e},t.queue=e,e=e.dispatch=k0.bind(null,de,e),[t.memoizedState,e]}function co(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=de.updateQueue,t===null?(t={lastEffect:null,stores:null},de.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function um(){return yt().memoizedState}function yi(e,t,n,r){var s=Tt();de.flags|=e,s.memoizedState=co(1|t,n,void 0,r===void 0?null:r)}function Da(e,t,n,r){var s=yt();r=r===void 0?null:r;var o=void 0;if(ke!==null){var i=ke.memoizedState;if(o=i.destroy,r!==null&&Ic(r,i.deps)){s.memoizedState=co(t,n,o,r);return}}de.flags|=e,s.memoizedState=co(1|t,n,o,r)}function lf(e,t){return yi(8390656,8,e,t)}function $c(e,t){return Da(2048,8,e,t)}function cm(e,t){return Da(4,2,e,t)}function dm(e,t){return Da(4,4,e,t)}function fm(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function hm(e,t,n){return n=n!=null?n.concat([e]):null,Da(4,4,fm.bind(null,t,e),n)}function Fc(){}function pm(e,t){var n=yt();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Ic(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function mm(e,t){var n=yt();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Ic(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function gm(e,t,n){return cr&21?(Ct(n,t)||(n=xp(),de.lanes|=n,dr|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,Je=!0),e.memoizedState=n)}function _0(e,t){var n=Z;Z=n!==0&&4>n?n:4,e(!0);var r=_l.transition;_l.transition={};try{e(!1),t()}finally{Z=n,_l.transition=r}}function ym(){return yt().memoizedState}function x0(e,t,n){var r=Nn(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},vm(e))wm(t,n);else if(n=Zp(e,t,n,r),n!==null){var s=Be();Dt(n,e,r,s),_m(n,t,r)}}function k0(e,t,n){var r=Nn(e),s={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(vm(e))wm(t,s);else{var o=e.alternate;if(e.lanes===0&&(o===null||o.lanes===0)&&(o=t.lastRenderedReducer,o!==null))try{var i=t.lastRenderedState,a=o(i,n);if(s.hasEagerState=!0,s.eagerState=a,Ct(a,i)){var l=t.interleaved;l===null?(s.next=s,Tc(t)):(s.next=l.next,l.next=s),t.interleaved=s;return}}catch{}finally{}n=Zp(e,t,s,r),n!==null&&(s=Be(),Dt(n,e,r,s),_m(n,t,r))}}function vm(e){var t=e.alternate;return e===de||t!==null&&t===de}function wm(e,t){Bs=Qi=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function _m(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,gc(e,n)}}var Ki={readContext:gt,useCallback:Ie,useContext:Ie,useEffect:Ie,useImperativeHandle:Ie,useInsertionEffect:Ie,useLayoutEffect:Ie,useMemo:Ie,useReducer:Ie,useRef:Ie,useState:Ie,useDebugValue:Ie,useDeferredValue:Ie,useTransition:Ie,useMutableSource:Ie,useSyncExternalStore:Ie,useId:Ie,unstable_isNewReconciler:!1},S0={readContext:gt,useCallback:function(e,t){return Tt().memoizedState=[e,t===void 0?null:t],e},useContext:gt,useEffect:lf,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,yi(4194308,4,fm.bind(null,t,e),n)},useLayoutEffect:function(e,t){return yi(4194308,4,e,t)},useInsertionEffect:function(e,t){return yi(4,2,e,t)},useMemo:function(e,t){var n=Tt();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=Tt();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=x0.bind(null,de,e),[r.memoizedState,e]},useRef:function(e){var t=Tt();return e={current:e},t.memoizedState=e},useState:af,useDebugValue:Fc,useDeferredValue:function(e){return Tt().memoizedState=e},useTransition:function(){var e=af(!1),t=e[0];return e=_0.bind(null,e[1]),Tt().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=de,s=Tt();if(ae){if(n===void 0)throw Error(T(407));n=n()}else{if(n=t(),Ee===null)throw Error(T(349));cr&30||sm(r,t,n)}s.memoizedState=n;var o={value:n,getSnapshot:t};return s.queue=o,lf(im.bind(null,r,o,e),[e]),r.flags|=2048,co(9,om.bind(null,r,o,n,t),void 0,null),n},useId:function(){var e=Tt(),t=Ee.identifierPrefix;if(ae){var n=Gt,r=qt;n=(r&~(1<<32-bt(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=lo++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=w0++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},b0={readContext:gt,useCallback:pm,useContext:gt,useEffect:$c,useImperativeHandle:hm,useInsertionEffect:cm,useLayoutEffect:dm,useMemo:mm,useReducer:xl,useRef:um,useState:function(){return xl(uo)},useDebugValue:Fc,useDeferredValue:function(e){var t=yt();return gm(t,ke.memoizedState,e)},useTransition:function(){var e=xl(uo)[0],t=yt().memoizedState;return[e,t]},useMutableSource:nm,useSyncExternalStore:rm,useId:ym,unstable_isNewReconciler:!1},D0={readContext:gt,useCallback:pm,useContext:gt,useEffect:$c,useImperativeHandle:hm,useInsertionEffect:cm,useLayoutEffect:dm,useMemo:mm,useReducer:kl,useRef:um,useState:function(){return kl(uo)},useDebugValue:Fc,useDeferredValue:function(e){var t=yt();return ke===null?t.memoizedState=e:gm(t,ke.memoizedState,e)},useTransition:function(){var e=kl(uo)[0],t=yt().memoizedState;return[e,t]},useMutableSource:nm,useSyncExternalStore:rm,useId:ym,unstable_isNewReconciler:!1};function _t(e,t){if(e&&e.defaultProps){t=fe({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function vu(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:fe({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var Ca={isMounted:function(e){return(e=e._reactInternals)?gr(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=Be(),s=Nn(e),o=Jt(r,s);o.payload=t,n!=null&&(o.callback=n),t=En(e,o,s),t!==null&&(Dt(t,e,s,r),mi(t,e,s))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=Be(),s=Nn(e),o=Jt(r,s);o.tag=1,o.payload=t,n!=null&&(o.callback=n),t=En(e,o,s),t!==null&&(Dt(t,e,s,r),mi(t,e,s))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=Be(),r=Nn(e),s=Jt(n,r);s.tag=2,t!=null&&(s.callback=t),t=En(e,s,r),t!==null&&(Dt(t,e,r,n),mi(t,e,r))}};function uf(e,t,n,r,s,o,i){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,o,i):t.prototype&&t.prototype.isPureReactComponent?!no(n,r)||!no(s,o):!0}function xm(e,t,n){var r=!1,s=In,o=t.contextType;return typeof o=="object"&&o!==null?o=gt(o):(s=Ze(t)?lr:$e.current,r=t.contextTypes,o=(r=r!=null)?Gr(e,s):In),t=new t(n,o),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=Ca,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=s,e.__reactInternalMemoizedMaskedChildContext=o),t}function cf(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&Ca.enqueueReplaceState(t,t.state,null)}function wu(e,t,n,r){var s=e.stateNode;s.props=n,s.state=e.memoizedState,s.refs={},Oc(e);var o=t.contextType;typeof o=="object"&&o!==null?s.context=gt(o):(o=Ze(t)?lr:$e.current,s.context=Gr(e,o)),s.state=e.memoizedState,o=t.getDerivedStateFromProps,typeof o=="function"&&(vu(e,t,o,n),s.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof s.getSnapshotBeforeUpdate=="function"||typeof s.UNSAFE_componentWillMount!="function"&&typeof s.componentWillMount!="function"||(t=s.state,typeof s.componentWillMount=="function"&&s.componentWillMount(),typeof s.UNSAFE_componentWillMount=="function"&&s.UNSAFE_componentWillMount(),t!==s.state&&Ca.enqueueReplaceState(s,s.state,null),Hi(e,n,s,r),s.state=e.memoizedState),typeof s.componentDidMount=="function"&&(e.flags|=4194308)}function es(e,t){try{var n="",r=t;do n+=Zy(r),r=r.return;while(r);var s=n}catch(o){s=`
Error generating stack: `+o.message+`
`+o.stack}return{value:e,source:t,stack:s,digest:null}}function Sl(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function _u(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var C0=typeof WeakMap=="function"?WeakMap:Map;function km(e,t,n){n=Jt(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){Gi||(Gi=!0,Tu=r),_u(e,t)},n}function Sm(e,t,n){n=Jt(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var s=t.value;n.payload=function(){return r(s)},n.callback=function(){_u(e,t)}}var o=e.stateNode;return o!==null&&typeof o.componentDidCatch=="function"&&(n.callback=function(){_u(e,t),typeof r!="function"&&(Pn===null?Pn=new Set([this]):Pn.add(this));var i=t.stack;this.componentDidCatch(t.value,{componentStack:i!==null?i:""})}),n}function df(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new C0;var s=new Set;r.set(t,s)}else s=r.get(t),s===void 0&&(s=new Set,r.set(t,s));s.has(n)||(s.add(n),e=Y0.bind(null,e,t,n),t.then(e,e))}function ff(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function hf(e,t,n,r,s){return e.mode&1?(e.flags|=65536,e.lanes=s,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=Jt(-1,1),t.tag=2,En(n,t,1))),n.lanes|=1),e)}var E0=an.ReactCurrentOwner,Je=!1;function Fe(e,t,n,r){t.child=e===null?Xp(t,null,n,r):Xr(t,e.child,n,r)}function pf(e,t,n,r,s){n=n.render;var o=t.ref;return zr(t,s),r=Lc(e,t,n,r,o,s),n=Ac(),e!==null&&!Je?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~s,rn(e,t,s)):(ae&&n&&bc(t),t.flags|=1,Fe(e,t,r,s),t.child)}function mf(e,t,n,r,s){if(e===null){var o=n.type;return typeof o=="function"&&!Qc(o)&&o.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=o,bm(e,t,o,r,s)):(e=xi(n.type,null,r,t,t.mode,s),e.ref=t.ref,e.return=t,t.child=e)}if(o=e.child,!(e.lanes&s)){var i=o.memoizedProps;if(n=n.compare,n=n!==null?n:no,n(i,r)&&e.ref===t.ref)return rn(e,t,s)}return t.flags|=1,e=Tn(o,r),e.ref=t.ref,e.return=t,t.child=e}function bm(e,t,n,r,s){if(e!==null){var o=e.memoizedProps;if(no(o,r)&&e.ref===t.ref)if(Je=!1,t.pendingProps=r=o,(e.lanes&s)!==0)e.flags&131072&&(Je=!0);else return t.lanes=e.lanes,rn(e,t,s)}return xu(e,t,n,r,s)}function Dm(e,t,n){var r=t.pendingProps,s=r.children,o=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},ne(Fr,nt),nt|=n;else{if(!(n&1073741824))return e=o!==null?o.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,ne(Fr,nt),nt|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=o!==null?o.baseLanes:n,ne(Fr,nt),nt|=r}else o!==null?(r=o.baseLanes|n,t.memoizedState=null):r=n,ne(Fr,nt),nt|=r;return Fe(e,t,s,n),t.child}function Cm(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function xu(e,t,n,r,s){var o=Ze(n)?lr:$e.current;return o=Gr(t,o),zr(t,s),n=Lc(e,t,n,r,o,s),r=Ac(),e!==null&&!Je?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~s,rn(e,t,s)):(ae&&r&&bc(t),t.flags|=1,Fe(e,t,n,s),t.child)}function gf(e,t,n,r,s){if(Ze(n)){var o=!0;Yi(t)}else o=!1;if(zr(t,s),t.stateNode===null)vi(e,t),xm(t,n,r),wu(t,n,r,s),r=!0;else if(e===null){var i=t.stateNode,a=t.memoizedProps;i.props=a;var l=i.context,u=n.contextType;typeof u=="object"&&u!==null?u=gt(u):(u=Ze(n)?lr:$e.current,u=Gr(t,u));var c=n.getDerivedStateFromProps,d=typeof c=="function"||typeof i.getSnapshotBeforeUpdate=="function";d||typeof i.UNSAFE_componentWillReceiveProps!="function"&&typeof i.componentWillReceiveProps!="function"||(a!==r||l!==u)&&cf(t,i,r,u),hn=!1;var f=t.memoizedState;i.state=f,Hi(t,r,i,s),l=t.memoizedState,a!==r||f!==l||Xe.current||hn?(typeof c=="function"&&(vu(t,n,c,r),l=t.memoizedState),(a=hn||uf(t,n,a,r,f,l,u))?(d||typeof i.UNSAFE_componentWillMount!="function"&&typeof i.componentWillMount!="function"||(typeof i.componentWillMount=="function"&&i.componentWillMount(),typeof i.UNSAFE_componentWillMount=="function"&&i.UNSAFE_componentWillMount()),typeof i.componentDidMount=="function"&&(t.flags|=4194308)):(typeof i.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=l),i.props=r,i.state=l,i.context=u,r=a):(typeof i.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{i=t.stateNode,em(e,t),a=t.memoizedProps,u=t.type===t.elementType?a:_t(t.type,a),i.props=u,d=t.pendingProps,f=i.context,l=n.contextType,typeof l=="object"&&l!==null?l=gt(l):(l=Ze(n)?lr:$e.current,l=Gr(t,l));var p=n.getDerivedStateFromProps;(c=typeof p=="function"||typeof i.getSnapshotBeforeUpdate=="function")||typeof i.UNSAFE_componentWillReceiveProps!="function"&&typeof i.componentWillReceiveProps!="function"||(a!==d||f!==l)&&cf(t,i,r,l),hn=!1,f=t.memoizedState,i.state=f,Hi(t,r,i,s);var y=t.memoizedState;a!==d||f!==y||Xe.current||hn?(typeof p=="function"&&(vu(t,n,p,r),y=t.memoizedState),(u=hn||uf(t,n,u,r,f,y,l)||!1)?(c||typeof i.UNSAFE_componentWillUpdate!="function"&&typeof i.componentWillUpdate!="function"||(typeof i.componentWillUpdate=="function"&&i.componentWillUpdate(r,y,l),typeof i.UNSAFE_componentWillUpdate=="function"&&i.UNSAFE_componentWillUpdate(r,y,l)),typeof i.componentDidUpdate=="function"&&(t.flags|=4),typeof i.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof i.componentDidUpdate!="function"||a===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),typeof i.getSnapshotBeforeUpdate!="function"||a===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=y),i.props=r,i.state=y,i.context=l,r=u):(typeof i.componentDidUpdate!="function"||a===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),typeof i.getSnapshotBeforeUpdate!="function"||a===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),r=!1)}return ku(e,t,n,r,o,s)}function ku(e,t,n,r,s,o){Cm(e,t);var i=(t.flags&128)!==0;if(!r&&!i)return s&&ef(t,n,!1),rn(e,t,o);r=t.stateNode,E0.current=t;var a=i&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&i?(t.child=Xr(t,e.child,null,o),t.child=Xr(t,null,a,o)):Fe(e,t,a,o),t.memoizedState=r.state,s&&ef(t,n,!0),t.child}function Em(e){var t=e.stateNode;t.pendingContext?Zd(e,t.pendingContext,t.pendingContext!==t.context):t.context&&Zd(e,t.context,!1),Mc(e,t.containerInfo)}function yf(e,t,n,r,s){return Jr(),Cc(s),t.flags|=256,Fe(e,t,n,r),t.child}var Su={dehydrated:null,treeContext:null,retryLane:0};function bu(e){return{baseLanes:e,cachePool:null,transitions:null}}function Pm(e,t,n){var r=t.pendingProps,s=ue.current,o=!1,i=(t.flags&128)!==0,a;if((a=i)||(a=e!==null&&e.memoizedState===null?!1:(s&2)!==0),a?(o=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(s|=1),ne(ue,s&1),e===null)return gu(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(i=r.children,e=r.fallback,o?(r=t.mode,o=t.child,i={mode:"hidden",children:i},!(r&1)&&o!==null?(o.childLanes=0,o.pendingProps=i):o=Na(i,r,0,null),e=rr(e,r,n,null),o.return=t,e.return=t,o.sibling=e,t.child=o,t.child.memoizedState=bu(n),t.memoizedState=Su,e):Yc(t,i));if(s=e.memoizedState,s!==null&&(a=s.dehydrated,a!==null))return P0(e,t,i,r,a,s,n);if(o){o=r.fallback,i=t.mode,s=e.child,a=s.sibling;var l={mode:"hidden",children:r.children};return!(i&1)&&t.child!==s?(r=t.child,r.childLanes=0,r.pendingProps=l,t.deletions=null):(r=Tn(s,l),r.subtreeFlags=s.subtreeFlags&14680064),a!==null?o=Tn(a,o):(o=rr(o,i,n,null),o.flags|=2),o.return=t,r.return=t,r.sibling=o,t.child=r,r=o,o=t.child,i=e.child.memoizedState,i=i===null?bu(n):{baseLanes:i.baseLanes|n,cachePool:null,transitions:i.transitions},o.memoizedState=i,o.childLanes=e.childLanes&~n,t.memoizedState=Su,r}return o=e.child,e=o.sibling,r=Tn(o,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function Yc(e,t){return t=Na({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function qo(e,t,n,r){return r!==null&&Cc(r),Xr(t,e.child,null,n),e=Yc(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function P0(e,t,n,r,s,o,i){if(n)return t.flags&256?(t.flags&=-257,r=Sl(Error(T(422))),qo(e,t,i,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(o=r.fallback,s=t.mode,r=Na({mode:"visible",children:r.children},s,0,null),o=rr(o,s,i,null),o.flags|=2,r.return=t,o.return=t,r.sibling=o,t.child=r,t.mode&1&&Xr(t,e.child,null,i),t.child.memoizedState=bu(i),t.memoizedState=Su,o);if(!(t.mode&1))return qo(e,t,i,null);if(s.data==="$!"){if(r=s.nextSibling&&s.nextSibling.dataset,r)var a=r.dgst;return r=a,o=Error(T(419)),r=Sl(o,r,void 0),qo(e,t,i,r)}if(a=(i&e.childLanes)!==0,Je||a){if(r=Ee,r!==null){switch(i&-i){case 4:s=2;break;case 16:s=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:s=32;break;case 536870912:s=268435456;break;default:s=0}s=s&(r.suspendedLanes|i)?0:s,s!==0&&s!==o.retryLane&&(o.retryLane=s,nn(e,s),Dt(r,e,s,-1))}return Vc(),r=Sl(Error(T(421))),qo(e,t,i,r)}return s.data==="$?"?(t.flags|=128,t.child=e.child,t=U0.bind(null,e),s._reactRetry=t,null):(e=o.treeContext,st=Cn(s.nextSibling),ot=t,ae=!0,St=null,e!==null&&(dt[ft++]=qt,dt[ft++]=Gt,dt[ft++]=ur,qt=e.id,Gt=e.overflow,ur=t),t=Yc(t,r.children),t.flags|=4096,t)}function vf(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),yu(e.return,t,n)}function bl(e,t,n,r,s){var o=e.memoizedState;o===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:s}:(o.isBackwards=t,o.rendering=null,o.renderingStartTime=0,o.last=r,o.tail=n,o.tailMode=s)}function Nm(e,t,n){var r=t.pendingProps,s=r.revealOrder,o=r.tail;if(Fe(e,t,r.children,n),r=ue.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&vf(e,n,t);else if(e.tag===19)vf(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(ne(ue,r),!(t.mode&1))t.memoizedState=null;else switch(s){case"forwards":for(n=t.child,s=null;n!==null;)e=n.alternate,e!==null&&Vi(e)===null&&(s=n),n=n.sibling;n=s,n===null?(s=t.child,t.child=null):(s=n.sibling,n.sibling=null),bl(t,!1,s,n,o);break;case"backwards":for(n=null,s=t.child,t.child=null;s!==null;){if(e=s.alternate,e!==null&&Vi(e)===null){t.child=s;break}e=s.sibling,s.sibling=n,n=s,s=e}bl(t,!0,n,null,o);break;case"together":bl(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function vi(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function rn(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),dr|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(T(153));if(t.child!==null){for(e=t.child,n=Tn(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=Tn(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function N0(e,t,n){switch(t.tag){case 3:Em(t),Jr();break;case 5:tm(t);break;case 1:Ze(t.type)&&Yi(t);break;case 4:Mc(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,s=t.memoizedProps.value;ne(Bi,r._currentValue),r._currentValue=s;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(ne(ue,ue.current&1),t.flags|=128,null):n&t.child.childLanes?Pm(e,t,n):(ne(ue,ue.current&1),e=rn(e,t,n),e!==null?e.sibling:null);ne(ue,ue.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return Nm(e,t,n);t.flags|=128}if(s=t.memoizedState,s!==null&&(s.rendering=null,s.tail=null,s.lastEffect=null),ne(ue,ue.current),r)break;return null;case 22:case 23:return t.lanes=0,Dm(e,t,n)}return rn(e,t,n)}var Tm,Du,Om,Mm;Tm=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};Du=function(){};Om=function(e,t,n,r){var s=e.memoizedProps;if(s!==r){e=t.stateNode,er($t.current);var o=null;switch(n){case"input":s=Ql(e,s),r=Ql(e,r),o=[];break;case"select":s=fe({},s,{value:void 0}),r=fe({},r,{value:void 0}),o=[];break;case"textarea":s=Gl(e,s),r=Gl(e,r),o=[];break;default:typeof s.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=$i)}Xl(n,r);var i;n=null;for(u in s)if(!r.hasOwnProperty(u)&&s.hasOwnProperty(u)&&s[u]!=null)if(u==="style"){var a=s[u];for(i in a)a.hasOwnProperty(i)&&(n||(n={}),n[i]="")}else u!=="dangerouslySetInnerHTML"&&u!=="children"&&u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&u!=="autoFocus"&&(qs.hasOwnProperty(u)?o||(o=[]):(o=o||[]).push(u,null));for(u in r){var l=r[u];if(a=s!=null?s[u]:void 0,r.hasOwnProperty(u)&&l!==a&&(l!=null||a!=null))if(u==="style")if(a){for(i in a)!a.hasOwnProperty(i)||l&&l.hasOwnProperty(i)||(n||(n={}),n[i]="");for(i in l)l.hasOwnProperty(i)&&a[i]!==l[i]&&(n||(n={}),n[i]=l[i])}else n||(o||(o=[]),o.push(u,n)),n=l;else u==="dangerouslySetInnerHTML"?(l=l?l.__html:void 0,a=a?a.__html:void 0,l!=null&&a!==l&&(o=o||[]).push(u,l)):u==="children"?typeof l!="string"&&typeof l!="number"||(o=o||[]).push(u,""+l):u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&(qs.hasOwnProperty(u)?(l!=null&&u==="onScroll"&&re("scroll",e),o||a===l||(o=[])):(o=o||[]).push(u,l))}n&&(o=o||[]).push("style",n);var u=o;(t.updateQueue=u)&&(t.flags|=4)}};Mm=function(e,t,n,r){n!==r&&(t.flags|=4)};function bs(e,t){if(!ae)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function Le(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var s=e.child;s!==null;)n|=s.lanes|s.childLanes,r|=s.subtreeFlags&14680064,r|=s.flags&14680064,s.return=e,s=s.sibling;else for(s=e.child;s!==null;)n|=s.lanes|s.childLanes,r|=s.subtreeFlags,r|=s.flags,s.return=e,s=s.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function T0(e,t,n){var r=t.pendingProps;switch(Dc(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Le(t),null;case 1:return Ze(t.type)&&Fi(),Le(t),null;case 3:return r=t.stateNode,Zr(),se(Xe),se($e),Rc(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(Qo(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,St!==null&&(ju(St),St=null))),Du(e,t),Le(t),null;case 5:jc(t);var s=er(ao.current);if(n=t.type,e!==null&&t.stateNode!=null)Om(e,t,n,r,s),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(T(166));return Le(t),null}if(e=er($t.current),Qo(t)){r=t.stateNode,n=t.type;var o=t.memoizedProps;switch(r[jt]=t,r[oo]=o,e=(t.mode&1)!==0,n){case"dialog":re("cancel",r),re("close",r);break;case"iframe":case"object":case"embed":re("load",r);break;case"video":case"audio":for(s=0;s<Rs.length;s++)re(Rs[s],r);break;case"source":re("error",r);break;case"img":case"image":case"link":re("error",r),re("load",r);break;case"details":re("toggle",r);break;case"input":Cd(r,o),re("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!o.multiple},re("invalid",r);break;case"textarea":Pd(r,o),re("invalid",r)}Xl(n,o),s=null;for(var i in o)if(o.hasOwnProperty(i)){var a=o[i];i==="children"?typeof a=="string"?r.textContent!==a&&(o.suppressHydrationWarning!==!0&&Vo(r.textContent,a,e),s=["children",a]):typeof a=="number"&&r.textContent!==""+a&&(o.suppressHydrationWarning!==!0&&Vo(r.textContent,a,e),s=["children",""+a]):qs.hasOwnProperty(i)&&a!=null&&i==="onScroll"&&re("scroll",r)}switch(n){case"input":$o(r),Ed(r,o,!0);break;case"textarea":$o(r),Nd(r);break;case"select":case"option":break;default:typeof o.onClick=="function"&&(r.onclick=$i)}r=s,t.updateQueue=r,r!==null&&(t.flags|=4)}else{i=s.nodeType===9?s:s.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=ip(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=i.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=i.createElement(n,{is:r.is}):(e=i.createElement(n),n==="select"&&(i=e,r.multiple?i.multiple=!0:r.size&&(i.size=r.size))):e=i.createElementNS(e,n),e[jt]=t,e[oo]=r,Tm(e,t,!1,!1),t.stateNode=e;e:{switch(i=Zl(n,r),n){case"dialog":re("cancel",e),re("close",e),s=r;break;case"iframe":case"object":case"embed":re("load",e),s=r;break;case"video":case"audio":for(s=0;s<Rs.length;s++)re(Rs[s],e);s=r;break;case"source":re("error",e),s=r;break;case"img":case"image":case"link":re("error",e),re("load",e),s=r;break;case"details":re("toggle",e),s=r;break;case"input":Cd(e,r),s=Ql(e,r),re("invalid",e);break;case"option":s=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},s=fe({},r,{value:void 0}),re("invalid",e);break;case"textarea":Pd(e,r),s=Gl(e,r),re("invalid",e);break;default:s=r}Xl(n,s),a=s;for(o in a)if(a.hasOwnProperty(o)){var l=a[o];o==="style"?up(e,l):o==="dangerouslySetInnerHTML"?(l=l?l.__html:void 0,l!=null&&ap(e,l)):o==="children"?typeof l=="string"?(n!=="textarea"||l!=="")&&Gs(e,l):typeof l=="number"&&Gs(e,""+l):o!=="suppressContentEditableWarning"&&o!=="suppressHydrationWarning"&&o!=="autoFocus"&&(qs.hasOwnProperty(o)?l!=null&&o==="onScroll"&&re("scroll",e):l!=null&&cc(e,o,l,i))}switch(n){case"input":$o(e),Ed(e,r,!1);break;case"textarea":$o(e),Nd(e);break;case"option":r.value!=null&&e.setAttribute("value",""+Rn(r.value));break;case"select":e.multiple=!!r.multiple,o=r.value,o!=null?Yr(e,!!r.multiple,o,!1):r.defaultValue!=null&&Yr(e,!!r.multiple,r.defaultValue,!0);break;default:typeof s.onClick=="function"&&(e.onclick=$i)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return Le(t),null;case 6:if(e&&t.stateNode!=null)Mm(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(T(166));if(n=er(ao.current),er($t.current),Qo(t)){if(r=t.stateNode,n=t.memoizedProps,r[jt]=t,(o=r.nodeValue!==n)&&(e=ot,e!==null))switch(e.tag){case 3:Vo(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&Vo(r.nodeValue,n,(e.mode&1)!==0)}o&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[jt]=t,t.stateNode=r}return Le(t),null;case 13:if(se(ue),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(ae&&st!==null&&t.mode&1&&!(t.flags&128))Gp(),Jr(),t.flags|=98560,o=!1;else if(o=Qo(t),r!==null&&r.dehydrated!==null){if(e===null){if(!o)throw Error(T(318));if(o=t.memoizedState,o=o!==null?o.dehydrated:null,!o)throw Error(T(317));o[jt]=t}else Jr(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;Le(t),o=!1}else St!==null&&(ju(St),St=null),o=!0;if(!o)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||ue.current&1?Se===0&&(Se=3):Vc())),t.updateQueue!==null&&(t.flags|=4),Le(t),null);case 4:return Zr(),Du(e,t),e===null&&ro(t.stateNode.containerInfo),Le(t),null;case 10:return Nc(t.type._context),Le(t),null;case 17:return Ze(t.type)&&Fi(),Le(t),null;case 19:if(se(ue),o=t.memoizedState,o===null)return Le(t),null;if(r=(t.flags&128)!==0,i=o.rendering,i===null)if(r)bs(o,!1);else{if(Se!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(i=Vi(e),i!==null){for(t.flags|=128,bs(o,!1),r=i.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)o=n,e=r,o.flags&=14680066,i=o.alternate,i===null?(o.childLanes=0,o.lanes=e,o.child=null,o.subtreeFlags=0,o.memoizedProps=null,o.memoizedState=null,o.updateQueue=null,o.dependencies=null,o.stateNode=null):(o.childLanes=i.childLanes,o.lanes=i.lanes,o.child=i.child,o.subtreeFlags=0,o.deletions=null,o.memoizedProps=i.memoizedProps,o.memoizedState=i.memoizedState,o.updateQueue=i.updateQueue,o.type=i.type,e=i.dependencies,o.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return ne(ue,ue.current&1|2),t.child}e=e.sibling}o.tail!==null&&ge()>ts&&(t.flags|=128,r=!0,bs(o,!1),t.lanes=4194304)}else{if(!r)if(e=Vi(i),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),bs(o,!0),o.tail===null&&o.tailMode==="hidden"&&!i.alternate&&!ae)return Le(t),null}else 2*ge()-o.renderingStartTime>ts&&n!==1073741824&&(t.flags|=128,r=!0,bs(o,!1),t.lanes=4194304);o.isBackwards?(i.sibling=t.child,t.child=i):(n=o.last,n!==null?n.sibling=i:t.child=i,o.last=i)}return o.tail!==null?(t=o.tail,o.rendering=t,o.tail=t.sibling,o.renderingStartTime=ge(),t.sibling=null,n=ue.current,ne(ue,r?n&1|2:n&1),t):(Le(t),null);case 22:case 23:return Hc(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?nt&1073741824&&(Le(t),t.subtreeFlags&6&&(t.flags|=8192)):Le(t),null;case 24:return null;case 25:return null}throw Error(T(156,t.tag))}function O0(e,t){switch(Dc(t),t.tag){case 1:return Ze(t.type)&&Fi(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return Zr(),se(Xe),se($e),Rc(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return jc(t),null;case 13:if(se(ue),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(T(340));Jr()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return se(ue),null;case 4:return Zr(),null;case 10:return Nc(t.type._context),null;case 22:case 23:return Hc(),null;case 24:return null;default:return null}}var Go=!1,Ae=!1,M0=typeof WeakSet=="function"?WeakSet:Set,I=null;function $r(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){he(e,t,r)}else n.current=null}function Cu(e,t,n){try{n()}catch(r){he(e,t,r)}}var wf=!1;function j0(e,t){if(uu=Ii,e=Ap(),Sc(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var s=r.anchorOffset,o=r.focusNode;r=r.focusOffset;try{n.nodeType,o.nodeType}catch{n=null;break e}var i=0,a=-1,l=-1,u=0,c=0,d=e,f=null;t:for(;;){for(var p;d!==n||s!==0&&d.nodeType!==3||(a=i+s),d!==o||r!==0&&d.nodeType!==3||(l=i+r),d.nodeType===3&&(i+=d.nodeValue.length),(p=d.firstChild)!==null;)f=d,d=p;for(;;){if(d===e)break t;if(f===n&&++u===s&&(a=i),f===o&&++c===r&&(l=i),(p=d.nextSibling)!==null)break;d=f,f=d.parentNode}d=p}n=a===-1||l===-1?null:{start:a,end:l}}else n=null}n=n||{start:0,end:0}}else n=null;for(cu={focusedElem:e,selectionRange:n},Ii=!1,I=t;I!==null;)if(t=I,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,I=e;else for(;I!==null;){t=I;try{var y=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(y!==null){var _=y.memoizedProps,x=y.memoizedState,g=t.stateNode,m=g.getSnapshotBeforeUpdate(t.elementType===t.type?_:_t(t.type,_),x);g.__reactInternalSnapshotBeforeUpdate=m}break;case 3:var v=t.stateNode.containerInfo;v.nodeType===1?v.textContent="":v.nodeType===9&&v.documentElement&&v.removeChild(v.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(T(163))}}catch(k){he(t,t.return,k)}if(e=t.sibling,e!==null){e.return=t.return,I=e;break}I=t.return}return y=wf,wf=!1,y}function zs(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var s=r=r.next;do{if((s.tag&e)===e){var o=s.destroy;s.destroy=void 0,o!==void 0&&Cu(t,n,o)}s=s.next}while(s!==r)}}function Ea(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function Eu(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function jm(e){var t=e.alternate;t!==null&&(e.alternate=null,jm(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[jt],delete t[oo],delete t[hu],delete t[m0],delete t[g0])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function Rm(e){return e.tag===5||e.tag===3||e.tag===4}function _f(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||Rm(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function Pu(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=$i));else if(r!==4&&(e=e.child,e!==null))for(Pu(e,t,n),e=e.sibling;e!==null;)Pu(e,t,n),e=e.sibling}function Nu(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(Nu(e,t,n),e=e.sibling;e!==null;)Nu(e,t,n),e=e.sibling}var Me=null,xt=!1;function ln(e,t,n){for(n=n.child;n!==null;)Im(e,t,n),n=n.sibling}function Im(e,t,n){if(At&&typeof At.onCommitFiberUnmount=="function")try{At.onCommitFiberUnmount(wa,n)}catch{}switch(n.tag){case 5:Ae||$r(n,t);case 6:var r=Me,s=xt;Me=null,ln(e,t,n),Me=r,xt=s,Me!==null&&(xt?(e=Me,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):Me.removeChild(n.stateNode));break;case 18:Me!==null&&(xt?(e=Me,n=n.stateNode,e.nodeType===8?yl(e.parentNode,n):e.nodeType===1&&yl(e,n),eo(e)):yl(Me,n.stateNode));break;case 4:r=Me,s=xt,Me=n.stateNode.containerInfo,xt=!0,ln(e,t,n),Me=r,xt=s;break;case 0:case 11:case 14:case 15:if(!Ae&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){s=r=r.next;do{var o=s,i=o.destroy;o=o.tag,i!==void 0&&(o&2||o&4)&&Cu(n,t,i),s=s.next}while(s!==r)}ln(e,t,n);break;case 1:if(!Ae&&($r(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(a){he(n,t,a)}ln(e,t,n);break;case 21:ln(e,t,n);break;case 22:n.mode&1?(Ae=(r=Ae)||n.memoizedState!==null,ln(e,t,n),Ae=r):ln(e,t,n);break;default:ln(e,t,n)}}function xf(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new M0),t.forEach(function(r){var s=W0.bind(null,e,r);n.has(r)||(n.add(r),r.then(s,s))})}}function wt(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var s=n[r];try{var o=e,i=t,a=i;e:for(;a!==null;){switch(a.tag){case 5:Me=a.stateNode,xt=!1;break e;case 3:Me=a.stateNode.containerInfo,xt=!0;break e;case 4:Me=a.stateNode.containerInfo,xt=!0;break e}a=a.return}if(Me===null)throw Error(T(160));Im(o,i,s),Me=null,xt=!1;var l=s.alternate;l!==null&&(l.return=null),s.return=null}catch(u){he(s,t,u)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)Lm(t,e),t=t.sibling}function Lm(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(wt(t,e),Pt(e),r&4){try{zs(3,e,e.return),Ea(3,e)}catch(_){he(e,e.return,_)}try{zs(5,e,e.return)}catch(_){he(e,e.return,_)}}break;case 1:wt(t,e),Pt(e),r&512&&n!==null&&$r(n,n.return);break;case 5:if(wt(t,e),Pt(e),r&512&&n!==null&&$r(n,n.return),e.flags&32){var s=e.stateNode;try{Gs(s,"")}catch(_){he(e,e.return,_)}}if(r&4&&(s=e.stateNode,s!=null)){var o=e.memoizedProps,i=n!==null?n.memoizedProps:o,a=e.type,l=e.updateQueue;if(e.updateQueue=null,l!==null)try{a==="input"&&o.type==="radio"&&o.name!=null&&sp(s,o),Zl(a,i);var u=Zl(a,o);for(i=0;i<l.length;i+=2){var c=l[i],d=l[i+1];c==="style"?up(s,d):c==="dangerouslySetInnerHTML"?ap(s,d):c==="children"?Gs(s,d):cc(s,c,d,u)}switch(a){case"input":Kl(s,o);break;case"textarea":op(s,o);break;case"select":var f=s._wrapperState.wasMultiple;s._wrapperState.wasMultiple=!!o.multiple;var p=o.value;p!=null?Yr(s,!!o.multiple,p,!1):f!==!!o.multiple&&(o.defaultValue!=null?Yr(s,!!o.multiple,o.defaultValue,!0):Yr(s,!!o.multiple,o.multiple?[]:"",!1))}s[oo]=o}catch(_){he(e,e.return,_)}}break;case 6:if(wt(t,e),Pt(e),r&4){if(e.stateNode===null)throw Error(T(162));s=e.stateNode,o=e.memoizedProps;try{s.nodeValue=o}catch(_){he(e,e.return,_)}}break;case 3:if(wt(t,e),Pt(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{eo(t.containerInfo)}catch(_){he(e,e.return,_)}break;case 4:wt(t,e),Pt(e);break;case 13:wt(t,e),Pt(e),s=e.child,s.flags&8192&&(o=s.memoizedState!==null,s.stateNode.isHidden=o,!o||s.alternate!==null&&s.alternate.memoizedState!==null||(Bc=ge())),r&4&&xf(e);break;case 22:if(c=n!==null&&n.memoizedState!==null,e.mode&1?(Ae=(u=Ae)||c,wt(t,e),Ae=u):wt(t,e),Pt(e),r&8192){if(u=e.memoizedState!==null,(e.stateNode.isHidden=u)&&!c&&e.mode&1)for(I=e,c=e.child;c!==null;){for(d=I=c;I!==null;){switch(f=I,p=f.child,f.tag){case 0:case 11:case 14:case 15:zs(4,f,f.return);break;case 1:$r(f,f.return);var y=f.stateNode;if(typeof y.componentWillUnmount=="function"){r=f,n=f.return;try{t=r,y.props=t.memoizedProps,y.state=t.memoizedState,y.componentWillUnmount()}catch(_){he(r,n,_)}}break;case 5:$r(f,f.return);break;case 22:if(f.memoizedState!==null){Sf(d);continue}}p!==null?(p.return=f,I=p):Sf(d)}c=c.sibling}e:for(c=null,d=e;;){if(d.tag===5){if(c===null){c=d;try{s=d.stateNode,u?(o=s.style,typeof o.setProperty=="function"?o.setProperty("display","none","important"):o.display="none"):(a=d.stateNode,l=d.memoizedProps.style,i=l!=null&&l.hasOwnProperty("display")?l.display:null,a.style.display=lp("display",i))}catch(_){he(e,e.return,_)}}}else if(d.tag===6){if(c===null)try{d.stateNode.nodeValue=u?"":d.memoizedProps}catch(_){he(e,e.return,_)}}else if((d.tag!==22&&d.tag!==23||d.memoizedState===null||d===e)&&d.child!==null){d.child.return=d,d=d.child;continue}if(d===e)break e;for(;d.sibling===null;){if(d.return===null||d.return===e)break e;c===d&&(c=null),d=d.return}c===d&&(c=null),d.sibling.return=d.return,d=d.sibling}}break;case 19:wt(t,e),Pt(e),r&4&&xf(e);break;case 21:break;default:wt(t,e),Pt(e)}}function Pt(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(Rm(n)){var r=n;break e}n=n.return}throw Error(T(160))}switch(r.tag){case 5:var s=r.stateNode;r.flags&32&&(Gs(s,""),r.flags&=-33);var o=_f(e);Nu(e,o,s);break;case 3:case 4:var i=r.stateNode.containerInfo,a=_f(e);Pu(e,a,i);break;default:throw Error(T(161))}}catch(l){he(e,e.return,l)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function R0(e,t,n){I=e,Am(e)}function Am(e,t,n){for(var r=(e.mode&1)!==0;I!==null;){var s=I,o=s.child;if(s.tag===22&&r){var i=s.memoizedState!==null||Go;if(!i){var a=s.alternate,l=a!==null&&a.memoizedState!==null||Ae;a=Go;var u=Ae;if(Go=i,(Ae=l)&&!u)for(I=s;I!==null;)i=I,l=i.child,i.tag===22&&i.memoizedState!==null?bf(s):l!==null?(l.return=i,I=l):bf(s);for(;o!==null;)I=o,Am(o),o=o.sibling;I=s,Go=a,Ae=u}kf(e)}else s.subtreeFlags&8772&&o!==null?(o.return=s,I=o):kf(e)}}function kf(e){for(;I!==null;){var t=I;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:Ae||Ea(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!Ae)if(n===null)r.componentDidMount();else{var s=t.elementType===t.type?n.memoizedProps:_t(t.type,n.memoizedProps);r.componentDidUpdate(s,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var o=t.updateQueue;o!==null&&of(t,o,r);break;case 3:var i=t.updateQueue;if(i!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}of(t,i,n)}break;case 5:var a=t.stateNode;if(n===null&&t.flags&4){n=a;var l=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":l.autoFocus&&n.focus();break;case"img":l.src&&(n.src=l.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var u=t.alternate;if(u!==null){var c=u.memoizedState;if(c!==null){var d=c.dehydrated;d!==null&&eo(d)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(T(163))}Ae||t.flags&512&&Eu(t)}catch(f){he(t,t.return,f)}}if(t===e){I=null;break}if(n=t.sibling,n!==null){n.return=t.return,I=n;break}I=t.return}}function Sf(e){for(;I!==null;){var t=I;if(t===e){I=null;break}var n=t.sibling;if(n!==null){n.return=t.return,I=n;break}I=t.return}}function bf(e){for(;I!==null;){var t=I;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{Ea(4,t)}catch(l){he(t,n,l)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var s=t.return;try{r.componentDidMount()}catch(l){he(t,s,l)}}var o=t.return;try{Eu(t)}catch(l){he(t,o,l)}break;case 5:var i=t.return;try{Eu(t)}catch(l){he(t,i,l)}}}catch(l){he(t,t.return,l)}if(t===e){I=null;break}var a=t.sibling;if(a!==null){a.return=t.return,I=a;break}I=t.return}}var I0=Math.ceil,qi=an.ReactCurrentDispatcher,Uc=an.ReactCurrentOwner,pt=an.ReactCurrentBatchConfig,Q=0,Ee=null,_e=null,je=0,nt=0,Fr=Un(0),Se=0,fo=null,dr=0,Pa=0,Wc=0,Hs=null,Qe=null,Bc=0,ts=1/0,Ht=null,Gi=!1,Tu=null,Pn=null,Jo=!1,_n=null,Ji=0,Vs=0,Ou=null,wi=-1,_i=0;function Be(){return Q&6?ge():wi!==-1?wi:wi=ge()}function Nn(e){return e.mode&1?Q&2&&je!==0?je&-je:v0.transition!==null?(_i===0&&(_i=xp()),_i):(e=Z,e!==0||(e=window.event,e=e===void 0?16:Pp(e.type)),e):1}function Dt(e,t,n,r){if(50<Vs)throw Vs=0,Ou=null,Error(T(185));bo(e,n,r),(!(Q&2)||e!==Ee)&&(e===Ee&&(!(Q&2)&&(Pa|=n),Se===4&&yn(e,je)),et(e,r),n===1&&Q===0&&!(t.mode&1)&&(ts=ge()+500,ba&&Wn()))}function et(e,t){var n=e.callbackNode;vv(e,t);var r=Ri(e,e===Ee?je:0);if(r===0)n!==null&&Md(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&Md(n),t===1)e.tag===0?y0(Df.bind(null,e)):Qp(Df.bind(null,e)),h0(function(){!(Q&6)&&Wn()}),n=null;else{switch(kp(r)){case 1:n=mc;break;case 4:n=wp;break;case 16:n=ji;break;case 536870912:n=_p;break;default:n=ji}n=Hm(n,$m.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function $m(e,t){if(wi=-1,_i=0,Q&6)throw Error(T(327));var n=e.callbackNode;if(Hr()&&e.callbackNode!==n)return null;var r=Ri(e,e===Ee?je:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=Xi(e,r);else{t=r;var s=Q;Q|=2;var o=Ym();(Ee!==e||je!==t)&&(Ht=null,ts=ge()+500,nr(e,t));do try{$0();break}catch(a){Fm(e,a)}while(!0);Pc(),qi.current=o,Q=s,_e!==null?t=0:(Ee=null,je=0,t=Se)}if(t!==0){if(t===2&&(s=su(e),s!==0&&(r=s,t=Mu(e,s))),t===1)throw n=fo,nr(e,0),yn(e,r),et(e,ge()),n;if(t===6)yn(e,r);else{if(s=e.current.alternate,!(r&30)&&!L0(s)&&(t=Xi(e,r),t===2&&(o=su(e),o!==0&&(r=o,t=Mu(e,o))),t===1))throw n=fo,nr(e,0),yn(e,r),et(e,ge()),n;switch(e.finishedWork=s,e.finishedLanes=r,t){case 0:case 1:throw Error(T(345));case 2:Kn(e,Qe,Ht);break;case 3:if(yn(e,r),(r&130023424)===r&&(t=Bc+500-ge(),10<t)){if(Ri(e,0)!==0)break;if(s=e.suspendedLanes,(s&r)!==r){Be(),e.pingedLanes|=e.suspendedLanes&s;break}e.timeoutHandle=fu(Kn.bind(null,e,Qe,Ht),t);break}Kn(e,Qe,Ht);break;case 4:if(yn(e,r),(r&4194240)===r)break;for(t=e.eventTimes,s=-1;0<r;){var i=31-bt(r);o=1<<i,i=t[i],i>s&&(s=i),r&=~o}if(r=s,r=ge()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*I0(r/1960))-r,10<r){e.timeoutHandle=fu(Kn.bind(null,e,Qe,Ht),r);break}Kn(e,Qe,Ht);break;case 5:Kn(e,Qe,Ht);break;default:throw Error(T(329))}}}return et(e,ge()),e.callbackNode===n?$m.bind(null,e):null}function Mu(e,t){var n=Hs;return e.current.memoizedState.isDehydrated&&(nr(e,t).flags|=256),e=Xi(e,t),e!==2&&(t=Qe,Qe=n,t!==null&&ju(t)),e}function ju(e){Qe===null?Qe=e:Qe.push.apply(Qe,e)}function L0(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var s=n[r],o=s.getSnapshot;s=s.value;try{if(!Ct(o(),s))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function yn(e,t){for(t&=~Wc,t&=~Pa,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-bt(t),r=1<<n;e[n]=-1,t&=~r}}function Df(e){if(Q&6)throw Error(T(327));Hr();var t=Ri(e,0);if(!(t&1))return et(e,ge()),null;var n=Xi(e,t);if(e.tag!==0&&n===2){var r=su(e);r!==0&&(t=r,n=Mu(e,r))}if(n===1)throw n=fo,nr(e,0),yn(e,t),et(e,ge()),n;if(n===6)throw Error(T(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,Kn(e,Qe,Ht),et(e,ge()),null}function zc(e,t){var n=Q;Q|=1;try{return e(t)}finally{Q=n,Q===0&&(ts=ge()+500,ba&&Wn())}}function fr(e){_n!==null&&_n.tag===0&&!(Q&6)&&Hr();var t=Q;Q|=1;var n=pt.transition,r=Z;try{if(pt.transition=null,Z=1,e)return e()}finally{Z=r,pt.transition=n,Q=t,!(Q&6)&&Wn()}}function Hc(){nt=Fr.current,se(Fr)}function nr(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,f0(n)),_e!==null)for(n=_e.return;n!==null;){var r=n;switch(Dc(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&Fi();break;case 3:Zr(),se(Xe),se($e),Rc();break;case 5:jc(r);break;case 4:Zr();break;case 13:se(ue);break;case 19:se(ue);break;case 10:Nc(r.type._context);break;case 22:case 23:Hc()}n=n.return}if(Ee=e,_e=e=Tn(e.current,null),je=nt=t,Se=0,fo=null,Wc=Pa=dr=0,Qe=Hs=null,Zn!==null){for(t=0;t<Zn.length;t++)if(n=Zn[t],r=n.interleaved,r!==null){n.interleaved=null;var s=r.next,o=n.pending;if(o!==null){var i=o.next;o.next=s,r.next=i}n.pending=r}Zn=null}return e}function Fm(e,t){do{var n=_e;try{if(Pc(),gi.current=Ki,Qi){for(var r=de.memoizedState;r!==null;){var s=r.queue;s!==null&&(s.pending=null),r=r.next}Qi=!1}if(cr=0,Ce=ke=de=null,Bs=!1,lo=0,Uc.current=null,n===null||n.return===null){Se=1,fo=t,_e=null;break}e:{var o=e,i=n.return,a=n,l=t;if(t=je,a.flags|=32768,l!==null&&typeof l=="object"&&typeof l.then=="function"){var u=l,c=a,d=c.tag;if(!(c.mode&1)&&(d===0||d===11||d===15)){var f=c.alternate;f?(c.updateQueue=f.updateQueue,c.memoizedState=f.memoizedState,c.lanes=f.lanes):(c.updateQueue=null,c.memoizedState=null)}var p=ff(i);if(p!==null){p.flags&=-257,hf(p,i,a,o,t),p.mode&1&&df(o,u,t),t=p,l=u;var y=t.updateQueue;if(y===null){var _=new Set;_.add(l),t.updateQueue=_}else y.add(l);break e}else{if(!(t&1)){df(o,u,t),Vc();break e}l=Error(T(426))}}else if(ae&&a.mode&1){var x=ff(i);if(x!==null){!(x.flags&65536)&&(x.flags|=256),hf(x,i,a,o,t),Cc(es(l,a));break e}}o=l=es(l,a),Se!==4&&(Se=2),Hs===null?Hs=[o]:Hs.push(o),o=i;do{switch(o.tag){case 3:o.flags|=65536,t&=-t,o.lanes|=t;var g=km(o,l,t);sf(o,g);break e;case 1:a=l;var m=o.type,v=o.stateNode;if(!(o.flags&128)&&(typeof m.getDerivedStateFromError=="function"||v!==null&&typeof v.componentDidCatch=="function"&&(Pn===null||!Pn.has(v)))){o.flags|=65536,t&=-t,o.lanes|=t;var k=Sm(o,a,t);sf(o,k);break e}}o=o.return}while(o!==null)}Wm(n)}catch(E){t=E,_e===n&&n!==null&&(_e=n=n.return);continue}break}while(!0)}function Ym(){var e=qi.current;return qi.current=Ki,e===null?Ki:e}function Vc(){(Se===0||Se===3||Se===2)&&(Se=4),Ee===null||!(dr&268435455)&&!(Pa&268435455)||yn(Ee,je)}function Xi(e,t){var n=Q;Q|=2;var r=Ym();(Ee!==e||je!==t)&&(Ht=null,nr(e,t));do try{A0();break}catch(s){Fm(e,s)}while(!0);if(Pc(),Q=n,qi.current=r,_e!==null)throw Error(T(261));return Ee=null,je=0,Se}function A0(){for(;_e!==null;)Um(_e)}function $0(){for(;_e!==null&&!uv();)Um(_e)}function Um(e){var t=zm(e.alternate,e,nt);e.memoizedProps=e.pendingProps,t===null?Wm(e):_e=t,Uc.current=null}function Wm(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=O0(n,t),n!==null){n.flags&=32767,_e=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{Se=6,_e=null;return}}else if(n=T0(n,t,nt),n!==null){_e=n;return}if(t=t.sibling,t!==null){_e=t;return}_e=t=e}while(t!==null);Se===0&&(Se=5)}function Kn(e,t,n){var r=Z,s=pt.transition;try{pt.transition=null,Z=1,F0(e,t,n,r)}finally{pt.transition=s,Z=r}return null}function F0(e,t,n,r){do Hr();while(_n!==null);if(Q&6)throw Error(T(327));n=e.finishedWork;var s=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(T(177));e.callbackNode=null,e.callbackPriority=0;var o=n.lanes|n.childLanes;if(wv(e,o),e===Ee&&(_e=Ee=null,je=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||Jo||(Jo=!0,Hm(ji,function(){return Hr(),null})),o=(n.flags&15990)!==0,n.subtreeFlags&15990||o){o=pt.transition,pt.transition=null;var i=Z;Z=1;var a=Q;Q|=4,Uc.current=null,j0(e,n),Lm(n,e),o0(cu),Ii=!!uu,cu=uu=null,e.current=n,R0(n),cv(),Q=a,Z=i,pt.transition=o}else e.current=n;if(Jo&&(Jo=!1,_n=e,Ji=s),o=e.pendingLanes,o===0&&(Pn=null),hv(n.stateNode),et(e,ge()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)s=t[n],r(s.value,{componentStack:s.stack,digest:s.digest});if(Gi)throw Gi=!1,e=Tu,Tu=null,e;return Ji&1&&e.tag!==0&&Hr(),o=e.pendingLanes,o&1?e===Ou?Vs++:(Vs=0,Ou=e):Vs=0,Wn(),null}function Hr(){if(_n!==null){var e=kp(Ji),t=pt.transition,n=Z;try{if(pt.transition=null,Z=16>e?16:e,_n===null)var r=!1;else{if(e=_n,_n=null,Ji=0,Q&6)throw Error(T(331));var s=Q;for(Q|=4,I=e.current;I!==null;){var o=I,i=o.child;if(I.flags&16){var a=o.deletions;if(a!==null){for(var l=0;l<a.length;l++){var u=a[l];for(I=u;I!==null;){var c=I;switch(c.tag){case 0:case 11:case 15:zs(8,c,o)}var d=c.child;if(d!==null)d.return=c,I=d;else for(;I!==null;){c=I;var f=c.sibling,p=c.return;if(jm(c),c===u){I=null;break}if(f!==null){f.return=p,I=f;break}I=p}}}var y=o.alternate;if(y!==null){var _=y.child;if(_!==null){y.child=null;do{var x=_.sibling;_.sibling=null,_=x}while(_!==null)}}I=o}}if(o.subtreeFlags&2064&&i!==null)i.return=o,I=i;else e:for(;I!==null;){if(o=I,o.flags&2048)switch(o.tag){case 0:case 11:case 15:zs(9,o,o.return)}var g=o.sibling;if(g!==null){g.return=o.return,I=g;break e}I=o.return}}var m=e.current;for(I=m;I!==null;){i=I;var v=i.child;if(i.subtreeFlags&2064&&v!==null)v.return=i,I=v;else e:for(i=m;I!==null;){if(a=I,a.flags&2048)try{switch(a.tag){case 0:case 11:case 15:Ea(9,a)}}catch(E){he(a,a.return,E)}if(a===i){I=null;break e}var k=a.sibling;if(k!==null){k.return=a.return,I=k;break e}I=a.return}}if(Q=s,Wn(),At&&typeof At.onPostCommitFiberRoot=="function")try{At.onPostCommitFiberRoot(wa,e)}catch{}r=!0}return r}finally{Z=n,pt.transition=t}}return!1}function Cf(e,t,n){t=es(n,t),t=km(e,t,1),e=En(e,t,1),t=Be(),e!==null&&(bo(e,1,t),et(e,t))}function he(e,t,n){if(e.tag===3)Cf(e,e,n);else for(;t!==null;){if(t.tag===3){Cf(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(Pn===null||!Pn.has(r))){e=es(n,e),e=Sm(t,e,1),t=En(t,e,1),e=Be(),t!==null&&(bo(t,1,e),et(t,e));break}}t=t.return}}function Y0(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=Be(),e.pingedLanes|=e.suspendedLanes&n,Ee===e&&(je&n)===n&&(Se===4||Se===3&&(je&130023424)===je&&500>ge()-Bc?nr(e,0):Wc|=n),et(e,t)}function Bm(e,t){t===0&&(e.mode&1?(t=Uo,Uo<<=1,!(Uo&130023424)&&(Uo=4194304)):t=1);var n=Be();e=nn(e,t),e!==null&&(bo(e,t,n),et(e,n))}function U0(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),Bm(e,n)}function W0(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,s=e.memoizedState;s!==null&&(n=s.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(T(314))}r!==null&&r.delete(t),Bm(e,n)}var zm;zm=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||Xe.current)Je=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return Je=!1,N0(e,t,n);Je=!!(e.flags&131072)}else Je=!1,ae&&t.flags&1048576&&Kp(t,Wi,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;vi(e,t),e=t.pendingProps;var s=Gr(t,$e.current);zr(t,n),s=Lc(null,t,r,e,s,n);var o=Ac();return t.flags|=1,typeof s=="object"&&s!==null&&typeof s.render=="function"&&s.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,Ze(r)?(o=!0,Yi(t)):o=!1,t.memoizedState=s.state!==null&&s.state!==void 0?s.state:null,Oc(t),s.updater=Ca,t.stateNode=s,s._reactInternals=t,wu(t,r,e,n),t=ku(null,t,r,!0,o,n)):(t.tag=0,ae&&o&&bc(t),Fe(null,t,s,n),t=t.child),t;case 16:r=t.elementType;e:{switch(vi(e,t),e=t.pendingProps,s=r._init,r=s(r._payload),t.type=r,s=t.tag=z0(r),e=_t(r,e),s){case 0:t=xu(null,t,r,e,n);break e;case 1:t=gf(null,t,r,e,n);break e;case 11:t=pf(null,t,r,e,n);break e;case 14:t=mf(null,t,r,_t(r.type,e),n);break e}throw Error(T(306,r,""))}return t;case 0:return r=t.type,s=t.pendingProps,s=t.elementType===r?s:_t(r,s),xu(e,t,r,s,n);case 1:return r=t.type,s=t.pendingProps,s=t.elementType===r?s:_t(r,s),gf(e,t,r,s,n);case 3:e:{if(Em(t),e===null)throw Error(T(387));r=t.pendingProps,o=t.memoizedState,s=o.element,em(e,t),Hi(t,r,null,n);var i=t.memoizedState;if(r=i.element,o.isDehydrated)if(o={element:r,isDehydrated:!1,cache:i.cache,pendingSuspenseBoundaries:i.pendingSuspenseBoundaries,transitions:i.transitions},t.updateQueue.baseState=o,t.memoizedState=o,t.flags&256){s=es(Error(T(423)),t),t=yf(e,t,r,n,s);break e}else if(r!==s){s=es(Error(T(424)),t),t=yf(e,t,r,n,s);break e}else for(st=Cn(t.stateNode.containerInfo.firstChild),ot=t,ae=!0,St=null,n=Xp(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(Jr(),r===s){t=rn(e,t,n);break e}Fe(e,t,r,n)}t=t.child}return t;case 5:return tm(t),e===null&&gu(t),r=t.type,s=t.pendingProps,o=e!==null?e.memoizedProps:null,i=s.children,du(r,s)?i=null:o!==null&&du(r,o)&&(t.flags|=32),Cm(e,t),Fe(e,t,i,n),t.child;case 6:return e===null&&gu(t),null;case 13:return Pm(e,t,n);case 4:return Mc(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=Xr(t,null,r,n):Fe(e,t,r,n),t.child;case 11:return r=t.type,s=t.pendingProps,s=t.elementType===r?s:_t(r,s),pf(e,t,r,s,n);case 7:return Fe(e,t,t.pendingProps,n),t.child;case 8:return Fe(e,t,t.pendingProps.children,n),t.child;case 12:return Fe(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,s=t.pendingProps,o=t.memoizedProps,i=s.value,ne(Bi,r._currentValue),r._currentValue=i,o!==null)if(Ct(o.value,i)){if(o.children===s.children&&!Xe.current){t=rn(e,t,n);break e}}else for(o=t.child,o!==null&&(o.return=t);o!==null;){var a=o.dependencies;if(a!==null){i=o.child;for(var l=a.firstContext;l!==null;){if(l.context===r){if(o.tag===1){l=Jt(-1,n&-n),l.tag=2;var u=o.updateQueue;if(u!==null){u=u.shared;var c=u.pending;c===null?l.next=l:(l.next=c.next,c.next=l),u.pending=l}}o.lanes|=n,l=o.alternate,l!==null&&(l.lanes|=n),yu(o.return,n,t),a.lanes|=n;break}l=l.next}}else if(o.tag===10)i=o.type===t.type?null:o.child;else if(o.tag===18){if(i=o.return,i===null)throw Error(T(341));i.lanes|=n,a=i.alternate,a!==null&&(a.lanes|=n),yu(i,n,t),i=o.sibling}else i=o.child;if(i!==null)i.return=o;else for(i=o;i!==null;){if(i===t){i=null;break}if(o=i.sibling,o!==null){o.return=i.return,i=o;break}i=i.return}o=i}Fe(e,t,s.children,n),t=t.child}return t;case 9:return s=t.type,r=t.pendingProps.children,zr(t,n),s=gt(s),r=r(s),t.flags|=1,Fe(e,t,r,n),t.child;case 14:return r=t.type,s=_t(r,t.pendingProps),s=_t(r.type,s),mf(e,t,r,s,n);case 15:return bm(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,s=t.pendingProps,s=t.elementType===r?s:_t(r,s),vi(e,t),t.tag=1,Ze(r)?(e=!0,Yi(t)):e=!1,zr(t,n),xm(t,r,s),wu(t,r,s,n),ku(null,t,r,!0,e,n);case 19:return Nm(e,t,n);case 22:return Dm(e,t,n)}throw Error(T(156,t.tag))};function Hm(e,t){return vp(e,t)}function B0(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function ht(e,t,n,r){return new B0(e,t,n,r)}function Qc(e){return e=e.prototype,!(!e||!e.isReactComponent)}function z0(e){if(typeof e=="function")return Qc(e)?1:0;if(e!=null){if(e=e.$$typeof,e===fc)return 11;if(e===hc)return 14}return 2}function Tn(e,t){var n=e.alternate;return n===null?(n=ht(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function xi(e,t,n,r,s,o){var i=2;if(r=e,typeof e=="function")Qc(e)&&(i=1);else if(typeof e=="string")i=5;else e:switch(e){case Nr:return rr(n.children,s,o,t);case dc:i=8,s|=8;break;case Bl:return e=ht(12,n,t,s|2),e.elementType=Bl,e.lanes=o,e;case zl:return e=ht(13,n,t,s),e.elementType=zl,e.lanes=o,e;case Hl:return e=ht(19,n,t,s),e.elementType=Hl,e.lanes=o,e;case tp:return Na(n,s,o,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case Zh:i=10;break e;case ep:i=9;break e;case fc:i=11;break e;case hc:i=14;break e;case fn:i=16,r=null;break e}throw Error(T(130,e==null?e:typeof e,""))}return t=ht(i,n,t,s),t.elementType=e,t.type=r,t.lanes=o,t}function rr(e,t,n,r){return e=ht(7,e,r,t),e.lanes=n,e}function Na(e,t,n,r){return e=ht(22,e,r,t),e.elementType=tp,e.lanes=n,e.stateNode={isHidden:!1},e}function Dl(e,t,n){return e=ht(6,e,null,t),e.lanes=n,e}function Cl(e,t,n){return t=ht(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function H0(e,t,n,r,s){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=il(0),this.expirationTimes=il(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=il(0),this.identifierPrefix=r,this.onRecoverableError=s,this.mutableSourceEagerHydrationData=null}function Kc(e,t,n,r,s,o,i,a,l){return e=new H0(e,t,n,a,l),t===1?(t=1,o===!0&&(t|=8)):t=0,o=ht(3,null,null,t),e.current=o,o.stateNode=e,o.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},Oc(o),e}function V0(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:Pr,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function Vm(e){if(!e)return In;e=e._reactInternals;e:{if(gr(e)!==e||e.tag!==1)throw Error(T(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(Ze(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(T(171))}if(e.tag===1){var n=e.type;if(Ze(n))return Vp(e,n,t)}return t}function Qm(e,t,n,r,s,o,i,a,l){return e=Kc(n,r,!0,e,s,o,i,a,l),e.context=Vm(null),n=e.current,r=Be(),s=Nn(n),o=Jt(r,s),o.callback=t??null,En(n,o,s),e.current.lanes=s,bo(e,s,r),et(e,r),e}function Ta(e,t,n,r){var s=t.current,o=Be(),i=Nn(s);return n=Vm(n),t.context===null?t.context=n:t.pendingContext=n,t=Jt(o,i),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=En(s,t,i),e!==null&&(Dt(e,s,i,o),mi(e,s,i)),i}function Zi(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function Ef(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function qc(e,t){Ef(e,t),(e=e.alternate)&&Ef(e,t)}function Q0(){return null}var Km=typeof reportError=="function"?reportError:function(e){console.error(e)};function Gc(e){this._internalRoot=e}Oa.prototype.render=Gc.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(T(409));Ta(e,t,null,null)};Oa.prototype.unmount=Gc.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;fr(function(){Ta(null,e,null,null)}),t[tn]=null}};function Oa(e){this._internalRoot=e}Oa.prototype.unstable_scheduleHydration=function(e){if(e){var t=Dp();e={blockedOn:null,target:e,priority:t};for(var n=0;n<gn.length&&t!==0&&t<gn[n].priority;n++);gn.splice(n,0,e),n===0&&Ep(e)}};function Jc(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function Ma(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function Pf(){}function K0(e,t,n,r,s){if(s){if(typeof r=="function"){var o=r;r=function(){var u=Zi(i);o.call(u)}}var i=Qm(t,r,e,0,null,!1,!1,"",Pf);return e._reactRootContainer=i,e[tn]=i.current,ro(e.nodeType===8?e.parentNode:e),fr(),i}for(;s=e.lastChild;)e.removeChild(s);if(typeof r=="function"){var a=r;r=function(){var u=Zi(l);a.call(u)}}var l=Kc(e,0,!1,null,null,!1,!1,"",Pf);return e._reactRootContainer=l,e[tn]=l.current,ro(e.nodeType===8?e.parentNode:e),fr(function(){Ta(t,l,n,r)}),l}function ja(e,t,n,r,s){var o=n._reactRootContainer;if(o){var i=o;if(typeof s=="function"){var a=s;s=function(){var l=Zi(i);a.call(l)}}Ta(t,i,e,s)}else i=K0(n,t,e,s,r);return Zi(i)}Sp=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=js(t.pendingLanes);n!==0&&(gc(t,n|1),et(t,ge()),!(Q&6)&&(ts=ge()+500,Wn()))}break;case 13:fr(function(){var r=nn(e,1);if(r!==null){var s=Be();Dt(r,e,1,s)}}),qc(e,1)}};yc=function(e){if(e.tag===13){var t=nn(e,134217728);if(t!==null){var n=Be();Dt(t,e,134217728,n)}qc(e,134217728)}};bp=function(e){if(e.tag===13){var t=Nn(e),n=nn(e,t);if(n!==null){var r=Be();Dt(n,e,t,r)}qc(e,t)}};Dp=function(){return Z};Cp=function(e,t){var n=Z;try{return Z=e,t()}finally{Z=n}};tu=function(e,t,n){switch(t){case"input":if(Kl(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var s=Sa(r);if(!s)throw Error(T(90));rp(r),Kl(r,s)}}}break;case"textarea":op(e,n);break;case"select":t=n.value,t!=null&&Yr(e,!!n.multiple,t,!1)}};fp=zc;hp=fr;var q0={usingClientEntryPoint:!1,Events:[Co,jr,Sa,cp,dp,zc]},Ds={findFiberByHostInstance:Xn,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},G0={bundleType:Ds.bundleType,version:Ds.version,rendererPackageName:Ds.rendererPackageName,rendererConfig:Ds.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:an.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=gp(e),e===null?null:e.stateNode},findFiberByHostInstance:Ds.findFiberByHostInstance||Q0,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Xo=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Xo.isDisabled&&Xo.supportsFiber)try{wa=Xo.inject(G0),At=Xo}catch{}}lt.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=q0;lt.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!Jc(t))throw Error(T(200));return V0(e,t,null,n)};lt.createRoot=function(e,t){if(!Jc(e))throw Error(T(299));var n=!1,r="",s=Km;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(s=t.onRecoverableError)),t=Kc(e,1,!1,null,null,n,!1,r,s),e[tn]=t.current,ro(e.nodeType===8?e.parentNode:e),new Gc(t)};lt.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(T(188)):(e=Object.keys(e).join(","),Error(T(268,e)));return e=gp(t),e=e===null?null:e.stateNode,e};lt.flushSync=function(e){return fr(e)};lt.hydrate=function(e,t,n){if(!Ma(t))throw Error(T(200));return ja(null,e,t,!0,n)};lt.hydrateRoot=function(e,t,n){if(!Jc(e))throw Error(T(405));var r=n!=null&&n.hydratedSources||null,s=!1,o="",i=Km;if(n!=null&&(n.unstable_strictMode===!0&&(s=!0),n.identifierPrefix!==void 0&&(o=n.identifierPrefix),n.onRecoverableError!==void 0&&(i=n.onRecoverableError)),t=Qm(t,null,e,1,n??null,s,!1,o,i),e[tn]=t.current,ro(e),r)for(e=0;e<r.length;e++)n=r[e],s=n._getVersion,s=s(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,s]:t.mutableSourceEagerHydrationData.push(n,s);return new Oa(t)};lt.render=function(e,t,n){if(!Ma(t))throw Error(T(200));return ja(null,e,t,!1,n)};lt.unmountComponentAtNode=function(e){if(!Ma(e))throw Error(T(40));return e._reactRootContainer?(fr(function(){ja(null,null,e,!1,function(){e._reactRootContainer=null,e[tn]=null})}),!0):!1};lt.unstable_batchedUpdates=zc;lt.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!Ma(n))throw Error(T(200));if(e==null||e._reactInternals===void 0)throw Error(T(38));return ja(e,t,n,!1,r)};lt.version="18.3.1-next-f1338f8080-20240426";function qm(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(qm)}catch(e){console.error(e)}}qm(),qh.exports=lt;var Ra=qh.exports;const J0=Lh(Ra);var Gm,Nf=Ra;Gm=Nf.createRoot,Nf.hydrateRoot;/**
 * @remix-run/router v1.22.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function ho(){return ho=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},ho.apply(this,arguments)}var xn;(function(e){e.Pop="POP",e.Push="PUSH",e.Replace="REPLACE"})(xn||(xn={}));const Tf="popstate";function X0(e){e===void 0&&(e={});function t(r,s){let{pathname:o,search:i,hash:a}=r.location;return Ru("",{pathname:o,search:i,hash:a},s.state&&s.state.usr||null,s.state&&s.state.key||"default")}function n(r,s){return typeof s=="string"?s:Xm(s)}return ew(t,n,null,e)}function xe(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}function Jm(e,t){if(!e){typeof console<"u"&&console.warn(t);try{throw new Error(t)}catch{}}}function Z0(){return Math.random().toString(36).substr(2,8)}function Of(e,t){return{usr:e.state,key:e.key,idx:t}}function Ru(e,t,n,r){return n===void 0&&(n=null),ho({pathname:typeof e=="string"?e:e.pathname,search:"",hash:""},typeof t=="string"?ps(t):t,{state:n,key:t&&t.key||r||Z0()})}function Xm(e){let{pathname:t="/",search:n="",hash:r=""}=e;return n&&n!=="?"&&(t+=n.charAt(0)==="?"?n:"?"+n),r&&r!=="#"&&(t+=r.charAt(0)==="#"?r:"#"+r),t}function ps(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substr(n),e=e.substr(0,n));let r=e.indexOf("?");r>=0&&(t.search=e.substr(r),e=e.substr(0,r)),e&&(t.pathname=e)}return t}function ew(e,t,n,r){r===void 0&&(r={});let{window:s=document.defaultView,v5Compat:o=!1}=r,i=s.history,a=xn.Pop,l=null,u=c();u==null&&(u=0,i.replaceState(ho({},i.state,{idx:u}),""));function c(){return(i.state||{idx:null}).idx}function d(){a=xn.Pop;let x=c(),g=x==null?null:x-u;u=x,l&&l({action:a,location:_.location,delta:g})}function f(x,g){a=xn.Push;let m=Ru(_.location,x,g);u=c()+1;let v=Of(m,u),k=_.createHref(m);try{i.pushState(v,"",k)}catch(E){if(E instanceof DOMException&&E.name==="DataCloneError")throw E;s.location.assign(k)}o&&l&&l({action:a,location:_.location,delta:1})}function p(x,g){a=xn.Replace;let m=Ru(_.location,x,g);u=c();let v=Of(m,u),k=_.createHref(m);i.replaceState(v,"",k),o&&l&&l({action:a,location:_.location,delta:0})}function y(x){let g=s.location.origin!=="null"?s.location.origin:s.location.href,m=typeof x=="string"?x:Xm(x);return m=m.replace(/ $/,"%20"),xe(g,"No window.location.(origin|href) available to create URL for href: "+m),new URL(m,g)}let _={get action(){return a},get location(){return e(s,i)},listen(x){if(l)throw new Error("A history only accepts one active listener");return s.addEventListener(Tf,d),l=x,()=>{s.removeEventListener(Tf,d),l=null}},createHref(x){return t(s,x)},createURL:y,encodeLocation(x){let g=y(x);return{pathname:g.pathname,search:g.search,hash:g.hash}},push:f,replace:p,go(x){return i.go(x)}};return _}var Mf;(function(e){e.data="data",e.deferred="deferred",e.redirect="redirect",e.error="error"})(Mf||(Mf={}));function tw(e,t,n){return n===void 0&&(n="/"),nw(e,t,n,!1)}function nw(e,t,n,r){let s=typeof t=="string"?ps(t):t,o=tg(s.pathname||"/",n);if(o==null)return null;let i=Zm(e);rw(i);let a=null;for(let l=0;a==null&&l<i.length;++l){let u=pw(o);a=fw(i[l],u,r)}return a}function Zm(e,t,n,r){t===void 0&&(t=[]),n===void 0&&(n=[]),r===void 0&&(r="");let s=(o,i,a)=>{let l={relativePath:a===void 0?o.path||"":a,caseSensitive:o.caseSensitive===!0,childrenIndex:i,route:o};l.relativePath.startsWith("/")&&(xe(l.relativePath.startsWith(r),'Absolute route path "'+l.relativePath+'" nested under path '+('"'+r+'" is not valid. An absolute child route path ')+"must start with the combined path of all its parent routes."),l.relativePath=l.relativePath.slice(r.length));let u=sr([r,l.relativePath]),c=n.concat(l);o.children&&o.children.length>0&&(xe(o.index!==!0,"Index routes must not have child routes. Please remove "+('all child routes from route path "'+u+'".')),Zm(o.children,t,c,u)),!(o.path==null&&!o.index)&&t.push({path:u,score:cw(u,o.index),routesMeta:c})};return e.forEach((o,i)=>{var a;if(o.path===""||!((a=o.path)!=null&&a.includes("?")))s(o,i);else for(let l of eg(o.path))s(o,i,l)}),t}function eg(e){let t=e.split("/");if(t.length===0)return[];let[n,...r]=t,s=n.endsWith("?"),o=n.replace(/\?$/,"");if(r.length===0)return s?[o,""]:[o];let i=eg(r.join("/")),a=[];return a.push(...i.map(l=>l===""?o:[o,l].join("/"))),s&&a.push(...i),a.map(l=>e.startsWith("/")&&l===""?"/":l)}function rw(e){e.sort((t,n)=>t.score!==n.score?n.score-t.score:dw(t.routesMeta.map(r=>r.childrenIndex),n.routesMeta.map(r=>r.childrenIndex)))}const sw=/^:[\w-]+$/,ow=3,iw=2,aw=1,lw=10,uw=-2,jf=e=>e==="*";function cw(e,t){let n=e.split("/"),r=n.length;return n.some(jf)&&(r+=uw),t&&(r+=iw),n.filter(s=>!jf(s)).reduce((s,o)=>s+(sw.test(o)?ow:o===""?aw:lw),r)}function dw(e,t){return e.length===t.length&&e.slice(0,-1).every((r,s)=>r===t[s])?e[e.length-1]-t[t.length-1]:0}function fw(e,t,n){let{routesMeta:r}=e,s={},o="/",i=[];for(let a=0;a<r.length;++a){let l=r[a],u=a===r.length-1,c=o==="/"?t:t.slice(o.length)||"/",d=Rf({path:l.relativePath,caseSensitive:l.caseSensitive,end:u},c),f=l.route;if(!d&&u&&n&&!r[r.length-1].route.index&&(d=Rf({path:l.relativePath,caseSensitive:l.caseSensitive,end:!1},c)),!d)return null;Object.assign(s,d.params),i.push({params:s,pathname:sr([o,d.pathname]),pathnameBase:vw(sr([o,d.pathnameBase])),route:f}),d.pathnameBase!=="/"&&(o=sr([o,d.pathnameBase]))}return i}function Rf(e,t){typeof e=="string"&&(e={path:e,caseSensitive:!1,end:!0});let[n,r]=hw(e.path,e.caseSensitive,e.end),s=t.match(n);if(!s)return null;let o=s[0],i=o.replace(/(.)\/+$/,"$1"),a=s.slice(1);return{params:r.reduce((u,c,d)=>{let{paramName:f,isOptional:p}=c;if(f==="*"){let _=a[d]||"";i=o.slice(0,o.length-_.length).replace(/(.)\/+$/,"$1")}const y=a[d];return p&&!y?u[f]=void 0:u[f]=(y||"").replace(/%2F/g,"/"),u},{}),pathname:o,pathnameBase:i,pattern:e}}function hw(e,t,n){t===void 0&&(t=!1),n===void 0&&(n=!0),Jm(e==="*"||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were '+('"'+e.replace(/\*$/,"/*")+'" because the `*` character must ')+"always follow a `/` in the pattern. To get rid of this warning, "+('please change the route path to "'+e.replace(/\*$/,"/*")+'".'));let r=[],s="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(i,a,l)=>(r.push({paramName:a,isOptional:l!=null}),l?"/?([^\\/]+)?":"/([^\\/]+)"));return e.endsWith("*")?(r.push({paramName:"*"}),s+=e==="*"||e==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?s+="\\/*$":e!==""&&e!=="/"&&(s+="(?:(?=\\/|$))"),[new RegExp(s,t?void 0:"i"),r]}function pw(e){try{return e.split("/").map(t=>decodeURIComponent(t).replace(/\//g,"%2F")).join("/")}catch(t){return Jm(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent '+("encoding ("+t+").")),e}}function tg(e,t){if(t==="/")return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,r=e.charAt(n);return r&&r!=="/"?null:e.slice(n)||"/"}function mw(e,t){t===void 0&&(t="/");let{pathname:n,search:r="",hash:s=""}=typeof e=="string"?ps(e):e;return{pathname:n?n.startsWith("/")?n:gw(n,t):t,search:ww(r),hash:_w(s)}}function gw(e,t){let n=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(s=>{s===".."?n.length>1&&n.pop():s!=="."&&n.push(s)}),n.length>1?n.join("/"):"/"}function El(e,t,n,r){return"Cannot include a '"+e+"' character in a manually specified "+("`to."+t+"` field ["+JSON.stringify(r)+"].  Please separate it out to the ")+("`to."+n+"` field. Alternatively you may provide the full path as ")+'a string in <Link to="..."> and the router will parse it for you.'}function yw(e){return e.filter((t,n)=>n===0||t.route.path&&t.route.path.length>0)}function ng(e,t){let n=yw(e);return t?n.map((r,s)=>s===n.length-1?r.pathname:r.pathnameBase):n.map(r=>r.pathnameBase)}function rg(e,t,n,r){r===void 0&&(r=!1);let s;typeof e=="string"?s=ps(e):(s=ho({},e),xe(!s.pathname||!s.pathname.includes("?"),El("?","pathname","search",s)),xe(!s.pathname||!s.pathname.includes("#"),El("#","pathname","hash",s)),xe(!s.search||!s.search.includes("#"),El("#","search","hash",s)));let o=e===""||s.pathname==="",i=o?"/":s.pathname,a;if(i==null)a=n;else{let d=t.length-1;if(!r&&i.startsWith("..")){let f=i.split("/");for(;f[0]==="..";)f.shift(),d-=1;s.pathname=f.join("/")}a=d>=0?t[d]:"/"}let l=mw(s,a),u=i&&i!=="/"&&i.endsWith("/"),c=(o||i===".")&&n.endsWith("/");return!l.pathname.endsWith("/")&&(u||c)&&(l.pathname+="/"),l}const sr=e=>e.join("/").replace(/\/\/+/g,"/"),vw=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),ww=e=>!e||e==="?"?"":e.startsWith("?")?e:"?"+e,_w=e=>!e||e==="#"?"":e.startsWith("#")?e:"#"+e;function xw(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.internal=="boolean"&&"data"in e}const sg=["post","put","patch","delete"];new Set(sg);const kw=["get",...sg];new Set(kw);/**
 * React Router v6.29.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function po(){return po=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},po.apply(this,arguments)}const Xc=S.createContext(null),Sw=S.createContext(null),Po=S.createContext(null),Ia=S.createContext(null),yr=S.createContext({outlet:null,matches:[],isDataRoute:!1}),og=S.createContext(null);function No(){return S.useContext(Ia)!=null}function Zc(){return No()||xe(!1),S.useContext(Ia).location}function ig(e){S.useContext(Po).static||S.useLayoutEffect(e)}function ed(){let{isDataRoute:e}=S.useContext(yr);return e?Lw():bw()}function bw(){No()||xe(!1);let e=S.useContext(Xc),{basename:t,future:n,navigator:r}=S.useContext(Po),{matches:s}=S.useContext(yr),{pathname:o}=Zc(),i=JSON.stringify(ng(s,n.v7_relativeSplatPath)),a=S.useRef(!1);return ig(()=>{a.current=!0}),S.useCallback(function(u,c){if(c===void 0&&(c={}),!a.current)return;if(typeof u=="number"){r.go(u);return}let d=rg(u,JSON.parse(i),o,c.relative==="path");e==null&&t!=="/"&&(d.pathname=d.pathname==="/"?t:sr([t,d.pathname])),(c.replace?r.replace:r.push)(d,c.state,c)},[t,r,i,o,e])}function Dw(e,t){return Cw(e,t)}function Cw(e,t,n,r){No()||xe(!1);let{navigator:s,static:o}=S.useContext(Po),{matches:i}=S.useContext(yr),a=i[i.length-1],l=a?a.params:{};a&&a.pathname;let u=a?a.pathnameBase:"/";a&&a.route;let c=Zc(),d;if(t){var f;let g=typeof t=="string"?ps(t):t;u==="/"||(f=g.pathname)!=null&&f.startsWith(u)||xe(!1),d=g}else d=c;let p=d.pathname||"/",y=p;if(u!=="/"){let g=u.replace(/^\//,"").split("/");y="/"+p.replace(/^\//,"").split("/").slice(g.length).join("/")}let _=!o&&n&&n.matches&&n.matches.length>0?n.matches:tw(e,{pathname:y}),x=Ow(_&&_.map(g=>Object.assign({},g,{params:Object.assign({},l,g.params),pathname:sr([u,s.encodeLocation?s.encodeLocation(g.pathname).pathname:g.pathname]),pathnameBase:g.pathnameBase==="/"?u:sr([u,s.encodeLocation?s.encodeLocation(g.pathnameBase).pathname:g.pathnameBase])})),i,n,r);return t&&x?S.createElement(Ia.Provider,{value:{location:po({pathname:"/",search:"",hash:"",state:null,key:"default"},d),navigationType:xn.Pop}},x):x}function Ew(){let e=Iw(),t=xw(e)?e.status+" "+e.statusText:e instanceof Error?e.message:JSON.stringify(e),n=e instanceof Error?e.stack:null,s={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"};return S.createElement(S.Fragment,null,S.createElement("h2",null,"Unexpected Application Error!"),S.createElement("h3",{style:{fontStyle:"italic"}},t),n?S.createElement("pre",{style:s},n):null,null)}const Pw=S.createElement(Ew,null);class Nw extends S.Component{constructor(t){super(t),this.state={location:t.location,revalidation:t.revalidation,error:t.error}}static getDerivedStateFromError(t){return{error:t}}static getDerivedStateFromProps(t,n){return n.location!==t.location||n.revalidation!=="idle"&&t.revalidation==="idle"?{error:t.error,location:t.location,revalidation:t.revalidation}:{error:t.error!==void 0?t.error:n.error,location:n.location,revalidation:t.revalidation||n.revalidation}}componentDidCatch(t,n){console.error("React Router caught the following error during render",t,n)}render(){return this.state.error!==void 0?S.createElement(yr.Provider,{value:this.props.routeContext},S.createElement(og.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function Tw(e){let{routeContext:t,match:n,children:r}=e,s=S.useContext(Xc);return s&&s.static&&s.staticContext&&(n.route.errorElement||n.route.ErrorBoundary)&&(s.staticContext._deepestRenderedBoundaryId=n.route.id),S.createElement(yr.Provider,{value:t},r)}function Ow(e,t,n,r){var s;if(t===void 0&&(t=[]),n===void 0&&(n=null),r===void 0&&(r=null),e==null){var o;if(!n)return null;if(n.errors)e=n.matches;else if((o=r)!=null&&o.v7_partialHydration&&t.length===0&&!n.initialized&&n.matches.length>0)e=n.matches;else return null}let i=e,a=(s=n)==null?void 0:s.errors;if(a!=null){let c=i.findIndex(d=>d.route.id&&(a==null?void 0:a[d.route.id])!==void 0);c>=0||xe(!1),i=i.slice(0,Math.min(i.length,c+1))}let l=!1,u=-1;if(n&&r&&r.v7_partialHydration)for(let c=0;c<i.length;c++){let d=i[c];if((d.route.HydrateFallback||d.route.hydrateFallbackElement)&&(u=c),d.route.id){let{loaderData:f,errors:p}=n,y=d.route.loader&&f[d.route.id]===void 0&&(!p||p[d.route.id]===void 0);if(d.route.lazy||y){l=!0,u>=0?i=i.slice(0,u+1):i=[i[0]];break}}}return i.reduceRight((c,d,f)=>{let p,y=!1,_=null,x=null;n&&(p=a&&d.route.id?a[d.route.id]:void 0,_=d.route.errorElement||Pw,l&&(u<0&&f===0?(y=!0,x=null):u===f&&(y=!0,x=d.route.hydrateFallbackElement||null)));let g=t.concat(i.slice(0,f+1)),m=()=>{let v;return p?v=_:y?v=x:d.route.Component?v=S.createElement(d.route.Component,null):d.route.element?v=d.route.element:v=c,S.createElement(Tw,{match:d,routeContext:{outlet:c,matches:g,isDataRoute:n!=null},children:v})};return n&&(d.route.ErrorBoundary||d.route.errorElement||f===0)?S.createElement(Nw,{location:n.location,revalidation:n.revalidation,component:_,error:p,children:m(),routeContext:{outlet:null,matches:g,isDataRoute:!0}}):m()},null)}var ag=function(e){return e.UseBlocker="useBlocker",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e}(ag||{}),ea=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}(ea||{});function Mw(e){let t=S.useContext(Xc);return t||xe(!1),t}function jw(e){let t=S.useContext(Sw);return t||xe(!1),t}function Rw(e){let t=S.useContext(yr);return t||xe(!1),t}function lg(e){let t=Rw(),n=t.matches[t.matches.length-1];return n.route.id||xe(!1),n.route.id}function Iw(){var e;let t=S.useContext(og),n=jw(ea.UseRouteError),r=lg(ea.UseRouteError);return t!==void 0?t:(e=n.errors)==null?void 0:e[r]}function Lw(){let{router:e}=Mw(ag.UseNavigateStable),t=lg(ea.UseNavigateStable),n=S.useRef(!1);return ig(()=>{n.current=!0}),S.useCallback(function(s,o){o===void 0&&(o={}),n.current&&(typeof s=="number"?e.navigate(s):e.navigate(s,po({fromRouteId:t},o)))},[e,t])}function Aw(e,t){e==null||e.v7_startTransition,e==null||e.v7_relativeSplatPath}function $w(e){let{to:t,replace:n,state:r,relative:s}=e;No()||xe(!1);let{future:o,static:i}=S.useContext(Po),{matches:a}=S.useContext(yr),{pathname:l}=Zc(),u=ed(),c=rg(t,ng(a,o.v7_relativeSplatPath),l,s==="path"),d=JSON.stringify(c);return S.useEffect(()=>u(JSON.parse(d),{replace:n,state:r,relative:s}),[u,d,s,n,r]),null}function ki(e){xe(!1)}function Fw(e){let{basename:t="/",children:n=null,location:r,navigationType:s=xn.Pop,navigator:o,static:i=!1,future:a}=e;No()&&xe(!1);let l=t.replace(/^\/*/,"/"),u=S.useMemo(()=>({basename:l,navigator:o,static:i,future:po({v7_relativeSplatPath:!1},a)}),[l,a,o,i]);typeof r=="string"&&(r=ps(r));let{pathname:c="/",search:d="",hash:f="",state:p=null,key:y="default"}=r,_=S.useMemo(()=>{let x=tg(c,l);return x==null?null:{location:{pathname:x,search:d,hash:f,state:p,key:y},navigationType:s}},[l,c,d,f,p,y,s]);return _==null?null:S.createElement(Po.Provider,{value:u},S.createElement(Ia.Provider,{children:n,value:_}))}function Yw(e){let{children:t,location:n}=e;return Dw(Iu(t),n)}new Promise(()=>{});function Iu(e,t){t===void 0&&(t=[]);let n=[];return S.Children.forEach(e,(r,s)=>{if(!S.isValidElement(r))return;let o=[...t,s];if(r.type===S.Fragment){n.push.apply(n,Iu(r.props.children,o));return}r.type!==ki&&xe(!1),!r.props.index||!r.props.children||xe(!1);let i={id:r.props.id||o.join("-"),caseSensitive:r.props.caseSensitive,element:r.props.element,Component:r.props.Component,index:r.props.index,path:r.props.path,loader:r.props.loader,action:r.props.action,errorElement:r.props.errorElement,ErrorBoundary:r.props.ErrorBoundary,hasErrorBoundary:r.props.ErrorBoundary!=null||r.props.errorElement!=null,shouldRevalidate:r.props.shouldRevalidate,handle:r.props.handle,lazy:r.props.lazy};r.props.children&&(i.children=Iu(r.props.children,o)),n.push(i)}),n}/**
 * React Router DOM v6.29.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */const Uw="6";try{window.__reactRouterVersion=Uw}catch{}const Ww="startTransition",If=Qh[Ww];function Bw(e){let{basename:t,children:n,future:r,window:s}=e,o=S.useRef();o.current==null&&(o.current=X0({window:s,v5Compat:!0}));let i=o.current,[a,l]=S.useState({action:i.action,location:i.location}),{v7_startTransition:u}=r||{},c=S.useCallback(d=>{u&&If?If(()=>l(d)):l(d)},[l,u]);return S.useLayoutEffect(()=>i.listen(c),[i,c]),S.useEffect(()=>Aw(r),[r]),S.createElement(Fw,{basename:t,children:n,location:a.location,navigationType:a.action,navigator:i,future:r})}var Lf;(function(e){e.UseScrollRestoration="useScrollRestoration",e.UseSubmit="useSubmit",e.UseSubmitFetcher="useSubmitFetcher",e.UseFetcher="useFetcher",e.useViewTransitionState="useViewTransitionState"})(Lf||(Lf={}));var Af;(function(e){e.UseFetcher="useFetcher",e.UseFetchers="useFetchers",e.UseScrollRestoration="useScrollRestoration"})(Af||(Af={}));function ug(e){var t,n,r="";if(typeof e=="string"||typeof e=="number")r+=e;else if(typeof e=="object")if(Array.isArray(e)){var s=e.length;for(t=0;t<s;t++)e[t]&&(n=ug(e[t]))&&(r&&(r+=" "),r+=n)}else for(n in e)e[n]&&(r&&(r+=" "),r+=n);return r}function be(){for(var e,t,n=0,r="",s=arguments.length;n<s;n++)(e=arguments[n])&&(t=ug(e))&&(r&&(r+=" "),r+=t);return r}function hr(e){return e instanceof Date||typeof e=="object"&&Object.prototype.toString.call(e)==="[object Date]"}function R(e){const t=Object.prototype.toString.call(e);return e instanceof Date||typeof e=="object"&&t==="[object Date]"?new e.constructor(+e):typeof e=="number"||t==="[object Number]"||typeof e=="string"||t==="[object String]"?new Date(e):new Date(NaN)}function mo(e){if(!hr(e)&&typeof e!="number")return!1;const t=R(e);return!isNaN(Number(t))}const zw={lessThanXSeconds:{one:"less than a second",other:"less than {{count}} seconds"},xSeconds:{one:"1 second",other:"{{count}} seconds"},halfAMinute:"half a minute",lessThanXMinutes:{one:"less than a minute",other:"less than {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"about 1 hour",other:"about {{count}} hours"},xHours:{one:"1 hour",other:"{{count}} hours"},xDays:{one:"1 day",other:"{{count}} days"},aboutXWeeks:{one:"about 1 week",other:"about {{count}} weeks"},xWeeks:{one:"1 week",other:"{{count}} weeks"},aboutXMonths:{one:"about 1 month",other:"about {{count}} months"},xMonths:{one:"1 month",other:"{{count}} months"},aboutXYears:{one:"about 1 year",other:"about {{count}} years"},xYears:{one:"1 year",other:"{{count}} years"},overXYears:{one:"over 1 year",other:"over {{count}} years"},almostXYears:{one:"almost 1 year",other:"almost {{count}} years"}},Hw=(e,t,n)=>{let r;const s=zw[e];return typeof s=="string"?r=s:t===1?r=s.one:r=s.other.replace("{{count}}",t.toString()),n!=null&&n.addSuffix?n.comparison&&n.comparison>0?"in "+r:r+" ago":r};function Pl(e){return(t={})=>{const n=t.width?String(t.width):e.defaultWidth;return e.formats[n]||e.formats[e.defaultWidth]}}const Vw={full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"},Qw={full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},Kw={full:"{{date}} 'at' {{time}}",long:"{{date}} 'at' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},qw={date:Pl({formats:Vw,defaultWidth:"full"}),time:Pl({formats:Qw,defaultWidth:"full"}),dateTime:Pl({formats:Kw,defaultWidth:"full"})},Gw={lastWeek:"'last' eeee 'at' p",yesterday:"'yesterday at' p",today:"'today at' p",tomorrow:"'tomorrow at' p",nextWeek:"eeee 'at' p",other:"P"},Jw=(e,t,n,r)=>Gw[e];function Cs(e){return(t,n)=>{const r=n!=null&&n.context?String(n.context):"standalone";let s;if(r==="formatting"&&e.formattingValues){const i=e.defaultFormattingWidth||e.defaultWidth,a=n!=null&&n.width?String(n.width):i;s=e.formattingValues[a]||e.formattingValues[i]}else{const i=e.defaultWidth,a=n!=null&&n.width?String(n.width):e.defaultWidth;s=e.values[a]||e.values[i]}const o=e.argumentCallback?e.argumentCallback(t):t;return s[o]}}const Xw={narrow:["B","A"],abbreviated:["BC","AD"],wide:["Before Christ","Anno Domini"]},Zw={narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1st quarter","2nd quarter","3rd quarter","4th quarter"]},e_={narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],wide:["January","February","March","April","May","June","July","August","September","October","November","December"]},t_={narrow:["S","M","T","W","T","F","S"],short:["Su","Mo","Tu","We","Th","Fr","Sa"],abbreviated:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],wide:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"]},n_={narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"}},r_={narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"}},s_=(e,t)=>{const n=Number(e),r=n%100;if(r>20||r<10)switch(r%10){case 1:return n+"st";case 2:return n+"nd";case 3:return n+"rd"}return n+"th"},o_={ordinalNumber:s_,era:Cs({values:Xw,defaultWidth:"wide"}),quarter:Cs({values:Zw,defaultWidth:"wide",argumentCallback:e=>e-1}),month:Cs({values:e_,defaultWidth:"wide"}),day:Cs({values:t_,defaultWidth:"wide"}),dayPeriod:Cs({values:n_,defaultWidth:"wide",formattingValues:r_,defaultFormattingWidth:"wide"})};function Es(e){return(t,n={})=>{const r=n.width,s=r&&e.matchPatterns[r]||e.matchPatterns[e.defaultMatchWidth],o=t.match(s);if(!o)return null;const i=o[0],a=r&&e.parsePatterns[r]||e.parsePatterns[e.defaultParseWidth],l=Array.isArray(a)?a_(a,d=>d.test(i)):i_(a,d=>d.test(i));let u;u=e.valueCallback?e.valueCallback(l):l,u=n.valueCallback?n.valueCallback(u):u;const c=t.slice(i.length);return{value:u,rest:c}}}function i_(e,t){for(const n in e)if(Object.prototype.hasOwnProperty.call(e,n)&&t(e[n]))return n}function a_(e,t){for(let n=0;n<e.length;n++)if(t(e[n]))return n}function l_(e){return(t,n={})=>{const r=t.match(e.matchPattern);if(!r)return null;const s=r[0],o=t.match(e.parsePattern);if(!o)return null;let i=e.valueCallback?e.valueCallback(o[0]):o[0];i=n.valueCallback?n.valueCallback(i):i;const a=t.slice(s.length);return{value:i,rest:a}}}const u_=/^(\d+)(th|st|nd|rd)?/i,c_=/\d+/i,d_={narrow:/^(b|a)/i,abbreviated:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,wide:/^(before christ|before common era|anno domini|common era)/i},f_={any:[/^b/i,/^(a|c)/i]},h_={narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](th|st|nd|rd)? quarter/i},p_={any:[/1/i,/2/i,/3/i,/4/i]},m_={narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i,wide:/^(january|february|march|april|may|june|july|august|september|october|november|december)/i},g_={narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^may/i,/^jun/i,/^jul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},y_={narrow:/^[smtwf]/i,short:/^(su|mo|tu|we|th|fr|sa)/i,abbreviated:/^(sun|mon|tue|wed|thu|fri|sat)/i,wide:/^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i},v_={narrow:[/^s/i,/^m/i,/^t/i,/^w/i,/^t/i,/^f/i,/^s/i],any:[/^su/i,/^m/i,/^tu/i,/^w/i,/^th/i,/^f/i,/^sa/i]},w_={narrow:/^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,any:/^([ap]\.?\s?m\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i},__={any:{am:/^a/i,pm:/^p/i,midnight:/^mi/i,noon:/^no/i,morning:/morning/i,afternoon:/afternoon/i,evening:/evening/i,night:/night/i}},x_={ordinalNumber:l_({matchPattern:u_,parsePattern:c_,valueCallback:e=>parseInt(e,10)}),era:Es({matchPatterns:d_,defaultMatchWidth:"wide",parsePatterns:f_,defaultParseWidth:"any"}),quarter:Es({matchPatterns:h_,defaultMatchWidth:"wide",parsePatterns:p_,defaultParseWidth:"any",valueCallback:e=>e+1}),month:Es({matchPatterns:m_,defaultMatchWidth:"wide",parsePatterns:g_,defaultParseWidth:"any"}),day:Es({matchPatterns:y_,defaultMatchWidth:"wide",parsePatterns:v_,defaultParseWidth:"any"}),dayPeriod:Es({matchPatterns:w_,defaultMatchWidth:"any",parsePatterns:__,defaultParseWidth:"any"})},cg={code:"en-US",formatDistance:Hw,formatLong:qw,formatRelative:Jw,localize:o_,match:x_,options:{weekStartsOn:0,firstWeekContainsDate:1}};let k_={};function vr(){return k_}const dg=6048e5,S_=864e5,La=6e4,Aa=36e5,b_=1e3;function Ft(e){const t=R(e);return t.setHours(0,0,0,0),t}function ta(e){const t=R(e),n=new Date(Date.UTC(t.getFullYear(),t.getMonth(),t.getDate(),t.getHours(),t.getMinutes(),t.getSeconds(),t.getMilliseconds()));return n.setUTCFullYear(t.getFullYear()),+e-+n}function go(e,t){const n=Ft(e),r=Ft(t),s=+n-ta(n),o=+r-ta(r);return Math.round((s-o)/S_)}function J(e,t){return e instanceof Date?new e.constructor(t):new Date(t)}function $a(e){const t=R(e),n=J(e,0);return n.setFullYear(t.getFullYear(),0,1),n.setHours(0,0,0,0),n}function D_(e){const t=R(e);return go(t,$a(t))+1}function sn(e,t){var a,l,u,c;const n=vr(),r=(t==null?void 0:t.weekStartsOn)??((l=(a=t==null?void 0:t.locale)==null?void 0:a.options)==null?void 0:l.weekStartsOn)??n.weekStartsOn??((c=(u=n.locale)==null?void 0:u.options)==null?void 0:c.weekStartsOn)??0,s=R(e),o=s.getDay(),i=(o<r?7:0)+o-r;return s.setDate(s.getDate()-i),s.setHours(0,0,0,0),s}function ns(e){return sn(e,{weekStartsOn:1})}function fg(e){const t=R(e),n=t.getFullYear(),r=J(e,0);r.setFullYear(n+1,0,4),r.setHours(0,0,0,0);const s=ns(r),o=J(e,0);o.setFullYear(n,0,4),o.setHours(0,0,0,0);const i=ns(o);return t.getTime()>=s.getTime()?n+1:t.getTime()>=i.getTime()?n:n-1}function C_(e){const t=fg(e),n=J(e,0);return n.setFullYear(t,0,4),n.setHours(0,0,0,0),ns(n)}function td(e){const t=R(e),n=+ns(t)-+C_(t);return Math.round(n/dg)+1}function nd(e,t){var c,d,f,p;const n=R(e),r=n.getFullYear(),s=vr(),o=(t==null?void 0:t.firstWeekContainsDate)??((d=(c=t==null?void 0:t.locale)==null?void 0:c.options)==null?void 0:d.firstWeekContainsDate)??s.firstWeekContainsDate??((p=(f=s.locale)==null?void 0:f.options)==null?void 0:p.firstWeekContainsDate)??1,i=J(e,0);i.setFullYear(r+1,0,o),i.setHours(0,0,0,0);const a=sn(i,t),l=J(e,0);l.setFullYear(r,0,o),l.setHours(0,0,0,0);const u=sn(l,t);return n.getTime()>=a.getTime()?r+1:n.getTime()>=u.getTime()?r:r-1}function E_(e,t){var a,l,u,c;const n=vr(),r=(t==null?void 0:t.firstWeekContainsDate)??((l=(a=t==null?void 0:t.locale)==null?void 0:a.options)==null?void 0:l.firstWeekContainsDate)??n.firstWeekContainsDate??((c=(u=n.locale)==null?void 0:u.options)==null?void 0:c.firstWeekContainsDate)??1,s=nd(e,t),o=J(e,0);return o.setFullYear(s,0,r),o.setHours(0,0,0,0),sn(o,t)}function hg(e,t){const n=R(e),r=+sn(n,t)-+E_(n,t);return Math.round(r/dg)+1}function G(e,t){const n=e<0?"-":"",r=Math.abs(e).toString().padStart(t,"0");return n+r}const un={y(e,t){const n=e.getFullYear(),r=n>0?n:1-n;return G(t==="yy"?r%100:r,t.length)},M(e,t){const n=e.getMonth();return t==="M"?String(n+1):G(n+1,2)},d(e,t){return G(e.getDate(),t.length)},a(e,t){const n=e.getHours()/12>=1?"pm":"am";switch(t){case"a":case"aa":return n.toUpperCase();case"aaa":return n;case"aaaaa":return n[0];case"aaaa":default:return n==="am"?"a.m.":"p.m."}},h(e,t){return G(e.getHours()%12||12,t.length)},H(e,t){return G(e.getHours(),t.length)},m(e,t){return G(e.getMinutes(),t.length)},s(e,t){return G(e.getSeconds(),t.length)},S(e,t){const n=t.length,r=e.getMilliseconds(),s=Math.trunc(r*Math.pow(10,n-3));return G(s,t.length)}},Sr={am:"am",pm:"pm",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},$f={G:function(e,t,n){const r=e.getFullYear()>0?1:0;switch(t){case"G":case"GG":case"GGG":return n.era(r,{width:"abbreviated"});case"GGGGG":return n.era(r,{width:"narrow"});case"GGGG":default:return n.era(r,{width:"wide"})}},y:function(e,t,n){if(t==="yo"){const r=e.getFullYear(),s=r>0?r:1-r;return n.ordinalNumber(s,{unit:"year"})}return un.y(e,t)},Y:function(e,t,n,r){const s=nd(e,r),o=s>0?s:1-s;if(t==="YY"){const i=o%100;return G(i,2)}return t==="Yo"?n.ordinalNumber(o,{unit:"year"}):G(o,t.length)},R:function(e,t){const n=fg(e);return G(n,t.length)},u:function(e,t){const n=e.getFullYear();return G(n,t.length)},Q:function(e,t,n){const r=Math.ceil((e.getMonth()+1)/3);switch(t){case"Q":return String(r);case"QQ":return G(r,2);case"Qo":return n.ordinalNumber(r,{unit:"quarter"});case"QQQ":return n.quarter(r,{width:"abbreviated",context:"formatting"});case"QQQQQ":return n.quarter(r,{width:"narrow",context:"formatting"});case"QQQQ":default:return n.quarter(r,{width:"wide",context:"formatting"})}},q:function(e,t,n){const r=Math.ceil((e.getMonth()+1)/3);switch(t){case"q":return String(r);case"qq":return G(r,2);case"qo":return n.ordinalNumber(r,{unit:"quarter"});case"qqq":return n.quarter(r,{width:"abbreviated",context:"standalone"});case"qqqqq":return n.quarter(r,{width:"narrow",context:"standalone"});case"qqqq":default:return n.quarter(r,{width:"wide",context:"standalone"})}},M:function(e,t,n){const r=e.getMonth();switch(t){case"M":case"MM":return un.M(e,t);case"Mo":return n.ordinalNumber(r+1,{unit:"month"});case"MMM":return n.month(r,{width:"abbreviated",context:"formatting"});case"MMMMM":return n.month(r,{width:"narrow",context:"formatting"});case"MMMM":default:return n.month(r,{width:"wide",context:"formatting"})}},L:function(e,t,n){const r=e.getMonth();switch(t){case"L":return String(r+1);case"LL":return G(r+1,2);case"Lo":return n.ordinalNumber(r+1,{unit:"month"});case"LLL":return n.month(r,{width:"abbreviated",context:"standalone"});case"LLLLL":return n.month(r,{width:"narrow",context:"standalone"});case"LLLL":default:return n.month(r,{width:"wide",context:"standalone"})}},w:function(e,t,n,r){const s=hg(e,r);return t==="wo"?n.ordinalNumber(s,{unit:"week"}):G(s,t.length)},I:function(e,t,n){const r=td(e);return t==="Io"?n.ordinalNumber(r,{unit:"week"}):G(r,t.length)},d:function(e,t,n){return t==="do"?n.ordinalNumber(e.getDate(),{unit:"date"}):un.d(e,t)},D:function(e,t,n){const r=D_(e);return t==="Do"?n.ordinalNumber(r,{unit:"dayOfYear"}):G(r,t.length)},E:function(e,t,n){const r=e.getDay();switch(t){case"E":case"EE":case"EEE":return n.day(r,{width:"abbreviated",context:"formatting"});case"EEEEE":return n.day(r,{width:"narrow",context:"formatting"});case"EEEEEE":return n.day(r,{width:"short",context:"formatting"});case"EEEE":default:return n.day(r,{width:"wide",context:"formatting"})}},e:function(e,t,n,r){const s=e.getDay(),o=(s-r.weekStartsOn+8)%7||7;switch(t){case"e":return String(o);case"ee":return G(o,2);case"eo":return n.ordinalNumber(o,{unit:"day"});case"eee":return n.day(s,{width:"abbreviated",context:"formatting"});case"eeeee":return n.day(s,{width:"narrow",context:"formatting"});case"eeeeee":return n.day(s,{width:"short",context:"formatting"});case"eeee":default:return n.day(s,{width:"wide",context:"formatting"})}},c:function(e,t,n,r){const s=e.getDay(),o=(s-r.weekStartsOn+8)%7||7;switch(t){case"c":return String(o);case"cc":return G(o,t.length);case"co":return n.ordinalNumber(o,{unit:"day"});case"ccc":return n.day(s,{width:"abbreviated",context:"standalone"});case"ccccc":return n.day(s,{width:"narrow",context:"standalone"});case"cccccc":return n.day(s,{width:"short",context:"standalone"});case"cccc":default:return n.day(s,{width:"wide",context:"standalone"})}},i:function(e,t,n){const r=e.getDay(),s=r===0?7:r;switch(t){case"i":return String(s);case"ii":return G(s,t.length);case"io":return n.ordinalNumber(s,{unit:"day"});case"iii":return n.day(r,{width:"abbreviated",context:"formatting"});case"iiiii":return n.day(r,{width:"narrow",context:"formatting"});case"iiiiii":return n.day(r,{width:"short",context:"formatting"});case"iiii":default:return n.day(r,{width:"wide",context:"formatting"})}},a:function(e,t,n){const s=e.getHours()/12>=1?"pm":"am";switch(t){case"a":case"aa":return n.dayPeriod(s,{width:"abbreviated",context:"formatting"});case"aaa":return n.dayPeriod(s,{width:"abbreviated",context:"formatting"}).toLowerCase();case"aaaaa":return n.dayPeriod(s,{width:"narrow",context:"formatting"});case"aaaa":default:return n.dayPeriod(s,{width:"wide",context:"formatting"})}},b:function(e,t,n){const r=e.getHours();let s;switch(r===12?s=Sr.noon:r===0?s=Sr.midnight:s=r/12>=1?"pm":"am",t){case"b":case"bb":return n.dayPeriod(s,{width:"abbreviated",context:"formatting"});case"bbb":return n.dayPeriod(s,{width:"abbreviated",context:"formatting"}).toLowerCase();case"bbbbb":return n.dayPeriod(s,{width:"narrow",context:"formatting"});case"bbbb":default:return n.dayPeriod(s,{width:"wide",context:"formatting"})}},B:function(e,t,n){const r=e.getHours();let s;switch(r>=17?s=Sr.evening:r>=12?s=Sr.afternoon:r>=4?s=Sr.morning:s=Sr.night,t){case"B":case"BB":case"BBB":return n.dayPeriod(s,{width:"abbreviated",context:"formatting"});case"BBBBB":return n.dayPeriod(s,{width:"narrow",context:"formatting"});case"BBBB":default:return n.dayPeriod(s,{width:"wide",context:"formatting"})}},h:function(e,t,n){if(t==="ho"){let r=e.getHours()%12;return r===0&&(r=12),n.ordinalNumber(r,{unit:"hour"})}return un.h(e,t)},H:function(e,t,n){return t==="Ho"?n.ordinalNumber(e.getHours(),{unit:"hour"}):un.H(e,t)},K:function(e,t,n){const r=e.getHours()%12;return t==="Ko"?n.ordinalNumber(r,{unit:"hour"}):G(r,t.length)},k:function(e,t,n){let r=e.getHours();return r===0&&(r=24),t==="ko"?n.ordinalNumber(r,{unit:"hour"}):G(r,t.length)},m:function(e,t,n){return t==="mo"?n.ordinalNumber(e.getMinutes(),{unit:"minute"}):un.m(e,t)},s:function(e,t,n){return t==="so"?n.ordinalNumber(e.getSeconds(),{unit:"second"}):un.s(e,t)},S:function(e,t){return un.S(e,t)},X:function(e,t,n){const r=e.getTimezoneOffset();if(r===0)return"Z";switch(t){case"X":return Yf(r);case"XXXX":case"XX":return qn(r);case"XXXXX":case"XXX":default:return qn(r,":")}},x:function(e,t,n){const r=e.getTimezoneOffset();switch(t){case"x":return Yf(r);case"xxxx":case"xx":return qn(r);case"xxxxx":case"xxx":default:return qn(r,":")}},O:function(e,t,n){const r=e.getTimezoneOffset();switch(t){case"O":case"OO":case"OOO":return"GMT"+Ff(r,":");case"OOOO":default:return"GMT"+qn(r,":")}},z:function(e,t,n){const r=e.getTimezoneOffset();switch(t){case"z":case"zz":case"zzz":return"GMT"+Ff(r,":");case"zzzz":default:return"GMT"+qn(r,":")}},t:function(e,t,n){const r=Math.trunc(e.getTime()/1e3);return G(r,t.length)},T:function(e,t,n){const r=e.getTime();return G(r,t.length)}};function Ff(e,t=""){const n=e>0?"-":"+",r=Math.abs(e),s=Math.trunc(r/60),o=r%60;return o===0?n+String(s):n+String(s)+t+G(o,2)}function Yf(e,t){return e%60===0?(e>0?"-":"+")+G(Math.abs(e)/60,2):qn(e,t)}function qn(e,t=""){const n=e>0?"-":"+",r=Math.abs(e),s=G(Math.trunc(r/60),2),o=G(r%60,2);return n+s+t+o}const Uf=(e,t)=>{switch(e){case"P":return t.date({width:"short"});case"PP":return t.date({width:"medium"});case"PPP":return t.date({width:"long"});case"PPPP":default:return t.date({width:"full"})}},pg=(e,t)=>{switch(e){case"p":return t.time({width:"short"});case"pp":return t.time({width:"medium"});case"ppp":return t.time({width:"long"});case"pppp":default:return t.time({width:"full"})}},P_=(e,t)=>{const n=e.match(/(P+)(p+)?/)||[],r=n[1],s=n[2];if(!s)return Uf(e,t);let o;switch(r){case"P":o=t.dateTime({width:"short"});break;case"PP":o=t.dateTime({width:"medium"});break;case"PPP":o=t.dateTime({width:"long"});break;case"PPPP":default:o=t.dateTime({width:"full"});break}return o.replace("{{date}}",Uf(r,t)).replace("{{time}}",pg(s,t))},na={p:pg,P:P_},N_=/^D+$/,T_=/^Y+$/,O_=["D","DD","YY","YYYY"];function mg(e){return N_.test(e)}function gg(e){return T_.test(e)}function Lu(e,t,n){const r=M_(e,t,n);if(console.warn(r),O_.includes(e))throw new RangeError(r)}function M_(e,t,n){const r=e[0]==="Y"?"years":"days of the month";return`Use \`${e.toLowerCase()}\` instead of \`${e}\` (in \`${t}\`) for formatting ${r} to the input \`${n}\`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md`}const j_=/[yYQqMLwIdDecihHKkms]o|(\w)\1*|''|'(''|[^'])+('|$)|./g,R_=/P+p+|P+|p+|''|'(''|[^'])+('|$)|./g,I_=/^'([^]*?)'?$/,L_=/''/g,A_=/[a-zA-Z]/;function qe(e,t,n){var c,d,f,p,y,_,x,g;const r=vr(),s=(n==null?void 0:n.locale)??r.locale??cg,o=(n==null?void 0:n.firstWeekContainsDate)??((d=(c=n==null?void 0:n.locale)==null?void 0:c.options)==null?void 0:d.firstWeekContainsDate)??r.firstWeekContainsDate??((p=(f=r.locale)==null?void 0:f.options)==null?void 0:p.firstWeekContainsDate)??1,i=(n==null?void 0:n.weekStartsOn)??((_=(y=n==null?void 0:n.locale)==null?void 0:y.options)==null?void 0:_.weekStartsOn)??r.weekStartsOn??((g=(x=r.locale)==null?void 0:x.options)==null?void 0:g.weekStartsOn)??0,a=R(e);if(!mo(a))throw new RangeError("Invalid time value");let l=t.match(R_).map(m=>{const v=m[0];if(v==="p"||v==="P"){const k=na[v];return k(m,s.formatLong)}return m}).join("").match(j_).map(m=>{if(m==="''")return{isToken:!1,value:"'"};const v=m[0];if(v==="'")return{isToken:!1,value:$_(m)};if($f[v])return{isToken:!0,value:m};if(v.match(A_))throw new RangeError("Format string contains an unescaped latin alphabet character `"+v+"`");return{isToken:!1,value:m}});s.localize.preprocessor&&(l=s.localize.preprocessor(a,l));const u={firstWeekContainsDate:o,weekStartsOn:i,locale:s};return l.map(m=>{if(!m.isToken)return m.value;const v=m.value;(!(n!=null&&n.useAdditionalWeekYearTokens)&&gg(v)||!(n!=null&&n.useAdditionalDayOfYearTokens)&&mg(v))&&Lu(v,t,String(e));const k=$f[v[0]];return k(a,v,s.localize,u)}).join("")}function $_(e){const t=e.match(I_);return t?t[1].replace(L_,"'"):e}function rd(e,t){const n=+R(e);return J(e,n+t)}function Au(e,t){return rd(e,t*La)}function F_(e,t){return rd(e,t*Aa)}function Bn(e,t){const n=R(e);return isNaN(t)?J(e,NaN):(t&&n.setDate(n.getDate()+t),n)}function ra(e,t){const n=t*7;return Bn(e,n)}function Et(e,t){const n=R(e);if(isNaN(t))return J(e,NaN);if(!t)return n;const r=n.getDate(),s=J(e,n.getTime());s.setMonth(n.getMonth()+t+1,0);const o=s.getDate();return r>=o?s:(n.setFullYear(s.getFullYear(),s.getMonth(),r),n)}function sd(e,t){const n=t*3;return Et(e,n)}function Xt(e,t){return Et(e,t*12)}function Y_(e,t){return Bn(e,-t)}function Wf(e,t){return ra(e,-t)}function rs(e,t){return Et(e,-t)}function yg(e,t){return sd(e,-t)}function ss(e,t){return Xt(e,-t)}function Zt(e){return R(e).getSeconds()}function Yt(e){return R(e).getMinutes()}function Ut(e){return R(e).getHours()}function U_(e){return R(e).getDay()}function Bf(e){return R(e).getDate()}function Ue(e){return R(e).getMonth()}function or(e){const t=R(e);return Math.trunc(t.getMonth()/3)+1}function W(e){return R(e).getFullYear()}function $u(e){return R(e).getTime()}function Si(e,t){const n=R(e);return n.setSeconds(t),n}function bi(e,t){const n=R(e);return n.setMinutes(t),n}function Di(e,t){const n=R(e);return n.setHours(t),n}function W_(e){const t=R(e),n=t.getFullYear(),r=t.getMonth(),s=J(e,0);return s.setFullYear(n,r+1,0),s.setHours(0,0,0,0),s.getDate()}function Ye(e,t){const n=R(e),r=n.getFullYear(),s=n.getDate(),o=J(e,0);o.setFullYear(r,t,15),o.setHours(0,0,0,0);const i=W_(o);return n.setMonth(t,Math.min(s,i)),n}function Er(e,t){const n=R(e),r=Math.trunc(n.getMonth()/3)+1,s=t-r;return Ye(n,n.getMonth()+s*3)}function Ot(e,t){const n=R(e);return isNaN(+n)?J(e,NaN):(n.setFullYear(t),n)}function zf(e){let t;return e.forEach(n=>{const r=R(n);(!t||t>r||isNaN(+r))&&(t=r)}),t||new Date(NaN)}function Hf(e){let t;return e.forEach(function(n){const r=R(n);(t===void 0||t<r||isNaN(Number(r)))&&(t=r)}),t||new Date(NaN)}function sa(e,t){const n=R(e),r=R(t),s=n.getFullYear()-r.getFullYear(),o=n.getMonth()-r.getMonth();return s*12+o}function oa(e,t){const n=R(e),r=R(t);return n.getFullYear()-r.getFullYear()}function ia(e,t){const n=R(e),r=R(t),s=n.getFullYear()-r.getFullYear(),o=or(n)-or(r);return s*4+o}function vg(e){const t=R(e);return t.setDate(1),t.setHours(0,0,0,0),t}function Fu(e){const t=R(e),n=t.getMonth(),r=n-n%3;return t.setMonth(r,1),t.setHours(0,0,0,0),t}function Yu(e){const t=R(e);return t.setHours(23,59,59,999),t}function B_(e,t){var a,l;const n=vr(),r=n.weekStartsOn??((l=(a=n.locale)==null?void 0:a.options)==null?void 0:l.weekStartsOn)??0,s=R(e),o=s.getDay(),i=(o<r?-7:0)+6-(o-r);return s.setDate(s.getDate()+i),s.setHours(23,59,59,999),s}function z_(e){const t=R(e),n=t.getMonth();return t.setFullYear(t.getFullYear(),n+1,0),t.setHours(23,59,59,999),t}function wg(e){const t=R(e),n=t.getFullYear();return t.setFullYear(n+1,0,0),t.setHours(23,59,59,999),t}function H_(e,t){const n=R(e),r=R(t);return+n==+r}function V_(e,t){const n=Ft(e),r=Ft(t);return+n==+r}function Q_(e,t){const n=R(e),r=R(t);return n.getFullYear()===r.getFullYear()&&n.getMonth()===r.getMonth()}function K_(e,t){const n=R(e),r=R(t);return n.getFullYear()===r.getFullYear()}function q_(e,t){const n=Fu(e),r=Fu(t);return+n==+r}function Ln(e,t){const n=R(e),r=R(t);return n.getTime()>r.getTime()}function An(e,t){const n=R(e),r=R(t);return+n<+r}function yo(e,t){const n=+R(e),[r,s]=[+R(t.start),+R(t.end)].sort((o,i)=>o-i);return n>=r&&n<=s}function G_(){return Object.assign({},vr())}function J_(e,t){const n=t instanceof Date?J(t,0):new t(0);return n.setFullYear(e.getFullYear(),e.getMonth(),e.getDate()),n.setHours(e.getHours(),e.getMinutes(),e.getSeconds(),e.getMilliseconds()),n}const X_=10;class _g{constructor(){j(this,"subPriority",0)}validate(t,n){return!0}}class Z_ extends _g{constructor(t,n,r,s,o){super(),this.value=t,this.validateValue=n,this.setValue=r,this.priority=s,o&&(this.subPriority=o)}validate(t,n){return this.validateValue(t,this.value,n)}set(t,n,r){return this.setValue(t,n,this.value,r)}}class ex extends _g{constructor(){super(...arguments);j(this,"priority",X_);j(this,"subPriority",-1)}set(n,r){return r.timestampIsSet?n:J(n,J_(n,Date))}}class q{run(t,n,r,s){const o=this.parse(t,n,r,s);return o?{setter:new Z_(o.value,this.validate,this.set,this.priority,this.subPriority),rest:o.rest}:null}validate(t,n,r){return!0}}class tx extends q{constructor(){super(...arguments);j(this,"priority",140);j(this,"incompatibleTokens",["R","u","t","T"])}parse(n,r,s){switch(r){case"G":case"GG":case"GGG":return s.era(n,{width:"abbreviated"})||s.era(n,{width:"narrow"});case"GGGGG":return s.era(n,{width:"narrow"});case"GGGG":default:return s.era(n,{width:"wide"})||s.era(n,{width:"abbreviated"})||s.era(n,{width:"narrow"})}}set(n,r,s){return r.era=s,n.setFullYear(s,0,1),n.setHours(0,0,0,0),n}}const ye={month:/^(1[0-2]|0?\d)/,date:/^(3[0-1]|[0-2]?\d)/,dayOfYear:/^(36[0-6]|3[0-5]\d|[0-2]?\d?\d)/,week:/^(5[0-3]|[0-4]?\d)/,hour23h:/^(2[0-3]|[0-1]?\d)/,hour24h:/^(2[0-4]|[0-1]?\d)/,hour11h:/^(1[0-1]|0?\d)/,hour12h:/^(1[0-2]|0?\d)/,minute:/^[0-5]?\d/,second:/^[0-5]?\d/,singleDigit:/^\d/,twoDigits:/^\d{1,2}/,threeDigits:/^\d{1,3}/,fourDigits:/^\d{1,4}/,anyDigitsSigned:/^-?\d+/,singleDigitSigned:/^-?\d/,twoDigitsSigned:/^-?\d{1,2}/,threeDigitsSigned:/^-?\d{1,3}/,fourDigitsSigned:/^-?\d{1,4}/},Rt={basicOptionalMinutes:/^([+-])(\d{2})(\d{2})?|Z/,basic:/^([+-])(\d{2})(\d{2})|Z/,basicOptionalSeconds:/^([+-])(\d{2})(\d{2})((\d{2}))?|Z/,extended:/^([+-])(\d{2}):(\d{2})|Z/,extendedOptionalSeconds:/^([+-])(\d{2}):(\d{2})(:(\d{2}))?|Z/};function ve(e,t){return e&&{value:t(e.value),rest:e.rest}}function ce(e,t){const n=t.match(e);return n?{value:parseInt(n[0],10),rest:t.slice(n[0].length)}:null}function It(e,t){const n=t.match(e);if(!n)return null;if(n[0]==="Z")return{value:0,rest:t.slice(1)};const r=n[1]==="+"?1:-1,s=n[2]?parseInt(n[2],10):0,o=n[3]?parseInt(n[3],10):0,i=n[5]?parseInt(n[5],10):0;return{value:r*(s*Aa+o*La+i*b_),rest:t.slice(n[0].length)}}function xg(e){return ce(ye.anyDigitsSigned,e)}function pe(e,t){switch(e){case 1:return ce(ye.singleDigit,t);case 2:return ce(ye.twoDigits,t);case 3:return ce(ye.threeDigits,t);case 4:return ce(ye.fourDigits,t);default:return ce(new RegExp("^\\d{1,"+e+"}"),t)}}function aa(e,t){switch(e){case 1:return ce(ye.singleDigitSigned,t);case 2:return ce(ye.twoDigitsSigned,t);case 3:return ce(ye.threeDigitsSigned,t);case 4:return ce(ye.fourDigitsSigned,t);default:return ce(new RegExp("^-?\\d{1,"+e+"}"),t)}}function od(e){switch(e){case"morning":return 4;case"evening":return 17;case"pm":case"noon":case"afternoon":return 12;case"am":case"midnight":case"night":default:return 0}}function kg(e,t){const n=t>0,r=n?t:1-t;let s;if(r<=50)s=e||100;else{const o=r+50,i=Math.trunc(o/100)*100,a=e>=o%100;s=e+i-(a?100:0)}return n?s:1-s}function Sg(e){return e%400===0||e%4===0&&e%100!==0}class nx extends q{constructor(){super(...arguments);j(this,"priority",130);j(this,"incompatibleTokens",["Y","R","u","w","I","i","e","c","t","T"])}parse(n,r,s){const o=i=>({year:i,isTwoDigitYear:r==="yy"});switch(r){case"y":return ve(pe(4,n),o);case"yo":return ve(s.ordinalNumber(n,{unit:"year"}),o);default:return ve(pe(r.length,n),o)}}validate(n,r){return r.isTwoDigitYear||r.year>0}set(n,r,s){const o=n.getFullYear();if(s.isTwoDigitYear){const a=kg(s.year,o);return n.setFullYear(a,0,1),n.setHours(0,0,0,0),n}const i=!("era"in r)||r.era===1?s.year:1-s.year;return n.setFullYear(i,0,1),n.setHours(0,0,0,0),n}}class rx extends q{constructor(){super(...arguments);j(this,"priority",130);j(this,"incompatibleTokens",["y","R","u","Q","q","M","L","I","d","D","i","t","T"])}parse(n,r,s){const o=i=>({year:i,isTwoDigitYear:r==="YY"});switch(r){case"Y":return ve(pe(4,n),o);case"Yo":return ve(s.ordinalNumber(n,{unit:"year"}),o);default:return ve(pe(r.length,n),o)}}validate(n,r){return r.isTwoDigitYear||r.year>0}set(n,r,s,o){const i=nd(n,o);if(s.isTwoDigitYear){const l=kg(s.year,i);return n.setFullYear(l,0,o.firstWeekContainsDate),n.setHours(0,0,0,0),sn(n,o)}const a=!("era"in r)||r.era===1?s.year:1-s.year;return n.setFullYear(a,0,o.firstWeekContainsDate),n.setHours(0,0,0,0),sn(n,o)}}class sx extends q{constructor(){super(...arguments);j(this,"priority",130);j(this,"incompatibleTokens",["G","y","Y","u","Q","q","M","L","w","d","D","e","c","t","T"])}parse(n,r){return aa(r==="R"?4:r.length,n)}set(n,r,s){const o=J(n,0);return o.setFullYear(s,0,4),o.setHours(0,0,0,0),ns(o)}}class ox extends q{constructor(){super(...arguments);j(this,"priority",130);j(this,"incompatibleTokens",["G","y","Y","R","w","I","i","e","c","t","T"])}parse(n,r){return aa(r==="u"?4:r.length,n)}set(n,r,s){return n.setFullYear(s,0,1),n.setHours(0,0,0,0),n}}class ix extends q{constructor(){super(...arguments);j(this,"priority",120);j(this,"incompatibleTokens",["Y","R","q","M","L","w","I","d","D","i","e","c","t","T"])}parse(n,r,s){switch(r){case"Q":case"QQ":return pe(r.length,n);case"Qo":return s.ordinalNumber(n,{unit:"quarter"});case"QQQ":return s.quarter(n,{width:"abbreviated",context:"formatting"})||s.quarter(n,{width:"narrow",context:"formatting"});case"QQQQQ":return s.quarter(n,{width:"narrow",context:"formatting"});case"QQQQ":default:return s.quarter(n,{width:"wide",context:"formatting"})||s.quarter(n,{width:"abbreviated",context:"formatting"})||s.quarter(n,{width:"narrow",context:"formatting"})}}validate(n,r){return r>=1&&r<=4}set(n,r,s){return n.setMonth((s-1)*3,1),n.setHours(0,0,0,0),n}}class ax extends q{constructor(){super(...arguments);j(this,"priority",120);j(this,"incompatibleTokens",["Y","R","Q","M","L","w","I","d","D","i","e","c","t","T"])}parse(n,r,s){switch(r){case"q":case"qq":return pe(r.length,n);case"qo":return s.ordinalNumber(n,{unit:"quarter"});case"qqq":return s.quarter(n,{width:"abbreviated",context:"standalone"})||s.quarter(n,{width:"narrow",context:"standalone"});case"qqqqq":return s.quarter(n,{width:"narrow",context:"standalone"});case"qqqq":default:return s.quarter(n,{width:"wide",context:"standalone"})||s.quarter(n,{width:"abbreviated",context:"standalone"})||s.quarter(n,{width:"narrow",context:"standalone"})}}validate(n,r){return r>=1&&r<=4}set(n,r,s){return n.setMonth((s-1)*3,1),n.setHours(0,0,0,0),n}}class lx extends q{constructor(){super(...arguments);j(this,"incompatibleTokens",["Y","R","q","Q","L","w","I","D","i","e","c","t","T"]);j(this,"priority",110)}parse(n,r,s){const o=i=>i-1;switch(r){case"M":return ve(ce(ye.month,n),o);case"MM":return ve(pe(2,n),o);case"Mo":return ve(s.ordinalNumber(n,{unit:"month"}),o);case"MMM":return s.month(n,{width:"abbreviated",context:"formatting"})||s.month(n,{width:"narrow",context:"formatting"});case"MMMMM":return s.month(n,{width:"narrow",context:"formatting"});case"MMMM":default:return s.month(n,{width:"wide",context:"formatting"})||s.month(n,{width:"abbreviated",context:"formatting"})||s.month(n,{width:"narrow",context:"formatting"})}}validate(n,r){return r>=0&&r<=11}set(n,r,s){return n.setMonth(s,1),n.setHours(0,0,0,0),n}}class ux extends q{constructor(){super(...arguments);j(this,"priority",110);j(this,"incompatibleTokens",["Y","R","q","Q","M","w","I","D","i","e","c","t","T"])}parse(n,r,s){const o=i=>i-1;switch(r){case"L":return ve(ce(ye.month,n),o);case"LL":return ve(pe(2,n),o);case"Lo":return ve(s.ordinalNumber(n,{unit:"month"}),o);case"LLL":return s.month(n,{width:"abbreviated",context:"standalone"})||s.month(n,{width:"narrow",context:"standalone"});case"LLLLL":return s.month(n,{width:"narrow",context:"standalone"});case"LLLL":default:return s.month(n,{width:"wide",context:"standalone"})||s.month(n,{width:"abbreviated",context:"standalone"})||s.month(n,{width:"narrow",context:"standalone"})}}validate(n,r){return r>=0&&r<=11}set(n,r,s){return n.setMonth(s,1),n.setHours(0,0,0,0),n}}function cx(e,t,n){const r=R(e),s=hg(r,n)-t;return r.setDate(r.getDate()-s*7),r}class dx extends q{constructor(){super(...arguments);j(this,"priority",100);j(this,"incompatibleTokens",["y","R","u","q","Q","M","L","I","d","D","i","t","T"])}parse(n,r,s){switch(r){case"w":return ce(ye.week,n);case"wo":return s.ordinalNumber(n,{unit:"week"});default:return pe(r.length,n)}}validate(n,r){return r>=1&&r<=53}set(n,r,s,o){return sn(cx(n,s,o),o)}}function fx(e,t){const n=R(e),r=td(n)-t;return n.setDate(n.getDate()-r*7),n}class hx extends q{constructor(){super(...arguments);j(this,"priority",100);j(this,"incompatibleTokens",["y","Y","u","q","Q","M","L","w","d","D","e","c","t","T"])}parse(n,r,s){switch(r){case"I":return ce(ye.week,n);case"Io":return s.ordinalNumber(n,{unit:"week"});default:return pe(r.length,n)}}validate(n,r){return r>=1&&r<=53}set(n,r,s){return ns(fx(n,s))}}const px=[31,28,31,30,31,30,31,31,30,31,30,31],mx=[31,29,31,30,31,30,31,31,30,31,30,31];class gx extends q{constructor(){super(...arguments);j(this,"priority",90);j(this,"subPriority",1);j(this,"incompatibleTokens",["Y","R","q","Q","w","I","D","i","e","c","t","T"])}parse(n,r,s){switch(r){case"d":return ce(ye.date,n);case"do":return s.ordinalNumber(n,{unit:"date"});default:return pe(r.length,n)}}validate(n,r){const s=n.getFullYear(),o=Sg(s),i=n.getMonth();return o?r>=1&&r<=mx[i]:r>=1&&r<=px[i]}set(n,r,s){return n.setDate(s),n.setHours(0,0,0,0),n}}class yx extends q{constructor(){super(...arguments);j(this,"priority",90);j(this,"subpriority",1);j(this,"incompatibleTokens",["Y","R","q","Q","M","L","w","I","d","E","i","e","c","t","T"])}parse(n,r,s){switch(r){case"D":case"DD":return ce(ye.dayOfYear,n);case"Do":return s.ordinalNumber(n,{unit:"date"});default:return pe(r.length,n)}}validate(n,r){const s=n.getFullYear();return Sg(s)?r>=1&&r<=366:r>=1&&r<=365}set(n,r,s){return n.setMonth(0,s),n.setHours(0,0,0,0),n}}function id(e,t,n){var d,f,p,y;const r=vr(),s=(n==null?void 0:n.weekStartsOn)??((f=(d=n==null?void 0:n.locale)==null?void 0:d.options)==null?void 0:f.weekStartsOn)??r.weekStartsOn??((y=(p=r.locale)==null?void 0:p.options)==null?void 0:y.weekStartsOn)??0,o=R(e),i=o.getDay(),l=(t%7+7)%7,u=7-s,c=t<0||t>6?t-(i+u)%7:(l+u)%7-(i+u)%7;return Bn(o,c)}class vx extends q{constructor(){super(...arguments);j(this,"priority",90);j(this,"incompatibleTokens",["D","i","e","c","t","T"])}parse(n,r,s){switch(r){case"E":case"EE":case"EEE":return s.day(n,{width:"abbreviated",context:"formatting"})||s.day(n,{width:"short",context:"formatting"})||s.day(n,{width:"narrow",context:"formatting"});case"EEEEE":return s.day(n,{width:"narrow",context:"formatting"});case"EEEEEE":return s.day(n,{width:"short",context:"formatting"})||s.day(n,{width:"narrow",context:"formatting"});case"EEEE":default:return s.day(n,{width:"wide",context:"formatting"})||s.day(n,{width:"abbreviated",context:"formatting"})||s.day(n,{width:"short",context:"formatting"})||s.day(n,{width:"narrow",context:"formatting"})}}validate(n,r){return r>=0&&r<=6}set(n,r,s,o){return n=id(n,s,o),n.setHours(0,0,0,0),n}}class wx extends q{constructor(){super(...arguments);j(this,"priority",90);j(this,"incompatibleTokens",["y","R","u","q","Q","M","L","I","d","D","E","i","c","t","T"])}parse(n,r,s,o){const i=a=>{const l=Math.floor((a-1)/7)*7;return(a+o.weekStartsOn+6)%7+l};switch(r){case"e":case"ee":return ve(pe(r.length,n),i);case"eo":return ve(s.ordinalNumber(n,{unit:"day"}),i);case"eee":return s.day(n,{width:"abbreviated",context:"formatting"})||s.day(n,{width:"short",context:"formatting"})||s.day(n,{width:"narrow",context:"formatting"});case"eeeee":return s.day(n,{width:"narrow",context:"formatting"});case"eeeeee":return s.day(n,{width:"short",context:"formatting"})||s.day(n,{width:"narrow",context:"formatting"});case"eeee":default:return s.day(n,{width:"wide",context:"formatting"})||s.day(n,{width:"abbreviated",context:"formatting"})||s.day(n,{width:"short",context:"formatting"})||s.day(n,{width:"narrow",context:"formatting"})}}validate(n,r){return r>=0&&r<=6}set(n,r,s,o){return n=id(n,s,o),n.setHours(0,0,0,0),n}}class _x extends q{constructor(){super(...arguments);j(this,"priority",90);j(this,"incompatibleTokens",["y","R","u","q","Q","M","L","I","d","D","E","i","e","t","T"])}parse(n,r,s,o){const i=a=>{const l=Math.floor((a-1)/7)*7;return(a+o.weekStartsOn+6)%7+l};switch(r){case"c":case"cc":return ve(pe(r.length,n),i);case"co":return ve(s.ordinalNumber(n,{unit:"day"}),i);case"ccc":return s.day(n,{width:"abbreviated",context:"standalone"})||s.day(n,{width:"short",context:"standalone"})||s.day(n,{width:"narrow",context:"standalone"});case"ccccc":return s.day(n,{width:"narrow",context:"standalone"});case"cccccc":return s.day(n,{width:"short",context:"standalone"})||s.day(n,{width:"narrow",context:"standalone"});case"cccc":default:return s.day(n,{width:"wide",context:"standalone"})||s.day(n,{width:"abbreviated",context:"standalone"})||s.day(n,{width:"short",context:"standalone"})||s.day(n,{width:"narrow",context:"standalone"})}}validate(n,r){return r>=0&&r<=6}set(n,r,s,o){return n=id(n,s,o),n.setHours(0,0,0,0),n}}function xx(e){let n=R(e).getDay();return n===0&&(n=7),n}function kx(e,t){const n=R(e),r=xx(n),s=t-r;return Bn(n,s)}class Sx extends q{constructor(){super(...arguments);j(this,"priority",90);j(this,"incompatibleTokens",["y","Y","u","q","Q","M","L","w","d","D","E","e","c","t","T"])}parse(n,r,s){const o=i=>i===0?7:i;switch(r){case"i":case"ii":return pe(r.length,n);case"io":return s.ordinalNumber(n,{unit:"day"});case"iii":return ve(s.day(n,{width:"abbreviated",context:"formatting"})||s.day(n,{width:"short",context:"formatting"})||s.day(n,{width:"narrow",context:"formatting"}),o);case"iiiii":return ve(s.day(n,{width:"narrow",context:"formatting"}),o);case"iiiiii":return ve(s.day(n,{width:"short",context:"formatting"})||s.day(n,{width:"narrow",context:"formatting"}),o);case"iiii":default:return ve(s.day(n,{width:"wide",context:"formatting"})||s.day(n,{width:"abbreviated",context:"formatting"})||s.day(n,{width:"short",context:"formatting"})||s.day(n,{width:"narrow",context:"formatting"}),o)}}validate(n,r){return r>=1&&r<=7}set(n,r,s){return n=kx(n,s),n.setHours(0,0,0,0),n}}class bx extends q{constructor(){super(...arguments);j(this,"priority",80);j(this,"incompatibleTokens",["b","B","H","k","t","T"])}parse(n,r,s){switch(r){case"a":case"aa":case"aaa":return s.dayPeriod(n,{width:"abbreviated",context:"formatting"})||s.dayPeriod(n,{width:"narrow",context:"formatting"});case"aaaaa":return s.dayPeriod(n,{width:"narrow",context:"formatting"});case"aaaa":default:return s.dayPeriod(n,{width:"wide",context:"formatting"})||s.dayPeriod(n,{width:"abbreviated",context:"formatting"})||s.dayPeriod(n,{width:"narrow",context:"formatting"})}}set(n,r,s){return n.setHours(od(s),0,0,0),n}}class Dx extends q{constructor(){super(...arguments);j(this,"priority",80);j(this,"incompatibleTokens",["a","B","H","k","t","T"])}parse(n,r,s){switch(r){case"b":case"bb":case"bbb":return s.dayPeriod(n,{width:"abbreviated",context:"formatting"})||s.dayPeriod(n,{width:"narrow",context:"formatting"});case"bbbbb":return s.dayPeriod(n,{width:"narrow",context:"formatting"});case"bbbb":default:return s.dayPeriod(n,{width:"wide",context:"formatting"})||s.dayPeriod(n,{width:"abbreviated",context:"formatting"})||s.dayPeriod(n,{width:"narrow",context:"formatting"})}}set(n,r,s){return n.setHours(od(s),0,0,0),n}}class Cx extends q{constructor(){super(...arguments);j(this,"priority",80);j(this,"incompatibleTokens",["a","b","t","T"])}parse(n,r,s){switch(r){case"B":case"BB":case"BBB":return s.dayPeriod(n,{width:"abbreviated",context:"formatting"})||s.dayPeriod(n,{width:"narrow",context:"formatting"});case"BBBBB":return s.dayPeriod(n,{width:"narrow",context:"formatting"});case"BBBB":default:return s.dayPeriod(n,{width:"wide",context:"formatting"})||s.dayPeriod(n,{width:"abbreviated",context:"formatting"})||s.dayPeriod(n,{width:"narrow",context:"formatting"})}}set(n,r,s){return n.setHours(od(s),0,0,0),n}}class Ex extends q{constructor(){super(...arguments);j(this,"priority",70);j(this,"incompatibleTokens",["H","K","k","t","T"])}parse(n,r,s){switch(r){case"h":return ce(ye.hour12h,n);case"ho":return s.ordinalNumber(n,{unit:"hour"});default:return pe(r.length,n)}}validate(n,r){return r>=1&&r<=12}set(n,r,s){const o=n.getHours()>=12;return o&&s<12?n.setHours(s+12,0,0,0):!o&&s===12?n.setHours(0,0,0,0):n.setHours(s,0,0,0),n}}class Px extends q{constructor(){super(...arguments);j(this,"priority",70);j(this,"incompatibleTokens",["a","b","h","K","k","t","T"])}parse(n,r,s){switch(r){case"H":return ce(ye.hour23h,n);case"Ho":return s.ordinalNumber(n,{unit:"hour"});default:return pe(r.length,n)}}validate(n,r){return r>=0&&r<=23}set(n,r,s){return n.setHours(s,0,0,0),n}}class Nx extends q{constructor(){super(...arguments);j(this,"priority",70);j(this,"incompatibleTokens",["h","H","k","t","T"])}parse(n,r,s){switch(r){case"K":return ce(ye.hour11h,n);case"Ko":return s.ordinalNumber(n,{unit:"hour"});default:return pe(r.length,n)}}validate(n,r){return r>=0&&r<=11}set(n,r,s){return n.getHours()>=12&&s<12?n.setHours(s+12,0,0,0):n.setHours(s,0,0,0),n}}class Tx extends q{constructor(){super(...arguments);j(this,"priority",70);j(this,"incompatibleTokens",["a","b","h","H","K","t","T"])}parse(n,r,s){switch(r){case"k":return ce(ye.hour24h,n);case"ko":return s.ordinalNumber(n,{unit:"hour"});default:return pe(r.length,n)}}validate(n,r){return r>=1&&r<=24}set(n,r,s){const o=s<=24?s%24:s;return n.setHours(o,0,0,0),n}}class Ox extends q{constructor(){super(...arguments);j(this,"priority",60);j(this,"incompatibleTokens",["t","T"])}parse(n,r,s){switch(r){case"m":return ce(ye.minute,n);case"mo":return s.ordinalNumber(n,{unit:"minute"});default:return pe(r.length,n)}}validate(n,r){return r>=0&&r<=59}set(n,r,s){return n.setMinutes(s,0,0),n}}class Mx extends q{constructor(){super(...arguments);j(this,"priority",50);j(this,"incompatibleTokens",["t","T"])}parse(n,r,s){switch(r){case"s":return ce(ye.second,n);case"so":return s.ordinalNumber(n,{unit:"second"});default:return pe(r.length,n)}}validate(n,r){return r>=0&&r<=59}set(n,r,s){return n.setSeconds(s,0),n}}class jx extends q{constructor(){super(...arguments);j(this,"priority",30);j(this,"incompatibleTokens",["t","T"])}parse(n,r){const s=o=>Math.trunc(o*Math.pow(10,-r.length+3));return ve(pe(r.length,n),s)}set(n,r,s){return n.setMilliseconds(s),n}}class Rx extends q{constructor(){super(...arguments);j(this,"priority",10);j(this,"incompatibleTokens",["t","T","x"])}parse(n,r){switch(r){case"X":return It(Rt.basicOptionalMinutes,n);case"XX":return It(Rt.basic,n);case"XXXX":return It(Rt.basicOptionalSeconds,n);case"XXXXX":return It(Rt.extendedOptionalSeconds,n);case"XXX":default:return It(Rt.extended,n)}}set(n,r,s){return r.timestampIsSet?n:J(n,n.getTime()-ta(n)-s)}}class Ix extends q{constructor(){super(...arguments);j(this,"priority",10);j(this,"incompatibleTokens",["t","T","X"])}parse(n,r){switch(r){case"x":return It(Rt.basicOptionalMinutes,n);case"xx":return It(Rt.basic,n);case"xxxx":return It(Rt.basicOptionalSeconds,n);case"xxxxx":return It(Rt.extendedOptionalSeconds,n);case"xxx":default:return It(Rt.extended,n)}}set(n,r,s){return r.timestampIsSet?n:J(n,n.getTime()-ta(n)-s)}}class Lx extends q{constructor(){super(...arguments);j(this,"priority",40);j(this,"incompatibleTokens","*")}parse(n){return xg(n)}set(n,r,s){return[J(n,s*1e3),{timestampIsSet:!0}]}}class Ax extends q{constructor(){super(...arguments);j(this,"priority",20);j(this,"incompatibleTokens","*")}parse(n){return xg(n)}set(n,r,s){return[J(n,s),{timestampIsSet:!0}]}}const $x={G:new tx,y:new nx,Y:new rx,R:new sx,u:new ox,Q:new ix,q:new ax,M:new lx,L:new ux,w:new dx,I:new hx,d:new gx,D:new yx,E:new vx,e:new wx,c:new _x,i:new Sx,a:new bx,b:new Dx,B:new Cx,h:new Ex,H:new Px,K:new Nx,k:new Tx,m:new Ox,s:new Mx,S:new jx,X:new Rx,x:new Ix,t:new Lx,T:new Ax},Fx=/[yYQqMLwIdDecihHKkms]o|(\w)\1*|''|'(''|[^'])+('|$)|./g,Yx=/P+p+|P+|p+|''|'(''|[^'])+('|$)|./g,Ux=/^'([^]*?)'?$/,Wx=/''/g,Bx=/\S/,zx=/[a-zA-Z]/;function Ci(e,t,n,r){var _,x,g,m,v,k,E,P;const s=G_(),o=(r==null?void 0:r.locale)??s.locale??cg,i=(r==null?void 0:r.firstWeekContainsDate)??((x=(_=r==null?void 0:r.locale)==null?void 0:_.options)==null?void 0:x.firstWeekContainsDate)??s.firstWeekContainsDate??((m=(g=s.locale)==null?void 0:g.options)==null?void 0:m.firstWeekContainsDate)??1,a=(r==null?void 0:r.weekStartsOn)??((k=(v=r==null?void 0:r.locale)==null?void 0:v.options)==null?void 0:k.weekStartsOn)??s.weekStartsOn??((P=(E=s.locale)==null?void 0:E.options)==null?void 0:P.weekStartsOn)??0;if(t==="")return e===""?R(n):J(n,NaN);const l={firstWeekContainsDate:i,weekStartsOn:a,locale:o},u=[new ex],c=t.match(Yx).map(D=>{const b=D[0];if(b in na){const M=na[b];return M(D,o.formatLong)}return D}).join("").match(Fx),d=[];for(let D of c){!(r!=null&&r.useAdditionalWeekYearTokens)&&gg(D)&&Lu(D,t,e),!(r!=null&&r.useAdditionalDayOfYearTokens)&&mg(D)&&Lu(D,t,e);const b=D[0],M=$x[b];if(M){const{incompatibleTokens:O}=M;if(Array.isArray(O)){const H=d.find(ee=>O.includes(ee.token)||ee.token===b);if(H)throw new RangeError(`The format string mustn't contain \`${H.fullToken}\` and \`${D}\` at the same time`)}else if(M.incompatibleTokens==="*"&&d.length>0)throw new RangeError(`The format string mustn't contain \`${D}\` and any other token at the same time`);d.push({token:b,fullToken:D});const F=M.run(e,D,o.match,l);if(!F)return J(n,NaN);u.push(F.setter),e=F.rest}else{if(b.match(zx))throw new RangeError("Format string contains an unescaped latin alphabet character `"+b+"`");if(D==="''"?D="'":b==="'"&&(D=Hx(D)),e.indexOf(D)===0)e=e.slice(D.length);else return J(n,NaN)}}if(e.length>0&&Bx.test(e))return J(n,NaN);const f=u.map(D=>D.priority).sort((D,b)=>b-D).filter((D,b,M)=>M.indexOf(D)===b).map(D=>u.filter(b=>b.priority===D).sort((b,M)=>M.subPriority-b.subPriority)).map(D=>D[0]);let p=R(n);if(isNaN(p.getTime()))return J(n,NaN);const y={};for(const D of f){if(!D.validate(p,l))return J(n,NaN);const b=D.set(p,y,l);Array.isArray(b)?(p=b[0],Object.assign(y,b[1])):p=b}return J(n,p)}function Hx(e){return e.match(Ux)[1].replace(Wx,"'")}function Vx(e,t){const r=Gx(e);let s;if(r.date){const l=Jx(r.date,2);s=Xx(l.restDateString,l.year)}if(!s||isNaN(s.getTime()))return new Date(NaN);const o=s.getTime();let i=0,a;if(r.time&&(i=Zx(r.time),isNaN(i)))return new Date(NaN);if(r.timezone){if(a=e1(r.timezone),isNaN(a))return new Date(NaN)}else{const l=new Date(o+i),u=new Date(0);return u.setFullYear(l.getUTCFullYear(),l.getUTCMonth(),l.getUTCDate()),u.setHours(l.getUTCHours(),l.getUTCMinutes(),l.getUTCSeconds(),l.getUTCMilliseconds()),u}return new Date(o+i+a)}const Zo={dateTimeDelimiter:/[T ]/,timeZoneDelimiter:/[Z ]/i,timezone:/([Z+-].*)$/},Qx=/^-?(?:(\d{3})|(\d{2})(?:-?(\d{2}))?|W(\d{2})(?:-?(\d{1}))?|)$/,Kx=/^(\d{2}(?:[.,]\d*)?)(?::?(\d{2}(?:[.,]\d*)?))?(?::?(\d{2}(?:[.,]\d*)?))?$/,qx=/^([+-])(\d{2})(?::?(\d{2}))?$/;function Gx(e){const t={},n=e.split(Zo.dateTimeDelimiter);let r;if(n.length>2)return t;if(/:/.test(n[0])?r=n[0]:(t.date=n[0],r=n[1],Zo.timeZoneDelimiter.test(t.date)&&(t.date=e.split(Zo.timeZoneDelimiter)[0],r=e.substr(t.date.length,e.length))),r){const s=Zo.timezone.exec(r);s?(t.time=r.replace(s[1],""),t.timezone=s[1]):t.time=r}return t}function Jx(e,t){const n=new RegExp("^(?:(\\d{4}|[+-]\\d{"+(4+t)+"})|(\\d{2}|[+-]\\d{"+(2+t)+"})$)"),r=e.match(n);if(!r)return{year:NaN,restDateString:""};const s=r[1]?parseInt(r[1]):null,o=r[2]?parseInt(r[2]):null;return{year:o===null?s:o*100,restDateString:e.slice((r[1]||r[2]).length)}}function Xx(e,t){if(t===null)return new Date(NaN);const n=e.match(Qx);if(!n)return new Date(NaN);const r=!!n[4],s=Ps(n[1]),o=Ps(n[2])-1,i=Ps(n[3]),a=Ps(n[4]),l=Ps(n[5])-1;if(r)return o1(t,a,l)?t1(t,a,l):new Date(NaN);{const u=new Date(0);return!r1(t,o,i)||!s1(t,s)?new Date(NaN):(u.setUTCFullYear(t,o,Math.max(s,i)),u)}}function Ps(e){return e?parseInt(e):1}function Zx(e){const t=e.match(Kx);if(!t)return NaN;const n=Nl(t[1]),r=Nl(t[2]),s=Nl(t[3]);return i1(n,r,s)?n*Aa+r*La+s*1e3:NaN}function Nl(e){return e&&parseFloat(e.replace(",","."))||0}function e1(e){if(e==="Z")return 0;const t=e.match(qx);if(!t)return 0;const n=t[1]==="+"?-1:1,r=parseInt(t[2]),s=t[3]&&parseInt(t[3])||0;return a1(r,s)?n*(r*Aa+s*La):NaN}function t1(e,t,n){const r=new Date(0);r.setUTCFullYear(e,0,4);const s=r.getUTCDay()||7,o=(t-1)*7+n+1-s;return r.setUTCDate(r.getUTCDate()+o),r}const n1=[31,null,31,30,31,30,31,31,30,31,30,31];function bg(e){return e%400===0||e%4===0&&e%100!==0}function r1(e,t,n){return t>=0&&t<=11&&n>=1&&n<=(n1[t]||(bg(e)?29:28))}function s1(e,t){return t>=1&&t<=(bg(e)?366:365)}function o1(e,t,n){return t>=1&&t<=53&&n>=0&&n<=6}function i1(e,t,n){return e===24?t===0&&n===0:n>=0&&n<60&&t>=0&&t<60&&e>=0&&e<25}function a1(e,t){return t>=0&&t<=59}function l1(e,t){return rd(e,t*1e3)}function u1(e,t){let n=R(e);return isNaN(+n)?J(e,NaN):(t.year!=null&&n.setFullYear(t.year),t.month!=null&&(n=Ye(n,t.month)),t.date!=null&&n.setDate(t.date),t.hours!=null&&n.setHours(t.hours),t.minutes!=null&&n.setMinutes(t.minutes),t.seconds!=null&&n.setSeconds(t.seconds),t.milliseconds!=null&&n.setMilliseconds(t.milliseconds),n)}function c1(e,t){e.prototype=Object.create(t.prototype),e.prototype.constructor=e,Uu(e,t)}function Uu(e,t){return Uu=Object.setPrototypeOf||function(r,s){return r.__proto__=s,r},Uu(e,t)}function d1(e,t){if(e==null)return{};var n={},r=Object.keys(e),s,o;for(o=0;o<r.length;o++)s=r[o],!(t.indexOf(s)>=0)&&(n[s]=e[s]);return n}function Vf(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function f1(e,t,n){return e===t?!0:e.correspondingElement?e.correspondingElement.classList.contains(n):e.classList.contains(n)}function h1(e,t,n){if(e===t)return!0;for(;e.parentNode||e.host;){if(e.parentNode&&f1(e,t,n))return!0;e=e.parentNode||e.host}return e}function p1(e){return document.documentElement.clientWidth<=e.clientX||document.documentElement.clientHeight<=e.clientY}var m1=function(){if(!(typeof window>"u"||typeof window.addEventListener!="function")){var t=!1,n=Object.defineProperty({},"passive",{get:function(){t=!0}}),r=function(){};return window.addEventListener("testPassiveEventSupport",r,n),window.removeEventListener("testPassiveEventSupport",r,n),t}};function g1(e){return e===void 0&&(e=0),function(){return++e}}var y1=g1(),Wu,ei={},Tl={},v1=["touchstart","touchmove"],w1="ignore-react-onclickoutside";function Qf(e,t){var n={},r=v1.indexOf(t)!==-1;return r&&Wu&&(n.passive=!e.props.preventDefault),n}function Fa(e,t){var n,r,s=e.displayName||e.name||"Component";return r=n=function(o){c1(i,o);function i(l){var u;return u=o.call(this,l)||this,u.__outsideClickHandler=function(c){if(typeof u.__clickOutsideHandlerProp=="function"){u.__clickOutsideHandlerProp(c);return}var d=u.getInstance();if(typeof d.props.handleClickOutside=="function"){d.props.handleClickOutside(c);return}if(typeof d.handleClickOutside=="function"){d.handleClickOutside(c);return}throw new Error("WrappedComponent: "+s+" lacks a handleClickOutside(event) function for processing outside click events.")},u.__getComponentNode=function(){var c=u.getInstance();return typeof c.setClickOutsideRef=="function"?c.setClickOutsideRef():Ra.findDOMNode(c)},u.enableOnClickOutside=function(){if(!(typeof document>"u"||Tl[u._uid])){typeof Wu>"u"&&(Wu=m1()),Tl[u._uid]=!0;var c=u.props.eventTypes;c.forEach||(c=[c]),ei[u._uid]=function(d){if(u.componentNode!==null&&!(u.initTimeStamp>d.timeStamp)&&(u.props.preventDefault&&d.preventDefault(),u.props.stopPropagation&&d.stopPropagation(),!(u.props.excludeScrollbar&&p1(d)))){var f=d.composed&&d.composedPath&&d.composedPath().shift()||d.target;h1(f,u.componentNode,u.props.outsideClickIgnoreClass)===document&&u.__outsideClickHandler(d)}},c.forEach(function(d){document.addEventListener(d,ei[u._uid],Qf(Vf(u),d))})}},u.disableOnClickOutside=function(){delete Tl[u._uid];var c=ei[u._uid];if(c&&typeof document<"u"){var d=u.props.eventTypes;d.forEach||(d=[d]),d.forEach(function(f){return document.removeEventListener(f,c,Qf(Vf(u),f))}),delete ei[u._uid]}},u.getRef=function(c){return u.instanceRef=c},u._uid=y1(),u.initTimeStamp=performance.now(),u}var a=i.prototype;return a.getInstance=function(){if(e.prototype&&!e.prototype.isReactComponent)return this;var u=this.instanceRef;return u.getInstance?u.getInstance():u},a.componentDidMount=function(){typeof document>"u"||!document.createElement||(this.getInstance(),this.componentNode=this.__getComponentNode(),!this.props.disableOnClickOutside&&this.enableOnClickOutside())},a.componentDidUpdate=function(){this.componentNode=this.__getComponentNode()},a.componentWillUnmount=function(){this.disableOnClickOutside()},a.render=function(){var u=this.props;u.excludeScrollbar;var c=d1(u,["excludeScrollbar"]);return e.prototype&&e.prototype.isReactComponent?c.ref=this.getRef:c.wrappedRef=this.getRef,c.disableOnClickOutside=this.disableOnClickOutside,c.enableOnClickOutside=this.enableOnClickOutside,S.createElement(e,c)},i}(S.Component),n.displayName="OnClickOutside("+s+")",n.defaultProps={eventTypes:["mousedown","touchstart"],excludeScrollbar:!1,outsideClickIgnoreClass:w1,preventDefault:!1,stopPropagation:!1},n.getClass=function(){return e.getClass?e.getClass():e},r}function Ya(){return typeof window<"u"}function ms(e){return Dg(e)?(e.nodeName||"").toLowerCase():"#document"}function it(e){var t;return(e==null||(t=e.ownerDocument)==null?void 0:t.defaultView)||window}function zt(e){var t;return(t=(Dg(e)?e.ownerDocument:e.document)||window.document)==null?void 0:t.documentElement}function Dg(e){return Ya()?e instanceof Node||e instanceof it(e).Node:!1}function We(e){return Ya()?e instanceof Element||e instanceof it(e).Element:!1}function Bt(e){return Ya()?e instanceof HTMLElement||e instanceof it(e).HTMLElement:!1}function Kf(e){return!Ya()||typeof ShadowRoot>"u"?!1:e instanceof ShadowRoot||e instanceof it(e).ShadowRoot}function To(e){const{overflow:t,overflowX:n,overflowY:r,display:s}=vt(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(s)}function _1(e){return["table","td","th"].includes(ms(e))}function Ua(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch{return!1}})}function ad(e){const t=ld(),n=We(e)?vt(e):e;return["transform","translate","scale","rotate","perspective"].some(r=>n[r]?n[r]!=="none":!1)||(n.containerType?n.containerType!=="normal":!1)||!t&&(n.backdropFilter?n.backdropFilter!=="none":!1)||!t&&(n.filter?n.filter!=="none":!1)||["transform","translate","scale","rotate","perspective","filter"].some(r=>(n.willChange||"").includes(r))||["paint","layout","strict","content"].some(r=>(n.contain||"").includes(r))}function x1(e){let t=$n(e);for(;Bt(t)&&!os(t);){if(ad(t))return t;if(Ua(t))return null;t=$n(t)}return null}function ld(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}function os(e){return["html","body","#document"].includes(ms(e))}function vt(e){return it(e).getComputedStyle(e)}function Wa(e){return We(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function $n(e){if(ms(e)==="html")return e;const t=e.assignedSlot||e.parentNode||Kf(e)&&e.host||zt(e);return Kf(t)?t.host:t}function Cg(e){const t=$n(e);return os(t)?e.ownerDocument?e.ownerDocument.body:e.body:Bt(t)&&To(t)?t:Cg(t)}function vo(e,t,n){var r;t===void 0&&(t=[]),n===void 0&&(n=!0);const s=Cg(e),o=s===((r=e.ownerDocument)==null?void 0:r.body),i=it(s);if(o){const a=Bu(i);return t.concat(i,i.visualViewport||[],To(s)?s:[],a&&n?vo(a):[])}return t.concat(s,vo(s,[],n))}function Bu(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}const is=Math.min,ir=Math.max,la=Math.round,ti=Math.floor,Wt=e=>({x:e,y:e}),k1={left:"right",right:"left",bottom:"top",top:"bottom"},S1={start:"end",end:"start"};function b1(e,t,n){return ir(e,is(t,n))}function Ba(e,t){return typeof e=="function"?e(t):e}function as(e){return e.split("-")[0]}function Oo(e){return e.split("-")[1]}function D1(e){return e==="x"?"y":"x"}function ud(e){return e==="y"?"height":"width"}function wo(e){return["top","bottom"].includes(as(e))?"y":"x"}function cd(e){return D1(wo(e))}function C1(e,t,n){n===void 0&&(n=!1);const r=Oo(e),s=cd(e),o=ud(s);let i=s==="x"?r===(n?"end":"start")?"right":"left":r==="start"?"bottom":"top";return t.reference[o]>t.floating[o]&&(i=ua(i)),[i,ua(i)]}function E1(e){const t=ua(e);return[zu(e),t,zu(t)]}function zu(e){return e.replace(/start|end/g,t=>S1[t])}function P1(e,t,n){const r=["left","right"],s=["right","left"],o=["top","bottom"],i=["bottom","top"];switch(e){case"top":case"bottom":return n?t?s:r:t?r:s;case"left":case"right":return t?o:i;default:return[]}}function N1(e,t,n,r){const s=Oo(e);let o=P1(as(e),n==="start",r);return s&&(o=o.map(i=>i+"-"+s),t&&(o=o.concat(o.map(zu)))),o}function ua(e){return e.replace(/left|right|bottom|top/g,t=>k1[t])}function T1(e){return{top:0,right:0,bottom:0,left:0,...e}}function Eg(e){return typeof e!="number"?T1(e):{top:e,right:e,bottom:e,left:e}}function ca(e){const{x:t,y:n,width:r,height:s}=e;return{width:r,height:s,top:n,left:t,right:t+r,bottom:n+s,x:t,y:n}}function qf(e,t,n){let{reference:r,floating:s}=e;const o=wo(t),i=cd(t),a=ud(i),l=as(t),u=o==="y",c=r.x+r.width/2-s.width/2,d=r.y+r.height/2-s.height/2,f=r[a]/2-s[a]/2;let p;switch(l){case"top":p={x:c,y:r.y-s.height};break;case"bottom":p={x:c,y:r.y+r.height};break;case"right":p={x:r.x+r.width,y:d};break;case"left":p={x:r.x-s.width,y:d};break;default:p={x:r.x,y:r.y}}switch(Oo(t)){case"start":p[i]-=f*(n&&u?-1:1);break;case"end":p[i]+=f*(n&&u?-1:1);break}return p}const O1=async(e,t,n)=>{const{placement:r="bottom",strategy:s="absolute",middleware:o=[],platform:i}=n,a=o.filter(Boolean),l=await(i.isRTL==null?void 0:i.isRTL(t));let u=await i.getElementRects({reference:e,floating:t,strategy:s}),{x:c,y:d}=qf(u,r,l),f=r,p={},y=0;for(let _=0;_<a.length;_++){const{name:x,fn:g}=a[_],{x:m,y:v,data:k,reset:E}=await g({x:c,y:d,initialPlacement:r,placement:f,strategy:s,middlewareData:p,rects:u,platform:i,elements:{reference:e,floating:t}});c=m??c,d=v??d,p={...p,[x]:{...p[x],...k}},E&&y<=50&&(y++,typeof E=="object"&&(E.placement&&(f=E.placement),E.rects&&(u=E.rects===!0?await i.getElementRects({reference:e,floating:t,strategy:s}):E.rects),{x:c,y:d}=qf(u,f,l)),_=-1)}return{x:c,y:d,placement:f,strategy:s,middlewareData:p}};async function M1(e,t){var n;t===void 0&&(t={});const{x:r,y:s,platform:o,rects:i,elements:a,strategy:l}=e,{boundary:u="clippingAncestors",rootBoundary:c="viewport",elementContext:d="floating",altBoundary:f=!1,padding:p=0}=Ba(t,e),y=Eg(p),x=a[f?d==="floating"?"reference":"floating":d],g=ca(await o.getClippingRect({element:(n=await(o.isElement==null?void 0:o.isElement(x)))==null||n?x:x.contextElement||await(o.getDocumentElement==null?void 0:o.getDocumentElement(a.floating)),boundary:u,rootBoundary:c,strategy:l})),m=d==="floating"?{x:r,y:s,width:i.floating.width,height:i.floating.height}:i.reference,v=await(o.getOffsetParent==null?void 0:o.getOffsetParent(a.floating)),k=await(o.isElement==null?void 0:o.isElement(v))?await(o.getScale==null?void 0:o.getScale(v))||{x:1,y:1}:{x:1,y:1},E=ca(o.convertOffsetParentRelativeRectToViewportRelativeRect?await o.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:m,offsetParent:v,strategy:l}):m);return{top:(g.top-E.top+y.top)/k.y,bottom:(E.bottom-g.bottom+y.bottom)/k.y,left:(g.left-E.left+y.left)/k.x,right:(E.right-g.right+y.right)/k.x}}const j1=e=>({name:"arrow",options:e,async fn(t){const{x:n,y:r,placement:s,rects:o,platform:i,elements:a,middlewareData:l}=t,{element:u,padding:c=0}=Ba(e,t)||{};if(u==null)return{};const d=Eg(c),f={x:n,y:r},p=cd(s),y=ud(p),_=await i.getDimensions(u),x=p==="y",g=x?"top":"left",m=x?"bottom":"right",v=x?"clientHeight":"clientWidth",k=o.reference[y]+o.reference[p]-f[p]-o.floating[y],E=f[p]-o.reference[p],P=await(i.getOffsetParent==null?void 0:i.getOffsetParent(u));let D=P?P[v]:0;(!D||!await(i.isElement==null?void 0:i.isElement(P)))&&(D=a.floating[v]||o.floating[y]);const b=k/2-E/2,M=D/2-_[y]/2-1,O=is(d[g],M),F=is(d[m],M),H=O,ee=D-_[y]-F,A=D/2-_[y]/2+b,V=b1(H,A,ee),Y=!l.arrow&&Oo(s)!=null&&A!==V&&o.reference[y]/2-(A<H?O:F)-_[y]/2<0,ie=Y?A<H?A-H:A-ee:0;return{[p]:f[p]+ie,data:{[p]:V,centerOffset:A-V-ie,...Y&&{alignmentOffset:ie}},reset:Y}}}),R1=function(e){return e===void 0&&(e={}),{name:"flip",options:e,async fn(t){var n,r;const{placement:s,middlewareData:o,rects:i,initialPlacement:a,platform:l,elements:u}=t,{mainAxis:c=!0,crossAxis:d=!0,fallbackPlacements:f,fallbackStrategy:p="bestFit",fallbackAxisSideDirection:y="none",flipAlignment:_=!0,...x}=Ba(e,t);if((n=o.arrow)!=null&&n.alignmentOffset)return{};const g=as(s),m=wo(a),v=as(a)===a,k=await(l.isRTL==null?void 0:l.isRTL(u.floating)),E=f||(v||!_?[ua(a)]:E1(a)),P=y!=="none";!f&&P&&E.push(...N1(a,_,y,k));const D=[a,...E],b=await M1(t,x),M=[];let O=((r=o.flip)==null?void 0:r.overflows)||[];if(c&&M.push(b[g]),d){const A=C1(s,i,k);M.push(b[A[0]],b[A[1]])}if(O=[...O,{placement:s,overflows:M}],!M.every(A=>A<=0)){var F,H;const A=(((F=o.flip)==null?void 0:F.index)||0)+1,V=D[A];if(V)return{data:{index:A,overflows:O},reset:{placement:V}};let Y=(H=O.filter(ie=>ie.overflows[0]<=0).sort((ie,N)=>ie.overflows[1]-N.overflows[1])[0])==null?void 0:H.placement;if(!Y)switch(p){case"bestFit":{var ee;const ie=(ee=O.filter(N=>{if(P){const L=wo(N.placement);return L===m||L==="y"}return!0}).map(N=>[N.placement,N.overflows.filter(L=>L>0).reduce((L,$)=>L+$,0)]).sort((N,L)=>N[1]-L[1])[0])==null?void 0:ee[0];ie&&(Y=ie);break}case"initialPlacement":Y=a;break}if(s!==Y)return{reset:{placement:Y}}}return{}}}};async function I1(e,t){const{placement:n,platform:r,elements:s}=e,o=await(r.isRTL==null?void 0:r.isRTL(s.floating)),i=as(n),a=Oo(n),l=wo(n)==="y",u=["left","top"].includes(i)?-1:1,c=o&&l?-1:1,d=Ba(t,e);let{mainAxis:f,crossAxis:p,alignmentAxis:y}=typeof d=="number"?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:d.mainAxis||0,crossAxis:d.crossAxis||0,alignmentAxis:d.alignmentAxis};return a&&typeof y=="number"&&(p=a==="end"?y*-1:y),l?{x:p*c,y:f*u}:{x:f*u,y:p*c}}const L1=function(e){return e===void 0&&(e=0),{name:"offset",options:e,async fn(t){var n,r;const{x:s,y:o,placement:i,middlewareData:a}=t,l=await I1(t,e);return i===((n=a.offset)==null?void 0:n.placement)&&(r=a.arrow)!=null&&r.alignmentOffset?{}:{x:s+l.x,y:o+l.y,data:{...l,placement:i}}}}};function Pg(e){const t=vt(e);let n=parseFloat(t.width)||0,r=parseFloat(t.height)||0;const s=Bt(e),o=s?e.offsetWidth:n,i=s?e.offsetHeight:r,a=la(n)!==o||la(r)!==i;return a&&(n=o,r=i),{width:n,height:r,$:a}}function dd(e){return We(e)?e:e.contextElement}function Vr(e){const t=dd(e);if(!Bt(t))return Wt(1);const n=t.getBoundingClientRect(),{width:r,height:s,$:o}=Pg(t);let i=(o?la(n.width):n.width)/r,a=(o?la(n.height):n.height)/s;return(!i||!Number.isFinite(i))&&(i=1),(!a||!Number.isFinite(a))&&(a=1),{x:i,y:a}}const A1=Wt(0);function Ng(e){const t=it(e);return!ld()||!t.visualViewport?A1:{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}}function $1(e,t,n){return t===void 0&&(t=!1),!n||t&&n!==it(e)?!1:t}function pr(e,t,n,r){t===void 0&&(t=!1),n===void 0&&(n=!1);const s=e.getBoundingClientRect(),o=dd(e);let i=Wt(1);t&&(r?We(r)&&(i=Vr(r)):i=Vr(e));const a=$1(o,n,r)?Ng(o):Wt(0);let l=(s.left+a.x)/i.x,u=(s.top+a.y)/i.y,c=s.width/i.x,d=s.height/i.y;if(o){const f=it(o),p=r&&We(r)?it(r):r;let y=f,_=Bu(y);for(;_&&r&&p!==y;){const x=Vr(_),g=_.getBoundingClientRect(),m=vt(_),v=g.left+(_.clientLeft+parseFloat(m.paddingLeft))*x.x,k=g.top+(_.clientTop+parseFloat(m.paddingTop))*x.y;l*=x.x,u*=x.y,c*=x.x,d*=x.y,l+=v,u+=k,y=it(_),_=Bu(y)}}return ca({width:c,height:d,x:l,y:u})}function fd(e,t){const n=Wa(e).scrollLeft;return t?t.left+n:pr(zt(e)).left+n}function Tg(e,t,n){n===void 0&&(n=!1);const r=e.getBoundingClientRect(),s=r.left+t.scrollLeft-(n?0:fd(e,r)),o=r.top+t.scrollTop;return{x:s,y:o}}function F1(e){let{elements:t,rect:n,offsetParent:r,strategy:s}=e;const o=s==="fixed",i=zt(r),a=t?Ua(t.floating):!1;if(r===i||a&&o)return n;let l={scrollLeft:0,scrollTop:0},u=Wt(1);const c=Wt(0),d=Bt(r);if((d||!d&&!o)&&((ms(r)!=="body"||To(i))&&(l=Wa(r)),Bt(r))){const p=pr(r);u=Vr(r),c.x=p.x+r.clientLeft,c.y=p.y+r.clientTop}const f=i&&!d&&!o?Tg(i,l,!0):Wt(0);return{width:n.width*u.x,height:n.height*u.y,x:n.x*u.x-l.scrollLeft*u.x+c.x+f.x,y:n.y*u.y-l.scrollTop*u.y+c.y+f.y}}function Y1(e){return Array.from(e.getClientRects())}function U1(e){const t=zt(e),n=Wa(e),r=e.ownerDocument.body,s=ir(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),o=ir(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight);let i=-n.scrollLeft+fd(e);const a=-n.scrollTop;return vt(r).direction==="rtl"&&(i+=ir(t.clientWidth,r.clientWidth)-s),{width:s,height:o,x:i,y:a}}function W1(e,t){const n=it(e),r=zt(e),s=n.visualViewport;let o=r.clientWidth,i=r.clientHeight,a=0,l=0;if(s){o=s.width,i=s.height;const u=ld();(!u||u&&t==="fixed")&&(a=s.offsetLeft,l=s.offsetTop)}return{width:o,height:i,x:a,y:l}}function B1(e,t){const n=pr(e,!0,t==="fixed"),r=n.top+e.clientTop,s=n.left+e.clientLeft,o=Bt(e)?Vr(e):Wt(1),i=e.clientWidth*o.x,a=e.clientHeight*o.y,l=s*o.x,u=r*o.y;return{width:i,height:a,x:l,y:u}}function Gf(e,t,n){let r;if(t==="viewport")r=W1(e,n);else if(t==="document")r=U1(zt(e));else if(We(t))r=B1(t,n);else{const s=Ng(e);r={x:t.x-s.x,y:t.y-s.y,width:t.width,height:t.height}}return ca(r)}function Og(e,t){const n=$n(e);return n===t||!We(n)||os(n)?!1:vt(n).position==="fixed"||Og(n,t)}function z1(e,t){const n=t.get(e);if(n)return n;let r=vo(e,[],!1).filter(a=>We(a)&&ms(a)!=="body"),s=null;const o=vt(e).position==="fixed";let i=o?$n(e):e;for(;We(i)&&!os(i);){const a=vt(i),l=ad(i);!l&&a.position==="fixed"&&(s=null),(o?!l&&!s:!l&&a.position==="static"&&!!s&&["absolute","fixed"].includes(s.position)||To(i)&&!l&&Og(e,i))?r=r.filter(c=>c!==i):s=a,i=$n(i)}return t.set(e,r),r}function H1(e){let{element:t,boundary:n,rootBoundary:r,strategy:s}=e;const i=[...n==="clippingAncestors"?Ua(t)?[]:z1(t,this._c):[].concat(n),r],a=i[0],l=i.reduce((u,c)=>{const d=Gf(t,c,s);return u.top=ir(d.top,u.top),u.right=is(d.right,u.right),u.bottom=is(d.bottom,u.bottom),u.left=ir(d.left,u.left),u},Gf(t,a,s));return{width:l.right-l.left,height:l.bottom-l.top,x:l.left,y:l.top}}function V1(e){const{width:t,height:n}=Pg(e);return{width:t,height:n}}function Q1(e,t,n){const r=Bt(t),s=zt(t),o=n==="fixed",i=pr(e,!0,o,t);let a={scrollLeft:0,scrollTop:0};const l=Wt(0);if(r||!r&&!o)if((ms(t)!=="body"||To(s))&&(a=Wa(t)),r){const f=pr(t,!0,o,t);l.x=f.x+t.clientLeft,l.y=f.y+t.clientTop}else s&&(l.x=fd(s));const u=s&&!r&&!o?Tg(s,a):Wt(0),c=i.left+a.scrollLeft-l.x-u.x,d=i.top+a.scrollTop-l.y-u.y;return{x:c,y:d,width:i.width,height:i.height}}function Ol(e){return vt(e).position==="static"}function Jf(e,t){if(!Bt(e)||vt(e).position==="fixed")return null;if(t)return t(e);let n=e.offsetParent;return zt(e)===n&&(n=n.ownerDocument.body),n}function Mg(e,t){const n=it(e);if(Ua(e))return n;if(!Bt(e)){let s=$n(e);for(;s&&!os(s);){if(We(s)&&!Ol(s))return s;s=$n(s)}return n}let r=Jf(e,t);for(;r&&_1(r)&&Ol(r);)r=Jf(r,t);return r&&os(r)&&Ol(r)&&!ad(r)?n:r||x1(e)||n}const K1=async function(e){const t=this.getOffsetParent||Mg,n=this.getDimensions,r=await n(e.floating);return{reference:Q1(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}};function q1(e){return vt(e).direction==="rtl"}const G1={convertOffsetParentRelativeRectToViewportRelativeRect:F1,getDocumentElement:zt,getClippingRect:H1,getOffsetParent:Mg,getElementRects:K1,getClientRects:Y1,getDimensions:V1,getScale:Vr,isElement:We,isRTL:q1};function jg(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}function J1(e,t){let n=null,r;const s=zt(e);function o(){var a;clearTimeout(r),(a=n)==null||a.disconnect(),n=null}function i(a,l){a===void 0&&(a=!1),l===void 0&&(l=1),o();const u=e.getBoundingClientRect(),{left:c,top:d,width:f,height:p}=u;if(a||t(),!f||!p)return;const y=ti(d),_=ti(s.clientWidth-(c+f)),x=ti(s.clientHeight-(d+p)),g=ti(c),v={rootMargin:-y+"px "+-_+"px "+-x+"px "+-g+"px",threshold:ir(0,is(1,l))||1};let k=!0;function E(P){const D=P[0].intersectionRatio;if(D!==l){if(!k)return i();D?i(!1,D):r=setTimeout(()=>{i(!1,1e-7)},1e3)}D===1&&!jg(u,e.getBoundingClientRect())&&i(),k=!1}try{n=new IntersectionObserver(E,{...v,root:s.ownerDocument})}catch{n=new IntersectionObserver(E,v)}n.observe(e)}return i(!0),o}function X1(e,t,n,r){r===void 0&&(r={});const{ancestorScroll:s=!0,ancestorResize:o=!0,elementResize:i=typeof ResizeObserver=="function",layoutShift:a=typeof IntersectionObserver=="function",animationFrame:l=!1}=r,u=dd(e),c=s||o?[...u?vo(u):[],...vo(t)]:[];c.forEach(g=>{s&&g.addEventListener("scroll",n,{passive:!0}),o&&g.addEventListener("resize",n)});const d=u&&a?J1(u,n):null;let f=-1,p=null;i&&(p=new ResizeObserver(g=>{let[m]=g;m&&m.target===u&&p&&(p.unobserve(t),cancelAnimationFrame(f),f=requestAnimationFrame(()=>{var v;(v=p)==null||v.observe(t)})),n()}),u&&!l&&p.observe(u),p.observe(t));let y,_=l?pr(e):null;l&&x();function x(){const g=pr(e);_&&!jg(_,g)&&n(),_=g,y=requestAnimationFrame(x)}return n(),()=>{var g;c.forEach(m=>{s&&m.removeEventListener("scroll",n),o&&m.removeEventListener("resize",n)}),d==null||d(),(g=p)==null||g.disconnect(),p=null,l&&cancelAnimationFrame(y)}}const Z1=L1,ek=R1,Xf=j1,tk=(e,t,n)=>{const r=new Map,s={platform:G1,...n},o={...s.platform,_c:r};return O1(e,t,{...s,platform:o})};var Ei=typeof document<"u"?S.useLayoutEffect:S.useEffect;function da(e,t){if(e===t)return!0;if(typeof e!=typeof t)return!1;if(typeof e=="function"&&e.toString()===t.toString())return!0;let n,r,s;if(e&&t&&typeof e=="object"){if(Array.isArray(e)){if(n=e.length,n!==t.length)return!1;for(r=n;r--!==0;)if(!da(e[r],t[r]))return!1;return!0}if(s=Object.keys(e),n=s.length,n!==Object.keys(t).length)return!1;for(r=n;r--!==0;)if(!{}.hasOwnProperty.call(t,s[r]))return!1;for(r=n;r--!==0;){const o=s[r];if(!(o==="_owner"&&e.$$typeof)&&!da(e[o],t[o]))return!1}return!0}return e!==e&&t!==t}function Rg(e){return typeof window>"u"?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function Zf(e,t){const n=Rg(e);return Math.round(t*n)/n}function Ml(e){const t=S.useRef(e);return Ei(()=>{t.current=e}),t}function nk(e){e===void 0&&(e={});const{placement:t="bottom",strategy:n="absolute",middleware:r=[],platform:s,elements:{reference:o,floating:i}={},transform:a=!0,whileElementsMounted:l,open:u}=e,[c,d]=S.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[f,p]=S.useState(r);da(f,r)||p(r);const[y,_]=S.useState(null),[x,g]=S.useState(null),m=S.useCallback(N=>{N!==P.current&&(P.current=N,_(N))},[]),v=S.useCallback(N=>{N!==D.current&&(D.current=N,g(N))},[]),k=o||y,E=i||x,P=S.useRef(null),D=S.useRef(null),b=S.useRef(c),M=l!=null,O=Ml(l),F=Ml(s),H=Ml(u),ee=S.useCallback(()=>{if(!P.current||!D.current)return;const N={placement:t,strategy:n,middleware:f};F.current&&(N.platform=F.current),tk(P.current,D.current,N).then(L=>{const $={...L,isPositioned:H.current!==!1};A.current&&!da(b.current,$)&&(b.current=$,Ra.flushSync(()=>{d($)}))})},[f,t,n,F,H]);Ei(()=>{u===!1&&b.current.isPositioned&&(b.current.isPositioned=!1,d(N=>({...N,isPositioned:!1})))},[u]);const A=S.useRef(!1);Ei(()=>(A.current=!0,()=>{A.current=!1}),[]),Ei(()=>{if(k&&(P.current=k),E&&(D.current=E),k&&E){if(O.current)return O.current(k,E,ee);ee()}},[k,E,ee,O,M]);const V=S.useMemo(()=>({reference:P,floating:D,setReference:m,setFloating:v}),[m,v]),Y=S.useMemo(()=>({reference:k,floating:E}),[k,E]),ie=S.useMemo(()=>{const N={position:n,left:0,top:0};if(!Y.floating)return N;const L=Zf(Y.floating,c.x),$=Zf(Y.floating,c.y);return a?{...N,transform:"translate("+L+"px, "+$+"px)",...Rg(Y.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:L,top:$}},[n,a,Y.floating,c.x,c.y]);return S.useMemo(()=>({...c,update:ee,refs:V,elements:Y,floatingStyles:ie}),[c,ee,V,Y,ie])}const rk=e=>{function t(n){return{}.hasOwnProperty.call(n,"current")}return{name:"arrow",options:e,fn(n){const{element:r,padding:s}=typeof e=="function"?e(n):e;return r&&t(r)?r.current!=null?Xf({element:r.current,padding:s}).fn(n):{}:r?Xf({element:r,padding:s}).fn(n):{}}}},sk=(e,t)=>({...Z1(e),options:[e,t]}),ok=(e,t)=>({...ek(e),options:[e,t]}),ik=(e,t)=>({...rk(e),options:[e,t]}),Ig={...Qh},ak=Ig.useInsertionEffect,lk=ak||(e=>e());function uk(e){const t=S.useRef(()=>{});return lk(()=>{t.current=e}),S.useCallback(function(){for(var n=arguments.length,r=new Array(n),s=0;s<n;s++)r[s]=arguments[s];return t.current==null?void 0:t.current(...r)},[])}var fa=typeof document<"u"?S.useLayoutEffect:S.useEffect;function Hu(){return Hu=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Hu.apply(this,arguments)}let eh=!1,ck=0;const th=()=>"floating-ui-"+Math.random().toString(36).slice(2,6)+ck++;function dk(){const[e,t]=S.useState(()=>eh?th():void 0);return fa(()=>{e==null&&t(th())},[]),S.useEffect(()=>{eh=!0},[]),e}const fk=Ig.useId,Lg=fk||dk,hk=S.forwardRef(function(t,n){const{context:{placement:r,elements:{floating:s},middlewareData:{arrow:o,shift:i}},width:a=14,height:l=7,tipRadius:u=0,strokeWidth:c=0,staticOffset:d,stroke:f,d:p,style:{transform:y,..._}={},...x}=t,g=Lg(),[m,v]=S.useState(!1);if(fa(()=>{if(!s)return;vt(s).direction==="rtl"&&v(!0)},[s]),!s)return null;const[k,E]=r.split("-"),P=k==="top"||k==="bottom";let D=d;(P&&i!=null&&i.x||!P&&i!=null&&i.y)&&(D=null);const b=c*2,M=b/2,O=a/2*(u/-8+1),F=l/2*u/4,H=!!p,ee=D&&E==="end"?"bottom":"top";let A=D&&E==="end"?"right":"left";D&&m&&(A=E==="end"?"left":"right");const V=(o==null?void 0:o.x)!=null?D||o.x:"",Y=(o==null?void 0:o.y)!=null?D||o.y:"",ie=p||"M0,0"+(" H"+a)+(" L"+(a-O)+","+(l-F))+(" Q"+a/2+","+l+" "+O+","+(l-F))+" Z",N={top:H?"rotate(180deg)":"",left:H?"rotate(90deg)":"rotate(-90deg)",bottom:H?"":"rotate(180deg)",right:H?"rotate(-90deg)":"rotate(90deg)"}[k];return S.createElement("svg",Hu({},x,{"aria-hidden":!0,ref:n,width:H?a:a+b,height:a,viewBox:"0 0 "+a+" "+(l>a?l:a),style:{position:"absolute",pointerEvents:"none",[A]:V,[ee]:Y,[k]:P||H?"100%":"calc(100% - "+b/2+"px)",transform:[N,y].filter(L=>!!L).join(" "),..._}}),b>0&&S.createElement("path",{clipPath:"url(#"+g+")",fill:"none",stroke:f,strokeWidth:b+(p?0:1),d:ie}),S.createElement("path",{stroke:b&&!p?x.fill:"none",d:ie}),S.createElement("clipPath",{id:g},S.createElement("rect",{x:-M,y:M*(H?-1:1),width:a+b,height:a})))});function pk(){const e=new Map;return{emit(t,n){var r;(r=e.get(t))==null||r.forEach(s=>s(n))},on(t,n){e.set(t,[...e.get(t)||[],n])},off(t,n){var r;e.set(t,((r=e.get(t))==null?void 0:r.filter(s=>s!==n))||[])}}}const mk=S.createContext(null),gk=S.createContext(null),yk=()=>{var e;return((e=S.useContext(mk))==null?void 0:e.id)||null},vk=()=>S.useContext(gk);function wk(e){const{open:t=!1,onOpenChange:n,elements:r}=e,s=Lg(),o=S.useRef({}),[i]=S.useState(()=>pk()),a=yk()!=null,[l,u]=S.useState(r.reference),c=uk((p,y,_)=>{o.current.openEvent=p?y:void 0,i.emit("openchange",{open:p,event:y,reason:_,nested:a}),n==null||n(p,y,_)}),d=S.useMemo(()=>({setPositionReference:u}),[]),f=S.useMemo(()=>({reference:l||r.reference||null,floating:r.floating||null,domReference:r.reference}),[l,r.reference,r.floating]);return S.useMemo(()=>({dataRef:o,open:t,onOpenChange:c,elements:f,events:i,floatingId:s,refs:d}),[t,c,f,i,s,d])}function _k(e){e===void 0&&(e={});const{nodeId:t}=e,n=wk({...e,elements:{reference:null,floating:null,...e.elements}}),r=e.rootContext||n,s=r.elements,[o,i]=S.useState(null),[a,l]=S.useState(null),c=(s==null?void 0:s.domReference)||o,d=S.useRef(null),f=vk();fa(()=>{c&&(d.current=c)},[c]);const p=nk({...e,elements:{...s,...a&&{reference:a}}}),y=S.useCallback(v=>{const k=We(v)?{getBoundingClientRect:()=>v.getBoundingClientRect(),contextElement:v}:v;l(k),p.refs.setReference(k)},[p.refs]),_=S.useCallback(v=>{(We(v)||v===null)&&(d.current=v,i(v)),(We(p.refs.reference.current)||p.refs.reference.current===null||v!==null&&!We(v))&&p.refs.setReference(v)},[p.refs]),x=S.useMemo(()=>({...p.refs,setReference:_,setPositionReference:y,domReference:d}),[p.refs,_,y]),g=S.useMemo(()=>({...p.elements,domReference:c}),[p.elements,c]),m=S.useMemo(()=>({...p,...r,refs:x,elements:g,nodeId:t}),[p,x,g,t,r]);return fa(()=>{r.dataRef.current.floatingContext=m;const v=f==null?void 0:f.nodesRef.current.find(k=>k.id===t);v&&(v.context=m)}),S.useMemo(()=>({...p,context:m,refs:x,elements:g}),[p,x,g,m])}/*!
  react-datepicker v6.9.0
  https://github.com/Hacker0x01/react-datepicker
  Released under the MIT License.
*/function Pe(e,t,n){return t=ha(t),Sk(e,Ag()?Reflect.construct(t,n||[],ha(e).constructor):t.apply(e,n))}function Ag(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Ag=function(){return!!e})()}function nh(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(s){return Object.getOwnPropertyDescriptor(e,s).enumerable})),n.push.apply(n,r)}return n}function Kt(e){for(var t=1;t<arguments.length;t++){var n=arguments[t]!=null?arguments[t]:{};t%2?nh(Object(n),!0).forEach(function(r){w(e,r,n[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):nh(Object(n)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(n,r))})}return e}function xk(e,t){if(typeof e!="object"||!e)return e;var n=e[Symbol.toPrimitive];if(n!==void 0){var r=n.call(e,t||"default");if(typeof r!="object")return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function $g(e){var t=xk(e,"string");return typeof t=="symbol"?t:t+""}function Vu(e){"@babel/helpers - typeof";return Vu=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Vu(e)}function Ne(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function rh(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,$g(r.key),r)}}function Te(e,t,n){return t&&rh(e.prototype,t),n&&rh(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}function w(e,t,n){return t=$g(t),t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function _o(){return _o=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},_o.apply(this,arguments)}function Oe(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Qu(e,t)}function ha(e){return ha=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(n){return n.__proto__||Object.getPrototypeOf(n)},ha(e)}function Qu(e,t){return Qu=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(r,s){return r.__proto__=s,r},Qu(e,t)}function kk(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Sk(e,t){if(t&&(typeof t=="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return kk(e)}function Fn(e){return bk(e)||Dk(e)||Ck(e)||Ek()}function bk(e){if(Array.isArray(e))return Ku(e)}function Dk(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function Ck(e,t){if(e){if(typeof e=="string")return Ku(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);if(n==="Object"&&e.constructor&&(n=e.constructor.name),n==="Map"||n==="Set")return Array.from(e);if(n==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Ku(e,t)}}function Ku(e,t){(t==null||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function Ek(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}var Mo=12,Pk=/P+p+|P+|p+|''|'(''|[^'])+('|$)|./g;function X(e){var t=e?typeof e=="string"||e instanceof String?Vx(e):R(e):new Date;return pn(t)?t:null}function Nk(e,t,n,r,s){var o=null,i=jn(n)||jn(ar()),a=!0;return Array.isArray(t)?(t.forEach(function(l){var u=Ci(e,l,new Date,{locale:i,useAdditionalWeekYearTokens:!0,useAdditionalDayOfYearTokens:!0});r&&(a=pn(u,s)&&e===oe(u,l,n)),pn(u,s)&&a&&(o=u)}),o):(o=Ci(e,t,new Date,{locale:i,useAdditionalWeekYearTokens:!0,useAdditionalDayOfYearTokens:!0}),r?a=pn(o)&&e===oe(o,t,n):pn(o)||(t=t.match(Pk).map(function(l){var u=l[0];if(u==="p"||u==="P"){var c=na[u];return i?c(l,i.formatLong):u}return l}).join(""),e.length>0&&(o=Ci(e,t.slice(0,e.length),new Date,{useAdditionalWeekYearTokens:!0,useAdditionalDayOfYearTokens:!0})),pn(o)||(o=new Date(e))),pn(o)&&a?o:null)}function pn(e,t){return t=t||new Date("1/1/1000"),mo(e)&&!An(e,t)}function oe(e,t,n){if(n==="en")return qe(e,t,{useAdditionalWeekYearTokens:!0,useAdditionalDayOfYearTokens:!0});var r=jn(n);return n&&!r&&console.warn('A locale object was not found for the provided string ["'.concat(n,'"].')),!r&&ar()&&jn(ar())&&(r=jn(ar())),qe(e,t,{locale:r||null,useAdditionalWeekYearTokens:!0,useAdditionalDayOfYearTokens:!0})}function ct(e,t){var n=t.dateFormat,r=t.locale;return e&&oe(e,Array.isArray(n)?n[0]:n,r)||""}function Tk(e,t,n){if(!e)return"";var r=ct(e,n),s=t?ct(t,n):"";return"".concat(r," - ").concat(s)}function Ok(e,t){if(!(e!=null&&e.length))return"";var n=ct(e[0],t);if(e.length===1)return n;if(e.length===2){var r=ct(e[1],t);return"".concat(n,", ").concat(r)}var s=e.length-1;return"".concat(n," (+").concat(s,")")}function jl(e,t){var n=t.hour,r=n===void 0?0:n,s=t.minute,o=s===void 0?0:s,i=t.second,a=i===void 0?0:i;return Di(bi(Si(e,a),o),r)}function Mk(e,t){return ar()&&jn(ar()),td(e)}function jk(e,t){return oe(e,"ddd",t)}function Rk(e){return Ft(e)}function On(e,t,n){var r=jn(t||ar());return sn(e,{locale:r,weekStartsOn:n})}function Mn(e){return vg(e)}function Is(e){return $a(e)}function sh(e){return Fu(e)}function oh(){return Ft(X())}function Ik(e){return B_(e)}function Mt(e,t){return e&&t?K_(e,t):!e&&!t}function rt(e,t){return e&&t?Q_(e,t):!e&&!t}function pa(e,t){return e&&t?q_(e,t):!e&&!t}function K(e,t){return e&&t?V_(e,t):!e&&!t}function tr(e,t){return e&&t?H_(e,t):!e&&!t}function Ls(e,t,n){var r,s=Ft(t),o=Yu(n);try{r=yo(e,{start:s,end:o})}catch{r=!1}return r}function ar(){var e=typeof window<"u"?window:globalThis;return e.__localeId__}function jn(e){if(typeof e=="string"){var t=typeof window<"u"?window:globalThis;return t.__localeData__?t.__localeData__[e]:null}else return e}function Lk(e,t,n){return t(oe(e,"EEEE",n))}function Ak(e,t){return oe(e,"EEEEEE",t)}function $k(e,t){return oe(e,"EEE",t)}function hd(e,t){return oe(Ye(X(),e),"LLLL",t)}function Fg(e,t){return oe(Ye(X(),e),"LLL",t)}function Fk(e,t){return oe(Er(X(),e),"QQQ",t)}function za(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},n=t.minDate,r=t.maxDate,s=t.excludeDates,o=t.excludeDateIntervals,i=t.includeDates,a=t.includeDateIntervals,l=t.filterDate;return Ha(e,{minDate:n,maxDate:r})||s&&s.some(function(u){return K(e,u.date?u.date:u)})||o&&o.some(function(u){var c=u.start,d=u.end;return yo(e,{start:c,end:d})})||i&&!i.some(function(u){return K(e,u)})||a&&!a.some(function(u){var c=u.start,d=u.end;return yo(e,{start:c,end:d})})||l&&!l(X(e))||!1}function pd(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},n=t.excludeDates,r=t.excludeDateIntervals;return r&&r.length>0?r.some(function(s){var o=s.start,i=s.end;return yo(e,{start:o,end:i})}):n&&n.some(function(s){return K(e,s.date?s.date:s)})||!1}function Pi(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},n=t.minDate,r=t.maxDate,s=t.excludeDates,o=t.includeDates,i=t.filterDate;return Ha(e,{minDate:vg(n),maxDate:z_(r)})||s&&s.some(function(a){return rt(e,a)})||o&&!o.some(function(a){return rt(e,a)})||i&&!i(X(e))||!1}function ni(e,t,n,r){var s=W(e),o=Ue(e),i=W(t),a=Ue(t),l=W(r);if(s===i&&s===l)return o<=n&&n<=a;if(s<i)return l===s&&o<=n||l===i&&a>=n||l<i&&l>s}function Rl(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},n=t.minDate,r=t.maxDate,s=t.excludeDates,o=t.includeDates,i=t.filterDate;return Ha(e,{minDate:n,maxDate:r})||s&&s.some(function(a){return pa(e,a)})||o&&!o.some(function(a){return pa(e,a)})||i&&!i(X(e))||!1}function ri(e,t,n){if(!mo(t)||!mo(n))return!1;var r=W(t),s=W(n);return r<=e&&s>=e}function Yg(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},n=t.minDate,r=t.maxDate,s=t.excludeDates,o=t.includeDates,i=t.filterDate,a=new Date(e,0,1);return Ha(a,{minDate:$a(n),maxDate:wg(r)})||s&&s.some(function(l){return Mt(a,l)})||o&&!o.some(function(l){return Mt(a,l)})||i&&!i(X(a))||!1}function si(e,t,n,r){var s=W(e),o=or(e),i=W(t),a=or(t),l=W(r);if(s===i&&s===l)return o<=n&&n<=a;if(s<i)return l===s&&o<=n||l===i&&a>=n||l<i&&l>s}function Ha(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},n=t.minDate,r=t.maxDate;return n&&go(e,n)<0||r&&go(e,r)>0}function ih(e,t){return t.some(function(n){return Ut(n)===Ut(e)&&Yt(n)===Yt(e)&&Zt(n)===Zt(e)})}function ah(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},n=t.excludeTimes,r=t.includeTimes,s=t.filterTime;return n&&ih(e,n)||r&&!ih(e,r)||s&&!s(e)||!1}function lh(e,t){var n=t.minTime,r=t.maxTime;if(!n||!r)throw new Error("Both minTime and maxTime props required");var s=X();s=Di(s,Ut(e)),s=bi(s,Yt(e)),s=Si(s,Zt(e));var o=X();o=Di(o,Ut(n)),o=bi(o,Yt(n)),o=Si(o,Zt(n));var i=X();i=Di(i,Ut(r)),i=bi(i,Yt(r)),i=Si(i,Zt(r));var a;try{a=!yo(s,{start:o,end:i})}catch{a=!1}return a}function uh(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},n=t.minDate,r=t.includeDates,s=rs(e,1);return n&&sa(n,s)>0||r&&r.every(function(o){return sa(o,s)>0})||!1}function ch(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},n=t.maxDate,r=t.includeDates,s=Et(e,1);return n&&sa(s,n)>0||r&&r.every(function(o){return sa(s,o)>0})||!1}function Yk(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},n=t.minDate,r=t.includeDates,s=$a(e),o=yg(s,1);return n&&ia(n,o)>0||r&&r.every(function(i){return ia(i,o)>0})||!1}function Uk(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},n=t.maxDate,r=t.includeDates,s=wg(e),o=sd(s,1);return n&&ia(o,n)>0||r&&r.every(function(i){return ia(o,i)>0})||!1}function dh(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},n=t.minDate,r=t.includeDates,s=ss(e,1);return n&&oa(n,s)>0||r&&r.every(function(o){return oa(o,s)>0})||!1}function Wk(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},n=t.minDate,r=t.yearItemNumber,s=r===void 0?Mo:r,o=Is(ss(e,s)),i=kn(o,s),a=i.endPeriod,l=n&&W(n);return l&&l>a||!1}function fh(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},n=t.maxDate,r=t.includeDates,s=Xt(e,1);return n&&oa(s,n)>0||r&&r.every(function(o){return oa(s,o)>0})||!1}function Bk(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},n=t.maxDate,r=t.yearItemNumber,s=r===void 0?Mo:r,o=Xt(e,s),i=kn(o,s),a=i.startPeriod,l=n&&W(n);return l&&l<a||!1}function Ug(e){var t=e.minDate,n=e.includeDates;if(n&&t){var r=n.filter(function(s){return go(s,t)>=0});return zf(r)}else return n?zf(n):t}function Wg(e){var t=e.maxDate,n=e.includeDates;if(n&&t){var r=n.filter(function(s){return go(s,t)<=0});return Hf(r)}else return n?Hf(n):t}function hh(){for(var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"react-datepicker__day--highlighted",n=new Map,r=0,s=e.length;r<s;r++){var o=e[r];if(hr(o)){var i=oe(o,"MM.dd.yyyy"),a=n.get(i)||[];a.includes(t)||(a.push(t),n.set(i,a))}else if(Vu(o)==="object"){var l=Object.keys(o),u=l[0],c=o[l[0]];if(typeof u=="string"&&c.constructor===Array)for(var d=0,f=c.length;d<f;d++){var p=oe(c[d],"MM.dd.yyyy"),y=n.get(p)||[];y.includes(u)||(y.push(u),n.set(p,y))}}}return n}function zk(e,t){return e.length!==t.length?!1:e.every(function(n,r){return n===t[r]})}function Hk(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"react-datepicker__day--holidays",n=new Map;return e.forEach(function(r){var s=r.date,o=r.holidayName;if(hr(s)){var i=oe(s,"MM.dd.yyyy"),a=n.get(i)||{};if(!("className"in a&&a.className===t&&zk(a.holidayNames,[o]))){a.className=t;var l=a.holidayNames;a.holidayNames=l?[].concat(Fn(l),[o]):[o],n.set(i,a)}}}),n}function Vk(e,t,n,r,s){for(var o=s.length,i=[],a=0;a<o;a++){var l=e;l=F_(l,Ut(s[a])),l=Au(l,Yt(s[a])),l=l1(l,Zt(s[a]));var u=Au(e,(n+1)*r);Ln(l,t)&&An(l,u)&&i.push(s[a])}return i}function ph(e){return e<10?"0".concat(e):"".concat(e)}function kn(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:Mo,n=Math.ceil(W(e)/t)*t,r=n-(t-1);return{startPeriod:r,endPeriod:n}}function Qk(e){var t=new Date(e.getFullYear(),e.getMonth(),e.getDate()),n=new Date(e.getFullYear(),e.getMonth(),e.getDate(),24);return Math.round((+n-+t)/36e5)}function mh(e){var t=e.getSeconds(),n=e.getMilliseconds();return R(e.getTime()-t*1e3-n)}function Kk(e,t){return mh(e).getTime()===mh(t).getTime()}function gh(e){if(!hr(e))throw new Error("Invalid date");var t=new Date(e);return t.setHours(0,0,0,0),t}function yh(e,t){if(!hr(e)||!hr(t))throw new Error("Invalid date received");var n=gh(e),r=gh(t);return An(n,r)}function Bg(e){var t=" ";return e.key===t}function qk(e,t,n,r){for(var s=[],o=0;o<2*t+1;o++){var i=e+t-o,a=!0;n&&(a=W(n)<=i),r&&a&&(a=W(r)>=i),a&&s.push(i)}return s}var Gk=function(e){function t(n){var r;Ne(this,t),r=Pe(this,t,[n]),w(r,"renderOptions",function(){var a=r.props.year,l=r.state.yearsList.map(function(d){return C.createElement("div",{className:a===d?"react-datepicker__year-option react-datepicker__year-option--selected_year":"react-datepicker__year-option",key:d,onClick:r.onChange.bind(r,d),"aria-selected":a===d?"true":void 0},a===d?C.createElement("span",{className:"react-datepicker__year-option--selected"},"✓"):"",d)}),u=r.props.minDate?W(r.props.minDate):null,c=r.props.maxDate?W(r.props.maxDate):null;return(!c||!r.state.yearsList.find(function(d){return d===c}))&&l.unshift(C.createElement("div",{className:"react-datepicker__year-option",key:"upcoming",onClick:r.incrementYears},C.createElement("a",{className:"react-datepicker__navigation react-datepicker__navigation--years react-datepicker__navigation--years-upcoming"}))),(!u||!r.state.yearsList.find(function(d){return d===u}))&&l.push(C.createElement("div",{className:"react-datepicker__year-option",key:"previous",onClick:r.decrementYears},C.createElement("a",{className:"react-datepicker__navigation react-datepicker__navigation--years react-datepicker__navigation--years-previous"}))),l}),w(r,"onChange",function(a){r.props.onChange(a)}),w(r,"handleClickOutside",function(){r.props.onCancel()}),w(r,"shiftYears",function(a){var l=r.state.yearsList.map(function(u){return u+a});r.setState({yearsList:l})}),w(r,"incrementYears",function(){return r.shiftYears(1)}),w(r,"decrementYears",function(){return r.shiftYears(-1)});var s=n.yearDropdownItemNumber,o=n.scrollableYearDropdown,i=s||(o?10:5);return r.state={yearsList:qk(r.props.year,i,r.props.minDate,r.props.maxDate)},r.dropdownRef=S.createRef(),r}return Oe(t,e),Te(t,[{key:"componentDidMount",value:function(){var r=this.dropdownRef.current;if(r){var s=r.children?Array.from(r.children):null,o=s?s.find(function(i){return i.ariaSelected}):null;r.scrollTop=o?o.offsetTop+(o.clientHeight-r.clientHeight)/2:(r.scrollHeight-r.clientHeight)/2}}},{key:"render",value:function(){var r=be({"react-datepicker__year-dropdown":!0,"react-datepicker__year-dropdown--scrollable":this.props.scrollableYearDropdown});return C.createElement("div",{className:r,ref:this.dropdownRef},this.renderOptions())}}])}(C.Component),Jk=Fa(Gk),Xk=function(e){function t(){var n;Ne(this,t);for(var r=arguments.length,s=new Array(r),o=0;o<r;o++)s[o]=arguments[o];return n=Pe(this,t,[].concat(s)),w(n,"state",{dropdownVisible:!1}),w(n,"renderSelectOptions",function(){for(var i=n.props.minDate?W(n.props.minDate):1900,a=n.props.maxDate?W(n.props.maxDate):2100,l=[],u=i;u<=a;u++)l.push(C.createElement("option",{key:u,value:u},u));return l}),w(n,"onSelectChange",function(i){n.onChange(i.target.value)}),w(n,"renderSelectMode",function(){return C.createElement("select",{value:n.props.year,className:"react-datepicker__year-select",onChange:n.onSelectChange},n.renderSelectOptions())}),w(n,"renderReadView",function(i){return C.createElement("div",{key:"read",style:{visibility:i?"visible":"hidden"},className:"react-datepicker__year-read-view",onClick:function(l){return n.toggleDropdown(l)}},C.createElement("span",{className:"react-datepicker__year-read-view--down-arrow"}),C.createElement("span",{className:"react-datepicker__year-read-view--selected-year"},n.props.year))}),w(n,"renderDropdown",function(){return C.createElement(Jk,{key:"dropdown",year:n.props.year,onChange:n.onChange,onCancel:n.toggleDropdown,minDate:n.props.minDate,maxDate:n.props.maxDate,scrollableYearDropdown:n.props.scrollableYearDropdown,yearDropdownItemNumber:n.props.yearDropdownItemNumber})}),w(n,"renderScrollMode",function(){var i=n.state.dropdownVisible,a=[n.renderReadView(!i)];return i&&a.unshift(n.renderDropdown()),a}),w(n,"onChange",function(i){n.toggleDropdown(),i!==n.props.year&&n.props.onChange(i)}),w(n,"toggleDropdown",function(i){n.setState({dropdownVisible:!n.state.dropdownVisible},function(){n.props.adjustDateOnChange&&n.handleYearChange(n.props.date,i)})}),w(n,"handleYearChange",function(i,a){n.onSelect(i,a),n.setOpen()}),w(n,"onSelect",function(i,a){n.props.onSelect&&n.props.onSelect(i,a)}),w(n,"setOpen",function(){n.props.setOpen&&n.props.setOpen(!0)}),n}return Oe(t,e),Te(t,[{key:"render",value:function(){var r;switch(this.props.dropdownMode){case"scroll":r=this.renderScrollMode();break;case"select":r=this.renderSelectMode();break}return C.createElement("div",{className:"react-datepicker__year-dropdown-container react-datepicker__year-dropdown-container--".concat(this.props.dropdownMode)},r)}}])}(C.Component),Zk=function(e){function t(){var n;Ne(this,t);for(var r=arguments.length,s=new Array(r),o=0;o<r;o++)s[o]=arguments[o];return n=Pe(this,t,[].concat(s)),w(n,"isSelectedMonth",function(i){return n.props.month===i}),w(n,"renderOptions",function(){return n.props.monthNames.map(function(i,a){return C.createElement("div",{className:n.isSelectedMonth(a)?"react-datepicker__month-option react-datepicker__month-option--selected_month":"react-datepicker__month-option",key:i,onClick:n.onChange.bind(n,a),"aria-selected":n.isSelectedMonth(a)?"true":void 0},n.isSelectedMonth(a)?C.createElement("span",{className:"react-datepicker__month-option--selected"},"✓"):"",i)})}),w(n,"onChange",function(i){return n.props.onChange(i)}),w(n,"handleClickOutside",function(){return n.props.onCancel()}),n}return Oe(t,e),Te(t,[{key:"render",value:function(){return C.createElement("div",{className:"react-datepicker__month-dropdown"},this.renderOptions())}}])}(C.Component),eS=Fa(Zk),tS=function(e){function t(){var n;Ne(this,t);for(var r=arguments.length,s=new Array(r),o=0;o<r;o++)s[o]=arguments[o];return n=Pe(this,t,[].concat(s)),w(n,"state",{dropdownVisible:!1}),w(n,"renderSelectOptions",function(i){return i.map(function(a,l){return C.createElement("option",{key:l,value:l},a)})}),w(n,"renderSelectMode",function(i){return C.createElement("select",{value:n.props.month,className:"react-datepicker__month-select",onChange:function(l){return n.onChange(l.target.value)}},n.renderSelectOptions(i))}),w(n,"renderReadView",function(i,a){return C.createElement("div",{key:"read",style:{visibility:i?"visible":"hidden"},className:"react-datepicker__month-read-view",onClick:n.toggleDropdown},C.createElement("span",{className:"react-datepicker__month-read-view--down-arrow"}),C.createElement("span",{className:"react-datepicker__month-read-view--selected-month"},a[n.props.month]))}),w(n,"renderDropdown",function(i){return C.createElement(eS,{key:"dropdown",month:n.props.month,monthNames:i,onChange:n.onChange,onCancel:n.toggleDropdown})}),w(n,"renderScrollMode",function(i){var a=n.state.dropdownVisible,l=[n.renderReadView(!a,i)];return a&&l.unshift(n.renderDropdown(i)),l}),w(n,"onChange",function(i){n.toggleDropdown(),i!==n.props.month&&n.props.onChange(i)}),w(n,"toggleDropdown",function(){return n.setState({dropdownVisible:!n.state.dropdownVisible})}),n}return Oe(t,e),Te(t,[{key:"render",value:function(){var r=this,s=[0,1,2,3,4,5,6,7,8,9,10,11].map(this.props.useShortMonthInDropdown?function(i){return Fg(i,r.props.locale)}:function(i){return hd(i,r.props.locale)}),o;switch(this.props.dropdownMode){case"scroll":o=this.renderScrollMode(s);break;case"select":o=this.renderSelectMode(s);break}return C.createElement("div",{className:"react-datepicker__month-dropdown-container react-datepicker__month-dropdown-container--".concat(this.props.dropdownMode)},o)}}])}(C.Component);function nS(e,t){for(var n=[],r=Mn(e),s=Mn(t);!Ln(r,s);)n.push(X(r)),r=Et(r,1);return n}var rS=function(e){function t(n){var r;return Ne(this,t),r=Pe(this,t,[n]),w(r,"renderOptions",function(){return r.state.monthYearsList.map(function(s){var o=$u(s),i=Mt(r.props.date,s)&&rt(r.props.date,s);return C.createElement("div",{className:i?"react-datepicker__month-year-option--selected_month-year":"react-datepicker__month-year-option",key:o,onClick:r.onChange.bind(r,o),"aria-selected":i?"true":void 0},i?C.createElement("span",{className:"react-datepicker__month-year-option--selected"},"✓"):"",oe(s,r.props.dateFormat,r.props.locale))})}),w(r,"onChange",function(s){return r.props.onChange(s)}),w(r,"handleClickOutside",function(){r.props.onCancel()}),r.state={monthYearsList:nS(r.props.minDate,r.props.maxDate)},r}return Oe(t,e),Te(t,[{key:"render",value:function(){var r=be({"react-datepicker__month-year-dropdown":!0,"react-datepicker__month-year-dropdown--scrollable":this.props.scrollableMonthYearDropdown});return C.createElement("div",{className:r},this.renderOptions())}}])}(C.Component),sS=Fa(rS),oS=function(e){function t(){var n;Ne(this,t);for(var r=arguments.length,s=new Array(r),o=0;o<r;o++)s[o]=arguments[o];return n=Pe(this,t,[].concat(s)),w(n,"state",{dropdownVisible:!1}),w(n,"renderSelectOptions",function(){for(var i=Mn(n.props.minDate),a=Mn(n.props.maxDate),l=[];!Ln(i,a);){var u=$u(i);l.push(C.createElement("option",{key:u,value:u},oe(i,n.props.dateFormat,n.props.locale))),i=Et(i,1)}return l}),w(n,"onSelectChange",function(i){n.onChange(i.target.value)}),w(n,"renderSelectMode",function(){return C.createElement("select",{value:$u(Mn(n.props.date)),className:"react-datepicker__month-year-select",onChange:n.onSelectChange},n.renderSelectOptions())}),w(n,"renderReadView",function(i){var a=oe(n.props.date,n.props.dateFormat,n.props.locale);return C.createElement("div",{key:"read",style:{visibility:i?"visible":"hidden"},className:"react-datepicker__month-year-read-view",onClick:function(u){return n.toggleDropdown(u)}},C.createElement("span",{className:"react-datepicker__month-year-read-view--down-arrow"}),C.createElement("span",{className:"react-datepicker__month-year-read-view--selected-month-year"},a))}),w(n,"renderDropdown",function(){return C.createElement(sS,{key:"dropdown",date:n.props.date,dateFormat:n.props.dateFormat,onChange:n.onChange,onCancel:n.toggleDropdown,minDate:n.props.minDate,maxDate:n.props.maxDate,scrollableMonthYearDropdown:n.props.scrollableMonthYearDropdown,locale:n.props.locale})}),w(n,"renderScrollMode",function(){var i=n.state.dropdownVisible,a=[n.renderReadView(!i)];return i&&a.unshift(n.renderDropdown()),a}),w(n,"onChange",function(i){n.toggleDropdown();var a=X(parseInt(i));Mt(n.props.date,a)&&rt(n.props.date,a)||n.props.onChange(a)}),w(n,"toggleDropdown",function(){return n.setState({dropdownVisible:!n.state.dropdownVisible})}),n}return Oe(t,e),Te(t,[{key:"render",value:function(){var r;switch(this.props.dropdownMode){case"scroll":r=this.renderScrollMode();break;case"select":r=this.renderSelectMode();break}return C.createElement("div",{className:"react-datepicker__month-year-dropdown-container react-datepicker__month-year-dropdown-container--".concat(this.props.dropdownMode)},r)}}])}(C.Component),iS=function(e){function t(){var n;Ne(this,t);for(var r=arguments.length,s=new Array(r),o=0;o<r;o++)s[o]=arguments[o];return n=Pe(this,t,[].concat(s)),w(n,"dayEl",C.createRef()),w(n,"handleClick",function(i){!n.isDisabled()&&n.props.onClick&&n.props.onClick(i)}),w(n,"handleMouseEnter",function(i){!n.isDisabled()&&n.props.onMouseEnter&&n.props.onMouseEnter(i)}),w(n,"handleOnKeyDown",function(i){var a=i.key;a===" "&&(i.preventDefault(),i.key="Enter"),n.props.handleOnKeyDown(i)}),w(n,"isSameDay",function(i){return K(n.props.day,i)}),w(n,"isKeyboardSelected",function(){var i;if(n.props.disabledKeyboardNavigation)return!1;var a=n.props.selectsMultiple?(i=n.props.selectedDates)===null||i===void 0?void 0:i.some(function(l){return n.isSameDayOrWeek(l)}):n.isSameDayOrWeek(n.props.selected);return!a&&n.isSameDayOrWeek(n.props.preSelection)}),w(n,"isDisabled",function(){return za(n.props.day,n.props)}),w(n,"isExcluded",function(){return pd(n.props.day,n.props)}),w(n,"isStartOfWeek",function(){return K(n.props.day,On(n.props.day,n.props.locale,n.props.calendarStartDay))}),w(n,"isSameWeek",function(i){return n.props.showWeekPicker&&K(i,On(n.props.day,n.props.locale,n.props.calendarStartDay))}),w(n,"isSameDayOrWeek",function(i){return n.isSameDay(i)||n.isSameWeek(i)}),w(n,"getHighLightedClass",function(){var i=n.props,a=i.day,l=i.highlightDates;if(!l)return!1;var u=oe(a,"MM.dd.yyyy");return l.get(u)}),w(n,"getHolidaysClass",function(){var i=n.props,a=i.day,l=i.holidays;if(!l)return!1;var u=oe(a,"MM.dd.yyyy");if(l.has(u))return[l.get(u).className]}),w(n,"isInRange",function(){var i=n.props,a=i.day,l=i.startDate,u=i.endDate;return!l||!u?!1:Ls(a,l,u)}),w(n,"isInSelectingRange",function(){var i,a=n.props,l=a.day,u=a.selectsStart,c=a.selectsEnd,d=a.selectsRange,f=a.selectsDisabledDaysInRange,p=a.startDate,y=a.endDate,_=(i=n.props.selectingDate)!==null&&i!==void 0?i:n.props.preSelection;return!(u||c||d)||!_||!f&&n.isDisabled()?!1:u&&y&&(An(_,y)||tr(_,y))?Ls(l,_,y):c&&p&&(Ln(_,p)||tr(_,p))||d&&p&&!y&&(Ln(_,p)||tr(_,p))?Ls(l,p,_):!1}),w(n,"isSelectingRangeStart",function(){var i;if(!n.isInSelectingRange())return!1;var a=n.props,l=a.day,u=a.startDate,c=a.selectsStart,d=(i=n.props.selectingDate)!==null&&i!==void 0?i:n.props.preSelection;return c?K(l,d):K(l,u)}),w(n,"isSelectingRangeEnd",function(){var i;if(!n.isInSelectingRange())return!1;var a=n.props,l=a.day,u=a.endDate,c=a.selectsEnd,d=a.selectsRange,f=(i=n.props.selectingDate)!==null&&i!==void 0?i:n.props.preSelection;return c||d?K(l,f):K(l,u)}),w(n,"isRangeStart",function(){var i=n.props,a=i.day,l=i.startDate,u=i.endDate;return!l||!u?!1:K(l,a)}),w(n,"isRangeEnd",function(){var i=n.props,a=i.day,l=i.startDate,u=i.endDate;return!l||!u?!1:K(u,a)}),w(n,"isWeekend",function(){var i=U_(n.props.day);return i===0||i===6}),w(n,"isAfterMonth",function(){return n.props.month!==void 0&&(n.props.month+1)%12===Ue(n.props.day)}),w(n,"isBeforeMonth",function(){return n.props.month!==void 0&&(Ue(n.props.day)+1)%12===n.props.month}),w(n,"isCurrentDay",function(){return n.isSameDay(X())}),w(n,"isSelected",function(){if(n.props.selectsMultiple){var i;return(i=n.props.selectedDates)===null||i===void 0?void 0:i.some(function(a){return n.isSameDayOrWeek(a)})}return n.isSameDayOrWeek(n.props.selected)}),w(n,"getClassNames",function(i){var a=n.props.dayClassName?n.props.dayClassName(i):void 0;return be("react-datepicker__day",a,"react-datepicker__day--"+jk(n.props.day),{"react-datepicker__day--disabled":n.isDisabled(),"react-datepicker__day--excluded":n.isExcluded(),"react-datepicker__day--selected":n.isSelected(),"react-datepicker__day--keyboard-selected":n.isKeyboardSelected(),"react-datepicker__day--range-start":n.isRangeStart(),"react-datepicker__day--range-end":n.isRangeEnd(),"react-datepicker__day--in-range":n.isInRange(),"react-datepicker__day--in-selecting-range":n.isInSelectingRange(),"react-datepicker__day--selecting-range-start":n.isSelectingRangeStart(),"react-datepicker__day--selecting-range-end":n.isSelectingRangeEnd(),"react-datepicker__day--today":n.isCurrentDay(),"react-datepicker__day--weekend":n.isWeekend(),"react-datepicker__day--outside-month":n.isAfterMonth()||n.isBeforeMonth()},n.getHighLightedClass("react-datepicker__day--highlighted"),n.getHolidaysClass())}),w(n,"getAriaLabel",function(){var i=n.props,a=i.day,l=i.ariaLabelPrefixWhenEnabled,u=l===void 0?"Choose":l,c=i.ariaLabelPrefixWhenDisabled,d=c===void 0?"Not available":c,f=n.isDisabled()||n.isExcluded()?d:u;return"".concat(f," ").concat(oe(a,"PPPP",n.props.locale))}),w(n,"getTitle",function(){var i=n.props,a=i.day,l=i.holidays,u=l===void 0?new Map:l,c=i.excludeDates,d=oe(a,"MM.dd.yyyy"),f=[];return u.has(d)&&f.push.apply(f,Fn(u.get(d).holidayNames)),n.isExcluded()&&f.push(c==null?void 0:c.filter(function(p){return K(p.date?p.date:p,a)}).map(function(p){return p.message})),f.join(", ")}),w(n,"getTabIndex",function(i,a){var l=i||n.props.selected,u=a||n.props.preSelection,c=!(n.props.showWeekPicker&&(n.props.showWeekNumber||!n.isStartOfWeek()))&&(n.isKeyboardSelected()||n.isSameDay(l)&&K(u,l))?0:-1;return c}),w(n,"handleFocusDay",function(){var i,a=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},l=!1;n.getTabIndex()===0&&!a.isInputFocused&&n.isSameDay(n.props.preSelection)&&((!document.activeElement||document.activeElement===document.body)&&(l=!0),n.props.inline&&!n.props.shouldFocusDayInline&&(l=!1),n.props.containerRef&&n.props.containerRef.current&&n.props.containerRef.current.contains(document.activeElement)&&document.activeElement.classList.contains("react-datepicker__day")&&(l=!0),n.props.monthShowsDuplicateDaysEnd&&n.isAfterMonth()&&(l=!1),n.props.monthShowsDuplicateDaysStart&&n.isBeforeMonth()&&(l=!1)),l&&((i=n.dayEl.current)===null||i===void 0||i.focus({preventScroll:!0}))}),w(n,"renderDayContents",function(){return n.props.monthShowsDuplicateDaysEnd&&n.isAfterMonth()||n.props.monthShowsDuplicateDaysStart&&n.isBeforeMonth()?null:n.props.renderDayContents?n.props.renderDayContents(Bf(n.props.day),n.props.day):Bf(n.props.day)}),w(n,"render",function(){return C.createElement("div",{ref:n.dayEl,className:n.getClassNames(n.props.day),onKeyDown:n.handleOnKeyDown,onClick:n.handleClick,onMouseEnter:n.props.usePointerEvent?void 0:n.handleMouseEnter,onPointerEnter:n.props.usePointerEvent?n.handleMouseEnter:void 0,tabIndex:n.getTabIndex(),"aria-label":n.getAriaLabel(),role:"option",title:n.getTitle(),"aria-disabled":n.isDisabled(),"aria-current":n.isCurrentDay()?"date":void 0,"aria-selected":n.isSelected()||n.isInRange()},n.renderDayContents(),n.getTitle()!==""&&C.createElement("span",{className:"overlay"},n.getTitle()))}),n}return Oe(t,e),Te(t,[{key:"componentDidMount",value:function(){this.handleFocusDay()}},{key:"componentDidUpdate",value:function(r){this.handleFocusDay(r)}}])}(C.Component),aS=function(e){function t(){var n;Ne(this,t);for(var r=arguments.length,s=new Array(r),o=0;o<r;o++)s[o]=arguments[o];return n=Pe(this,t,[].concat(s)),w(n,"weekNumberEl",C.createRef()),w(n,"handleClick",function(i){n.props.onClick&&n.props.onClick(i)}),w(n,"handleOnKeyDown",function(i){var a=i.key;a===" "&&(i.preventDefault(),i.key="Enter"),n.props.handleOnKeyDown(i)}),w(n,"isKeyboardSelected",function(){return!n.props.disabledKeyboardNavigation&&!K(n.props.date,n.props.selected)&&K(n.props.date,n.props.preSelection)}),w(n,"getTabIndex",function(){return n.props.showWeekPicker&&n.props.showWeekNumber&&(n.isKeyboardSelected()||K(n.props.date,n.props.selected)&&K(n.props.preSelection,n.props.selected))?0:-1}),w(n,"handleFocusWeekNumber",function(){var i=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},a=!1;n.getTabIndex()===0&&!i.isInputFocused&&K(n.props.date,n.props.preSelection)&&((!document.activeElement||document.activeElement===document.body)&&(a=!0),n.props.inline&&!n.props.shouldFocusDayInline&&(a=!1),n.props.containerRef&&n.props.containerRef.current&&n.props.containerRef.current.contains(document.activeElement)&&document.activeElement&&document.activeElement.classList.contains("react-datepicker__week-number")&&(a=!0)),a&&n.weekNumberEl.current&&n.weekNumberEl.current.focus({preventScroll:!0})}),n}return Oe(t,e),Te(t,[{key:"componentDidMount",value:function(){this.handleFocusWeekNumber()}},{key:"componentDidUpdate",value:function(r){this.handleFocusWeekNumber(r)}},{key:"render",value:function(){var r=this.props,s=r.weekNumber,o=r.ariaLabelPrefix,i=o===void 0?"week ":o,a=r.onClick,l={"react-datepicker__week-number":!0,"react-datepicker__week-number--clickable":!!a,"react-datepicker__week-number--selected":!!a&&K(this.props.date,this.props.selected),"react-datepicker__week-number--keyboard-selected":this.isKeyboardSelected()};return C.createElement("div",{ref:this.weekNumberEl,className:be(l),"aria-label":"".concat(i," ").concat(this.props.weekNumber),onClick:this.handleClick,onKeyDown:this.handleOnKeyDown,tabIndex:this.getTabIndex()},s)}}],[{key:"defaultProps",get:function(){return{ariaLabelPrefix:"week "}}}])}(C.Component),lS=function(e){function t(){var n;Ne(this,t);for(var r=arguments.length,s=new Array(r),o=0;o<r;o++)s[o]=arguments[o];return n=Pe(this,t,[].concat(s)),w(n,"handleDayClick",function(i,a){n.props.onDayClick&&n.props.onDayClick(i,a)}),w(n,"handleDayMouseEnter",function(i){n.props.onDayMouseEnter&&n.props.onDayMouseEnter(i)}),w(n,"handleWeekClick",function(i,a,l){typeof n.props.onWeekSelect=="function"&&n.props.onWeekSelect(i,a,l),n.props.showWeekPicker&&n.handleDayClick(i,l),n.props.shouldCloseOnSelect&&n.props.setOpen(!1)}),w(n,"formatWeekNumber",function(i){return n.props.formatWeekNumber?n.props.formatWeekNumber(i):Mk(i)}),w(n,"renderDays",function(){var i=n.startOfWeek(),a=[],l=n.formatWeekNumber(i);if(n.props.showWeekNumber){var u=n.props.onWeekSelect||n.props.showWeekPicker?n.handleWeekClick.bind(n,i,l):void 0;a.push(C.createElement(aS,{key:"W",weekNumber:l,date:i,onClick:u,selected:n.props.selected,preSelection:n.props.preSelection,ariaLabelPrefix:n.props.ariaLabelPrefix,showWeekPicker:n.props.showWeekPicker,showWeekNumber:n.props.showWeekNumber,disabledKeyboardNavigation:n.props.disabledKeyboardNavigation,handleOnKeyDown:n.props.handleOnKeyDown,isInputFocused:n.props.isInputFocused,containerRef:n.props.containerRef}))}return a.concat([0,1,2,3,4,5,6].map(function(c){var d=Bn(i,c);return C.createElement(iS,{ariaLabelPrefixWhenEnabled:n.props.chooseDayAriaLabelPrefix,ariaLabelPrefixWhenDisabled:n.props.disabledDayAriaLabelPrefix,key:d.valueOf(),day:d,month:n.props.month,onClick:n.handleDayClick.bind(n,d),usePointerEvent:n.props.usePointerEvent,onMouseEnter:n.handleDayMouseEnter.bind(n,d),minDate:n.props.minDate,maxDate:n.props.maxDate,calendarStartDay:n.props.calendarStartDay,excludeDates:n.props.excludeDates,excludeDateIntervals:n.props.excludeDateIntervals,includeDates:n.props.includeDates,includeDateIntervals:n.props.includeDateIntervals,highlightDates:n.props.highlightDates,holidays:n.props.holidays,selectingDate:n.props.selectingDate,filterDate:n.props.filterDate,preSelection:n.props.preSelection,selected:n.props.selected,selectsStart:n.props.selectsStart,selectsEnd:n.props.selectsEnd,selectsRange:n.props.selectsRange,showWeekPicker:n.props.showWeekPicker,showWeekNumber:n.props.showWeekNumber,selectsDisabledDaysInRange:n.props.selectsDisabledDaysInRange,selectsMultiple:n.props.selectsMultiple,selectedDates:n.props.selectedDates,startDate:n.props.startDate,endDate:n.props.endDate,dayClassName:n.props.dayClassName,renderDayContents:n.props.renderDayContents,disabledKeyboardNavigation:n.props.disabledKeyboardNavigation,handleOnKeyDown:n.props.handleOnKeyDown,isInputFocused:n.props.isInputFocused,containerRef:n.props.containerRef,inline:n.props.inline,shouldFocusDayInline:n.props.shouldFocusDayInline,monthShowsDuplicateDaysEnd:n.props.monthShowsDuplicateDaysEnd,monthShowsDuplicateDaysStart:n.props.monthShowsDuplicateDaysStart,locale:n.props.locale})}))}),w(n,"startOfWeek",function(){return On(n.props.day,n.props.locale,n.props.calendarStartDay)}),w(n,"isKeyboardSelected",function(){return!n.props.disabledKeyboardNavigation&&!K(n.startOfWeek(),n.props.selected)&&K(n.startOfWeek(),n.props.preSelection)}),n}return Oe(t,e),Te(t,[{key:"render",value:function(){var r={"react-datepicker__week":!0,"react-datepicker__week--selected":K(this.startOfWeek(),this.props.selected),"react-datepicker__week--keyboard-selected":this.isKeyboardSelected()};return C.createElement("div",{className:be(r)},this.renderDays())}}],[{key:"defaultProps",get:function(){return{shouldCloseOnSelect:!0}}}])}(C.Component),uS=6,Qr={TWO_COLUMNS:"two_columns",THREE_COLUMNS:"three_columns",FOUR_COLUMNS:"four_columns"},Il=w(w(w({},Qr.TWO_COLUMNS,{grid:[[0,1],[2,3],[4,5],[6,7],[8,9],[10,11]],verticalNavigationOffset:2}),Qr.THREE_COLUMNS,{grid:[[0,1,2],[3,4,5],[6,7,8],[9,10,11]],verticalNavigationOffset:3}),Qr.FOUR_COLUMNS,{grid:[[0,1,2,3],[4,5,6,7],[8,9,10,11]],verticalNavigationOffset:4}),oi=1;function vh(e,t){return e?Qr.FOUR_COLUMNS:t?Qr.TWO_COLUMNS:Qr.THREE_COLUMNS}var cS=function(e){function t(){var n;Ne(this,t);for(var r=arguments.length,s=new Array(r),o=0;o<r;o++)s[o]=arguments[o];return n=Pe(this,t,[].concat(s)),w(n,"MONTH_REFS",Fn(Array(12)).map(function(){return C.createRef()})),w(n,"QUARTER_REFS",Fn(Array(4)).map(function(){return C.createRef()})),w(n,"isDisabled",function(i){return za(i,n.props)}),w(n,"isExcluded",function(i){return pd(i,n.props)}),w(n,"handleDayClick",function(i,a){n.props.onDayClick&&n.props.onDayClick(i,a,n.props.orderInDisplay)}),w(n,"handleDayMouseEnter",function(i){n.props.onDayMouseEnter&&n.props.onDayMouseEnter(i)}),w(n,"handleMouseLeave",function(){n.props.onMouseLeave&&n.props.onMouseLeave()}),w(n,"isRangeStartMonth",function(i){var a=n.props,l=a.day,u=a.startDate,c=a.endDate;return!u||!c?!1:rt(Ye(l,i),u)}),w(n,"isRangeStartQuarter",function(i){var a=n.props,l=a.day,u=a.startDate,c=a.endDate;return!u||!c?!1:pa(Er(l,i),u)}),w(n,"isRangeEndMonth",function(i){var a=n.props,l=a.day,u=a.startDate,c=a.endDate;return!u||!c?!1:rt(Ye(l,i),c)}),w(n,"isRangeEndQuarter",function(i){var a=n.props,l=a.day,u=a.startDate,c=a.endDate;return!u||!c?!1:pa(Er(l,i),c)}),w(n,"isInSelectingRangeMonth",function(i){var a,l=n.props,u=l.day,c=l.selectsStart,d=l.selectsEnd,f=l.selectsRange,p=l.startDate,y=l.endDate,_=(a=n.props.selectingDate)!==null&&a!==void 0?a:n.props.preSelection;return!(c||d||f)||!_?!1:c&&y?ni(_,y,i,u):d&&p||f&&p&&!y?ni(p,_,i,u):!1}),w(n,"isSelectingMonthRangeStart",function(i){var a;if(!n.isInSelectingRangeMonth(i))return!1;var l=n.props,u=l.day,c=l.startDate,d=l.selectsStart,f=Ye(u,i),p=(a=n.props.selectingDate)!==null&&a!==void 0?a:n.props.preSelection;return d?rt(f,p):rt(f,c)}),w(n,"isSelectingMonthRangeEnd",function(i){var a;if(!n.isInSelectingRangeMonth(i))return!1;var l=n.props,u=l.day,c=l.endDate,d=l.selectsEnd,f=l.selectsRange,p=Ye(u,i),y=(a=n.props.selectingDate)!==null&&a!==void 0?a:n.props.preSelection;return d||f?rt(p,y):rt(p,c)}),w(n,"isInSelectingRangeQuarter",function(i){var a,l=n.props,u=l.day,c=l.selectsStart,d=l.selectsEnd,f=l.selectsRange,p=l.startDate,y=l.endDate,_=(a=n.props.selectingDate)!==null&&a!==void 0?a:n.props.preSelection;return!(c||d||f)||!_?!1:c&&y?si(_,y,i,u):d&&p||f&&p&&!y?si(p,_,i,u):!1}),w(n,"isWeekInMonth",function(i){var a=n.props.day,l=Bn(i,6);return rt(i,a)||rt(l,a)}),w(n,"isCurrentMonth",function(i,a){return W(i)===W(X())&&a===Ue(X())}),w(n,"isCurrentQuarter",function(i,a){return W(i)===W(X())&&a===or(X())}),w(n,"isSelectedMonth",function(i,a,l){return Ue(l)===a&&W(i)===W(l)}),w(n,"isSelectedQuarter",function(i,a,l){return or(i)===a&&W(i)===W(l)}),w(n,"renderWeeks",function(){for(var i=[],a=n.props.fixedHeight,l=0,u=!1,c=On(Mn(n.props.day),n.props.locale,n.props.calendarStartDay),d=n.props.showWeekPicker?On(n.props.selected,n.props.locale,n.props.calendarStartDay):n.props.selected,f=n.props.showWeekPicker?On(n.props.preSelection,n.props.locale,n.props.calendarStartDay):n.props.preSelection;i.push(C.createElement(lS,{ariaLabelPrefix:n.props.weekAriaLabelPrefix,chooseDayAriaLabelPrefix:n.props.chooseDayAriaLabelPrefix,disabledDayAriaLabelPrefix:n.props.disabledDayAriaLabelPrefix,key:l,day:c,month:Ue(n.props.day),onDayClick:n.handleDayClick,usePointerEvent:n.props.usePointerEvent,onDayMouseEnter:n.handleDayMouseEnter,onWeekSelect:n.props.onWeekSelect,formatWeekNumber:n.props.formatWeekNumber,locale:n.props.locale,minDate:n.props.minDate,maxDate:n.props.maxDate,excludeDates:n.props.excludeDates,excludeDateIntervals:n.props.excludeDateIntervals,includeDates:n.props.includeDates,includeDateIntervals:n.props.includeDateIntervals,inline:n.props.inline,shouldFocusDayInline:n.props.shouldFocusDayInline,highlightDates:n.props.highlightDates,holidays:n.props.holidays,selectingDate:n.props.selectingDate,filterDate:n.props.filterDate,preSelection:f,selected:d,selectsStart:n.props.selectsStart,selectsEnd:n.props.selectsEnd,selectsRange:n.props.selectsRange,selectsDisabledDaysInRange:n.props.selectsDisabledDaysInRange,selectsMultiple:n.props.selectsMultiple,selectedDates:n.props.selectedDates,showWeekNumber:n.props.showWeekNumbers,showWeekPicker:n.props.showWeekPicker,startDate:n.props.startDate,endDate:n.props.endDate,dayClassName:n.props.dayClassName,setOpen:n.props.setOpen,shouldCloseOnSelect:n.props.shouldCloseOnSelect,disabledKeyboardNavigation:n.props.disabledKeyboardNavigation,renderDayContents:n.props.renderDayContents,handleOnKeyDown:n.props.handleOnKeyDown,isInputFocused:n.props.isInputFocused,containerRef:n.props.containerRef,calendarStartDay:n.props.calendarStartDay,monthShowsDuplicateDaysEnd:n.props.monthShowsDuplicateDaysEnd,monthShowsDuplicateDaysStart:n.props.monthShowsDuplicateDaysStart})),!u;){l++,c=ra(c,1);var p=a&&l>=uS,y=!a&&!n.isWeekInMonth(c);if(p||y)if(n.props.peekNextMonth)u=!0;else break}return i}),w(n,"onMonthClick",function(i,a){var l=Ye(n.props.day,a);Pi(l,n.props)||n.handleDayClick(Mn(l),i)}),w(n,"onMonthMouseEnter",function(i){var a=Ye(n.props.day,i);Pi(a,n.props)||n.handleDayMouseEnter(Mn(a))}),w(n,"handleMonthNavigation",function(i,a){n.isDisabled(a)||n.isExcluded(a)||(n.props.setPreSelection(a),n.MONTH_REFS[i].current&&n.MONTH_REFS[i].current.focus())}),w(n,"onMonthKeyDown",function(i,a){var l=n.props,u=l.selected,c=l.preSelection,d=l.disabledKeyboardNavigation,f=l.showTwoColumnMonthYearPicker,p=l.showFourColumnMonthYearPicker,y=l.setPreSelection,_=l.handleOnMonthKeyDown,x=i.key;if(x!=="Tab"&&i.preventDefault(),!d){var g=vh(p,f),m=Il[g].verticalNavigationOffset,v=Il[g].grid;switch(x){case"Enter":n.onMonthClick(i,a),y(u);break;case"ArrowRight":n.handleMonthNavigation(a===11?0:a+oi,Et(c,oi));break;case"ArrowLeft":n.handleMonthNavigation(a===0?11:a-oi,rs(c,oi));break;case"ArrowUp":n.handleMonthNavigation(v[0].includes(a)?a+12-m:a-m,rs(c,m));break;case"ArrowDown":n.handleMonthNavigation(v[v.length-1].includes(a)?a-12+m:a+m,Et(c,m));break}}_&&_(i)}),w(n,"onQuarterClick",function(i,a){var l=Er(n.props.day,a);Rl(l,n.props)||n.handleDayClick(sh(l),i)}),w(n,"onQuarterMouseEnter",function(i){var a=Er(n.props.day,i);Rl(a,n.props)||n.handleDayMouseEnter(sh(a))}),w(n,"handleQuarterNavigation",function(i,a){n.isDisabled(a)||n.isExcluded(a)||(n.props.setPreSelection(a),n.QUARTER_REFS[i-1].current&&n.QUARTER_REFS[i-1].current.focus())}),w(n,"onQuarterKeyDown",function(i,a){var l=i.key;if(!n.props.disabledKeyboardNavigation)switch(l){case"Enter":n.onQuarterClick(i,a),n.props.setPreSelection(n.props.selected);break;case"ArrowRight":n.handleQuarterNavigation(a===4?1:a+1,sd(n.props.preSelection,1));break;case"ArrowLeft":n.handleQuarterNavigation(a===1?4:a-1,yg(n.props.preSelection,1));break}}),w(n,"isMonthDisabled",function(i){var a=n.props,l=a.day,u=a.minDate,c=a.maxDate,d=a.excludeDates,f=a.includeDates,p=Ye(l,i);return(u||c||d||f)&&Pi(p,n.props)}),w(n,"getMonthClassNames",function(i){var a=n.props,l=a.day,u=a.startDate,c=a.endDate,d=a.selected,f=a.preSelection,p=a.monthClassName,y=p?p(Ye(l,i)):void 0;return be("react-datepicker__month-text","react-datepicker__month-".concat(i),y,{"react-datepicker__month-text--disabled":n.isMonthDisabled(i),"react-datepicker__month-text--selected":n.isSelectedMonth(l,i,d),"react-datepicker__month-text--keyboard-selected":!n.props.disabledKeyboardNavigation&&n.isSelectedMonth(l,i,f),"react-datepicker__month-text--in-selecting-range":n.isInSelectingRangeMonth(i),"react-datepicker__month-text--in-range":ni(u,c,i,l),"react-datepicker__month-text--range-start":n.isRangeStartMonth(i),"react-datepicker__month-text--range-end":n.isRangeEndMonth(i),"react-datepicker__month-text--selecting-range-start":n.isSelectingMonthRangeStart(i),"react-datepicker__month-text--selecting-range-end":n.isSelectingMonthRangeEnd(i),"react-datepicker__month-text--today":n.isCurrentMonth(l,i)})}),w(n,"getTabIndex",function(i){var a=Ue(n.props.preSelection),l=!n.props.disabledKeyboardNavigation&&i===a?"0":"-1";return l}),w(n,"getQuarterTabIndex",function(i){var a=or(n.props.preSelection),l=!n.props.disabledKeyboardNavigation&&i===a?"0":"-1";return l}),w(n,"getAriaLabel",function(i){var a=n.props,l=a.chooseDayAriaLabelPrefix,u=l===void 0?"Choose":l,c=a.disabledDayAriaLabelPrefix,d=c===void 0?"Not available":c,f=a.day,p=a.locale,y=Ye(f,i),_=n.isDisabled(y)||n.isExcluded(y)?d:u;return"".concat(_," ").concat(oe(y,"MMMM yyyy",p))}),w(n,"getQuarterClassNames",function(i){var a=n.props,l=a.day,u=a.startDate,c=a.endDate,d=a.selected,f=a.minDate,p=a.maxDate,y=a.preSelection,_=a.disabledKeyboardNavigation;return be("react-datepicker__quarter-text","react-datepicker__quarter-".concat(i),{"react-datepicker__quarter-text--disabled":(f||p)&&Rl(Er(l,i),n.props),"react-datepicker__quarter-text--selected":n.isSelectedQuarter(l,i,d),"react-datepicker__quarter-text--keyboard-selected":!_&&n.isSelectedQuarter(l,i,y),"react-datepicker__quarter-text--in-selecting-range":n.isInSelectingRangeQuarter(i),"react-datepicker__quarter-text--in-range":si(u,c,i,l),"react-datepicker__quarter-text--range-start":n.isRangeStartQuarter(i),"react-datepicker__quarter-text--range-end":n.isRangeEndQuarter(i)})}),w(n,"getMonthContent",function(i){var a=n.props,l=a.showFullMonthYearPicker,u=a.renderMonthContent,c=a.locale,d=a.day,f=Fg(i,c),p=hd(i,c);return u?u(i,f,p,d):l?p:f}),w(n,"getQuarterContent",function(i){var a=n.props,l=a.renderQuarterContent,u=a.locale,c=Fk(i,u);return l?l(i,c):c}),w(n,"renderMonths",function(){var i=n.props,a=i.showTwoColumnMonthYearPicker,l=i.showFourColumnMonthYearPicker,u=i.day,c=i.selected,d=Il[vh(l,a)].grid;return d.map(function(f,p){return C.createElement("div",{className:"react-datepicker__month-wrapper",key:p},f.map(function(y,_){return C.createElement("div",{ref:n.MONTH_REFS[y],key:_,onClick:function(g){n.onMonthClick(g,y)},onKeyDown:function(g){Bg(g)&&(g.preventDefault(),g.key="Enter"),n.onMonthKeyDown(g,y)},onMouseEnter:n.props.usePointerEvent?void 0:function(){return n.onMonthMouseEnter(y)},onPointerEnter:n.props.usePointerEvent?function(){return n.onMonthMouseEnter(y)}:void 0,tabIndex:n.getTabIndex(y),className:n.getMonthClassNames(y),"aria-disabled":n.isMonthDisabled(y),role:"option","aria-label":n.getAriaLabel(y),"aria-current":n.isCurrentMonth(u,y)?"date":void 0,"aria-selected":n.isSelectedMonth(u,y,c)},n.getMonthContent(y))}))})}),w(n,"renderQuarters",function(){var i=n.props,a=i.day,l=i.selected,u=[1,2,3,4];return C.createElement("div",{className:"react-datepicker__quarter-wrapper"},u.map(function(c,d){return C.createElement("div",{key:d,ref:n.QUARTER_REFS[d],role:"option",onClick:function(p){n.onQuarterClick(p,c)},onKeyDown:function(p){n.onQuarterKeyDown(p,c)},onMouseEnter:n.props.usePointerEvent?void 0:function(){return n.onQuarterMouseEnter(c)},onPointerEnter:n.props.usePointerEvent?function(){return n.onQuarterMouseEnter(c)}:void 0,className:n.getQuarterClassNames(c),"aria-selected":n.isSelectedQuarter(a,c,l),tabIndex:n.getQuarterTabIndex(c),"aria-current":n.isCurrentQuarter(a,c)?"date":void 0},n.getQuarterContent(c))}))}),w(n,"getClassNames",function(){var i=n.props,a=i.selectingDate,l=i.selectsStart,u=i.selectsEnd,c=i.showMonthYearPicker,d=i.showQuarterYearPicker,f=i.showWeekPicker;return be("react-datepicker__month",{"react-datepicker__month--selecting-range":a&&(l||u)},{"react-datepicker__monthPicker":c},{"react-datepicker__quarterPicker":d},{"react-datepicker__weekPicker":f})}),n}return Oe(t,e),Te(t,[{key:"render",value:function(){var r=this.props,s=r.showMonthYearPicker,o=r.showQuarterYearPicker,i=r.day,a=r.ariaLabelPrefix,l=a===void 0?"Month ":a,u=l?l.trim()+" ":"";return C.createElement("div",{className:this.getClassNames(),onMouseLeave:this.props.usePointerEvent?void 0:this.handleMouseLeave,onPointerLeave:this.props.usePointerEvent?this.handleMouseLeave:void 0,"aria-label":"".concat(u).concat(oe(i,"MMMM, yyyy",this.props.locale)),role:"listbox"},s?this.renderMonths():o?this.renderQuarters():this.renderWeeks())}}])}(C.Component),zg=function(e){function t(){var n;Ne(this,t);for(var r=arguments.length,s=new Array(r),o=0;o<r;o++)s[o]=arguments[o];return n=Pe(this,t,[].concat(s)),w(n,"state",{height:null}),w(n,"scrollToTheSelectedTime",function(){requestAnimationFrame(function(){n.list&&(n.list.scrollTop=n.centerLi&&t.calcCenterPosition(n.props.monthRef?n.props.monthRef.clientHeight-n.header.clientHeight:n.list.clientHeight,n.centerLi))})}),w(n,"handleClick",function(i){(n.props.minTime||n.props.maxTime)&&lh(i,n.props)||(n.props.excludeTimes||n.props.includeTimes||n.props.filterTime)&&ah(i,n.props)||n.props.onChange(i)}),w(n,"isSelectedTime",function(i){return n.props.selected&&Kk(n.props.selected,i)}),w(n,"isDisabledTime",function(i){return(n.props.minTime||n.props.maxTime)&&lh(i,n.props)||(n.props.excludeTimes||n.props.includeTimes||n.props.filterTime)&&ah(i,n.props)}),w(n,"liClasses",function(i){var a=["react-datepicker__time-list-item",n.props.timeClassName?n.props.timeClassName(i):void 0];return n.isSelectedTime(i)&&a.push("react-datepicker__time-list-item--selected"),n.isDisabledTime(i)&&a.push("react-datepicker__time-list-item--disabled"),n.props.injectTimes&&(Ut(i)*3600+Yt(i)*60+Zt(i))%(n.props.intervals*60)!==0&&a.push("react-datepicker__time-list-item--injected"),a.join(" ")}),w(n,"handleOnKeyDown",function(i,a){i.key===" "&&(i.preventDefault(),i.key="Enter"),(i.key==="ArrowUp"||i.key==="ArrowLeft")&&i.target.previousSibling&&(i.preventDefault(),i.target.previousSibling.focus()),(i.key==="ArrowDown"||i.key==="ArrowRight")&&i.target.nextSibling&&(i.preventDefault(),i.target.nextSibling.focus()),i.key==="Enter"&&n.handleClick(a),n.props.handleOnKeyDown(i)}),w(n,"renderTimes",function(){for(var i=[],a=n.props.format?n.props.format:"p",l=n.props.intervals,u=n.props.selected||n.props.openToDate||X(),c=Rk(u),d=n.props.injectTimes&&n.props.injectTimes.sort(function(m,v){return m-v}),f=60*Qk(u),p=f/l,y=0;y<p;y++){var _=Au(c,y*l);if(i.push(_),d){var x=Vk(c,_,y,l,d);i=i.concat(x)}}var g=i.reduce(function(m,v){return v.getTime()<=u.getTime()?v:m},i[0]);return i.map(function(m,v){return C.createElement("li",{key:v,onClick:n.handleClick.bind(n,m),className:n.liClasses(m),ref:function(E){m===g&&(n.centerLi=E)},onKeyDown:function(E){n.handleOnKeyDown(E,m)},tabIndex:m===g?0:-1,role:"option","aria-selected":n.isSelectedTime(m)?"true":void 0,"aria-disabled":n.isDisabledTime(m)?"true":void 0},oe(m,a,n.props.locale))})}),n}return Oe(t,e),Te(t,[{key:"componentDidMount",value:function(){this.scrollToTheSelectedTime(),this.props.monthRef&&this.header&&this.setState({height:this.props.monthRef.clientHeight-this.header.clientHeight})}},{key:"render",value:function(){var r=this,s=this.state.height;return C.createElement("div",{className:"react-datepicker__time-container ".concat(this.props.todayButton?"react-datepicker__time-container--with-today-button":"")},C.createElement("div",{className:"react-datepicker__header react-datepicker__header--time ".concat(this.props.showTimeSelectOnly?"react-datepicker__header--time--only":""),ref:function(i){r.header=i}},C.createElement("div",{className:"react-datepicker-time__header"},this.props.timeCaption)),C.createElement("div",{className:"react-datepicker__time"},C.createElement("div",{className:"react-datepicker__time-box"},C.createElement("ul",{className:"react-datepicker__time-list",ref:function(i){r.list=i},style:s?{height:s}:{},role:"listbox","aria-label":this.props.timeCaption},this.renderTimes()))))}}],[{key:"defaultProps",get:function(){return{intervals:30,onTimeChange:function(){},todayButton:null,timeCaption:"Time"}}}])}(C.Component);w(zg,"calcCenterPosition",function(e,t){return t.offsetTop-(e/2-t.clientHeight/2)});var wh=3,dS=function(e){function t(n){var r;return Ne(this,t),r=Pe(this,t,[n]),w(r,"YEAR_REFS",Fn(Array(r.props.yearItemNumber)).map(function(){return C.createRef()})),w(r,"isDisabled",function(s){return za(s,r.props)}),w(r,"isExcluded",function(s){return pd(s,r.props)}),w(r,"selectingDate",function(){var s;return(s=r.props.selectingDate)!==null&&s!==void 0?s:r.props.preSelection}),w(r,"updateFocusOnPaginate",function(s){var o=(function(){this.YEAR_REFS[s].current.focus()}).bind(r);window.requestAnimationFrame(o)}),w(r,"handleYearClick",function(s,o){r.props.onDayClick&&r.props.onDayClick(s,o)}),w(r,"handleYearNavigation",function(s,o){var i=r.props,a=i.date,l=i.yearItemNumber,u=kn(a,l),c=u.startPeriod;r.isDisabled(o)||r.isExcluded(o)||(r.props.setPreSelection(o),s-c<0?r.updateFocusOnPaginate(l-(c-s)):s-c>=l?r.updateFocusOnPaginate(Math.abs(l-(s-c))):r.YEAR_REFS[s-c].current.focus())}),w(r,"isSameDay",function(s,o){return K(s,o)}),w(r,"isCurrentYear",function(s){return s===W(X())}),w(r,"isRangeStart",function(s){return r.props.startDate&&r.props.endDate&&Mt(Ot(X(),s),r.props.startDate)}),w(r,"isRangeEnd",function(s){return r.props.startDate&&r.props.endDate&&Mt(Ot(X(),s),r.props.endDate)}),w(r,"isInRange",function(s){return ri(s,r.props.startDate,r.props.endDate)}),w(r,"isInSelectingRange",function(s){var o=r.props,i=o.selectsStart,a=o.selectsEnd,l=o.selectsRange,u=o.startDate,c=o.endDate;return!(i||a||l)||!r.selectingDate()?!1:i&&c?ri(s,r.selectingDate(),c):a&&u||l&&u&&!c?ri(s,u,r.selectingDate()):!1}),w(r,"isSelectingRangeStart",function(s){if(!r.isInSelectingRange(s))return!1;var o=r.props,i=o.startDate,a=o.selectsStart,l=Ot(X(),s);return a?Mt(l,r.selectingDate()):Mt(l,i)}),w(r,"isSelectingRangeEnd",function(s){if(!r.isInSelectingRange(s))return!1;var o=r.props,i=o.endDate,a=o.selectsEnd,l=o.selectsRange,u=Ot(X(),s);return a||l?Mt(u,r.selectingDate()):Mt(u,i)}),w(r,"isKeyboardSelected",function(s){var o=Is(Ot(r.props.date,s));return!r.props.disabledKeyboardNavigation&&!r.props.inline&&!K(o,Is(r.props.selected))&&K(o,Is(r.props.preSelection))}),w(r,"onYearClick",function(s,o){var i=r.props.date;r.handleYearClick(Is(Ot(i,o)),s)}),w(r,"onYearKeyDown",function(s,o){var i=s.key,a=r.props,l=a.date,u=a.yearItemNumber,c=a.handleOnKeyDown;if(i!=="Tab"&&s.preventDefault(),!r.props.disabledKeyboardNavigation)switch(i){case"Enter":r.onYearClick(s,o),r.props.setPreSelection(r.props.selected);break;case"ArrowRight":r.handleYearNavigation(o+1,Xt(r.props.preSelection,1));break;case"ArrowLeft":r.handleYearNavigation(o-1,ss(r.props.preSelection,1));break;case"ArrowUp":{var d=kn(l,u),f=d.startPeriod,p=wh,y=o-p;if(y<f){var _=u%p;o>=f&&o<f+_?p=_:p+=_,y=o-p}r.handleYearNavigation(y,ss(r.props.preSelection,p));break}case"ArrowDown":{var x=kn(l,u),g=x.endPeriod,m=wh,v=o+m;if(v>g){var k=u%m;o<=g&&o>g-k?m=k:m+=k,v=o+m}r.handleYearNavigation(v,Xt(r.props.preSelection,m));break}}c&&c(s)}),w(r,"getYearClassNames",function(s){var o=r.props,i=o.date,a=o.minDate,l=o.maxDate,u=o.selected,c=o.excludeDates,d=o.includeDates,f=o.filterDate,p=o.yearClassName;return be("react-datepicker__year-text","react-datepicker__year-".concat(s),p?p(Ot(i,s)):void 0,{"react-datepicker__year-text--selected":s===W(u),"react-datepicker__year-text--disabled":(a||l||c||d||f)&&Yg(s,r.props),"react-datepicker__year-text--keyboard-selected":r.isKeyboardSelected(s),"react-datepicker__year-text--range-start":r.isRangeStart(s),"react-datepicker__year-text--range-end":r.isRangeEnd(s),"react-datepicker__year-text--in-range":r.isInRange(s),"react-datepicker__year-text--in-selecting-range":r.isInSelectingRange(s),"react-datepicker__year-text--selecting-range-start":r.isSelectingRangeStart(s),"react-datepicker__year-text--selecting-range-end":r.isSelectingRangeEnd(s),"react-datepicker__year-text--today":r.isCurrentYear(s)})}),w(r,"getYearTabIndex",function(s){if(r.props.disabledKeyboardNavigation)return"-1";var o=W(r.props.preSelection);return s===o?"0":"-1"}),w(r,"getYearContainerClassNames",function(){var s=r.props,o=s.selectingDate,i=s.selectsStart,a=s.selectsEnd,l=s.selectsRange;return be("react-datepicker__year",{"react-datepicker__year--selecting-range":o&&(i||a||l)})}),w(r,"getYearContent",function(s){return r.props.renderYearContent?r.props.renderYearContent(s):s}),r}return Oe(t,e),Te(t,[{key:"render",value:function(){for(var r=this,s=[],o=this.props,i=o.date,a=o.yearItemNumber,l=o.onYearMouseEnter,u=o.onYearMouseLeave,c=kn(i,a),d=c.startPeriod,f=c.endPeriod,p=function(x){s.push(C.createElement("div",{ref:r.YEAR_REFS[x-d],onClick:function(m){r.onYearClick(m,x)},onKeyDown:function(m){Bg(m)&&(m.preventDefault(),m.key="Enter"),r.onYearKeyDown(m,x)},tabIndex:r.getYearTabIndex(x),className:r.getYearClassNames(x),onMouseEnter:r.props.usePointerEvent?void 0:function(g){return l(g,x)},onPointerEnter:r.props.usePointerEvent?function(g){return l(g,x)}:void 0,onMouseLeave:r.props.usePointerEvent?void 0:function(g){return u(g,x)},onPointerLeave:r.props.usePointerEvent?function(g){return u(g,x)}:void 0,key:x,"aria-current":r.isCurrentYear(x)?"date":void 0},r.getYearContent(x)))},y=d;y<=f;y++)p(y);return C.createElement("div",{className:this.getYearContainerClassNames()},C.createElement("div",{className:"react-datepicker__year-wrapper",onMouseLeave:this.props.usePointerEvent?void 0:this.props.clearSelectingDate,onPointerLeave:this.props.usePointerEvent?this.props.clearSelectingDate:void 0},s))}}])}(C.Component),fS=function(e){function t(n){var r;return Ne(this,t),r=Pe(this,t,[n]),w(r,"onTimeChange",function(s){r.setState({time:s});var o=r.props.date,i=o instanceof Date&&!isNaN(o),a=i?o:new Date;a.setHours(s.split(":")[0]),a.setMinutes(s.split(":")[1]),r.props.onChange(a)}),w(r,"renderTimeInput",function(){var s=r.state.time,o=r.props,i=o.date,a=o.timeString,l=o.customTimeInput;return l?C.cloneElement(l,{date:i,value:s,onChange:r.onTimeChange}):C.createElement("input",{type:"time",className:"react-datepicker-time__input",placeholder:"Time",name:"time-input",required:!0,value:s,onChange:function(c){r.onTimeChange(c.target.value||a)}})}),r.state={time:r.props.timeString},r}return Oe(t,e),Te(t,[{key:"render",value:function(){return C.createElement("div",{className:"react-datepicker__input-time-container"},C.createElement("div",{className:"react-datepicker-time__caption"},this.props.timeInputLabel),C.createElement("div",{className:"react-datepicker-time__input-container"},C.createElement("div",{className:"react-datepicker-time__input"},this.renderTimeInput())))}}],[{key:"getDerivedStateFromProps",value:function(r,s){return r.timeString!==s.time?{time:r.timeString}:null}}])}(C.Component);function hS(e){var t=e.showTimeSelectOnly,n=t===void 0?!1:t,r=e.showTime,s=r===void 0?!1:r,o=e.className,i=e.children,a=n?"Choose Time":"Choose Date".concat(s?" and Time":"");return C.createElement("div",{className:o,role:"dialog","aria-label":a,"aria-modal":"true"},i)}var pS=["react-datepicker__year-select","react-datepicker__month-select","react-datepicker__month-year-select"],mS=function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},n=(t.className||"").split(/\s+/);return pS.some(function(r){return n.indexOf(r)>=0})},gS=function(e){function t(n){var r;return Ne(this,t),r=Pe(this,t,[n]),w(r,"handleClickOutside",function(s){r.props.onClickOutside(s)}),w(r,"setClickOutsideRef",function(){return r.containerRef.current}),w(r,"handleDropdownFocus",function(s){mS(s.target)&&r.props.onDropdownFocus()}),w(r,"getDateInView",function(){var s=r.props,o=s.preSelection,i=s.selected,a=s.openToDate,l=Ug(r.props),u=Wg(r.props),c=X(),d=a||i||o;return d||(l&&An(c,l)?l:u&&Ln(c,u)?u:c)}),w(r,"increaseMonth",function(){r.setState(function(s){var o=s.date;return{date:Et(o,1)}},function(){return r.handleMonthChange(r.state.date)})}),w(r,"decreaseMonth",function(){r.setState(function(s){var o=s.date;return{date:rs(o,1)}},function(){return r.handleMonthChange(r.state.date)})}),w(r,"handleDayClick",function(s,o,i){r.props.onSelect(s,o,i),r.props.setPreSelection&&r.props.setPreSelection(s)}),w(r,"handleDayMouseEnter",function(s){r.setState({selectingDate:s}),r.props.onDayMouseEnter&&r.props.onDayMouseEnter(s)}),w(r,"handleMonthMouseLeave",function(){r.setState({selectingDate:null}),r.props.onMonthMouseLeave&&r.props.onMonthMouseLeave()}),w(r,"handleYearMouseEnter",function(s,o){r.setState({selectingDate:Ot(X(),o)}),r.props.onYearMouseEnter&&r.props.onYearMouseEnter(s,o)}),w(r,"handleYearMouseLeave",function(s,o){r.props.onYearMouseLeave&&r.props.onYearMouseLeave(s,o)}),w(r,"handleYearChange",function(s){r.props.onYearChange&&(r.props.onYearChange(s),r.setState({isRenderAriaLiveMessage:!0})),r.props.adjustDateOnChange&&(r.props.onSelect&&r.props.onSelect(s),r.props.setOpen&&r.props.setOpen(!0)),r.props.setPreSelection&&r.props.setPreSelection(s)}),w(r,"handleMonthChange",function(s){r.handleCustomMonthChange(s),r.props.adjustDateOnChange&&(r.props.onSelect&&r.props.onSelect(s),r.props.setOpen&&r.props.setOpen(!0)),r.props.setPreSelection&&r.props.setPreSelection(s)}),w(r,"handleCustomMonthChange",function(s){r.props.onMonthChange&&(r.props.onMonthChange(s),r.setState({isRenderAriaLiveMessage:!0}))}),w(r,"handleMonthYearChange",function(s){r.handleYearChange(s),r.handleMonthChange(s)}),w(r,"changeYear",function(s){r.setState(function(o){var i=o.date;return{date:Ot(i,s)}},function(){return r.handleYearChange(r.state.date)})}),w(r,"changeMonth",function(s){r.setState(function(o){var i=o.date;return{date:Ye(i,s)}},function(){return r.handleMonthChange(r.state.date)})}),w(r,"changeMonthYear",function(s){r.setState(function(o){var i=o.date;return{date:Ot(Ye(i,Ue(s)),W(s))}},function(){return r.handleMonthYearChange(r.state.date)})}),w(r,"header",function(){var s=arguments.length>0&&arguments[0]!==void 0?arguments[0]:r.state.date,o=On(s,r.props.locale,r.props.calendarStartDay),i=[];return r.props.showWeekNumbers&&i.push(C.createElement("div",{key:"W",className:"react-datepicker__day-name"},r.props.weekLabel||"#")),i.concat([0,1,2,3,4,5,6].map(function(a){var l=Bn(o,a),u=r.formatWeekday(l,r.props.locale),c=r.props.weekDayClassName?r.props.weekDayClassName(l):void 0;return C.createElement("div",{key:a,"aria-label":oe(l,"EEEE",r.props.locale),className:be("react-datepicker__day-name",c)},u)}))}),w(r,"formatWeekday",function(s,o){return r.props.formatWeekDay?Lk(s,r.props.formatWeekDay,o):r.props.useWeekdaysShort?$k(s,o):Ak(s,o)}),w(r,"decreaseYear",function(){r.setState(function(s){var o=s.date;return{date:ss(o,r.props.showYearPicker?r.props.yearItemNumber:1)}},function(){return r.handleYearChange(r.state.date)})}),w(r,"clearSelectingDate",function(){r.setState({selectingDate:null})}),w(r,"renderPreviousButton",function(){if(!r.props.renderCustomHeader){var s;switch(!0){case r.props.showMonthYearPicker:s=dh(r.state.date,r.props);break;case r.props.showYearPicker:s=Wk(r.state.date,r.props);break;case r.props.showQuarterYearPicker:s=Yk(r.state.date,r.props);break;default:s=uh(r.state.date,r.props);break}if(!(!r.props.forceShowMonthNavigation&&!r.props.showDisabledMonthNavigation&&s||r.props.showTimeSelectOnly)){var o=["react-datepicker__navigation-icon","react-datepicker__navigation-icon--previous"],i=["react-datepicker__navigation","react-datepicker__navigation--previous"],a=r.decreaseMonth;(r.props.showMonthYearPicker||r.props.showQuarterYearPicker||r.props.showYearPicker)&&(a=r.decreaseYear),s&&r.props.showDisabledMonthNavigation&&(i.push("react-datepicker__navigation--previous--disabled"),a=null);var l=r.props.showMonthYearPicker||r.props.showQuarterYearPicker||r.props.showYearPicker,u=r.props,c=u.previousMonthButtonLabel,d=u.previousYearButtonLabel,f=r.props,p=f.previousMonthAriaLabel,y=p===void 0?typeof c=="string"?c:"Previous Month":p,_=f.previousYearAriaLabel,x=_===void 0?typeof d=="string"?d:"Previous Year":_;return C.createElement("button",{type:"button",className:i.join(" "),onClick:a,onKeyDown:r.props.handleOnKeyDown,"aria-label":l?x:y},C.createElement("span",{className:o.join(" ")},l?r.props.previousYearButtonLabel:r.props.previousMonthButtonLabel))}}}),w(r,"increaseYear",function(){r.setState(function(s){var o=s.date;return{date:Xt(o,r.props.showYearPicker?r.props.yearItemNumber:1)}},function(){return r.handleYearChange(r.state.date)})}),w(r,"renderNextButton",function(){if(!r.props.renderCustomHeader){var s;switch(!0){case r.props.showMonthYearPicker:s=fh(r.state.date,r.props);break;case r.props.showYearPicker:s=Bk(r.state.date,r.props);break;case r.props.showQuarterYearPicker:s=Uk(r.state.date,r.props);break;default:s=ch(r.state.date,r.props);break}if(!(!r.props.forceShowMonthNavigation&&!r.props.showDisabledMonthNavigation&&s||r.props.showTimeSelectOnly)){var o=["react-datepicker__navigation","react-datepicker__navigation--next"],i=["react-datepicker__navigation-icon","react-datepicker__navigation-icon--next"];r.props.showTimeSelect&&o.push("react-datepicker__navigation--next--with-time"),r.props.todayButton&&o.push("react-datepicker__navigation--next--with-today-button");var a=r.increaseMonth;(r.props.showMonthYearPicker||r.props.showQuarterYearPicker||r.props.showYearPicker)&&(a=r.increaseYear),s&&r.props.showDisabledMonthNavigation&&(o.push("react-datepicker__navigation--next--disabled"),a=null);var l=r.props.showMonthYearPicker||r.props.showQuarterYearPicker||r.props.showYearPicker,u=r.props,c=u.nextMonthButtonLabel,d=u.nextYearButtonLabel,f=r.props,p=f.nextMonthAriaLabel,y=p===void 0?typeof c=="string"?c:"Next Month":p,_=f.nextYearAriaLabel,x=_===void 0?typeof d=="string"?d:"Next Year":_;return C.createElement("button",{type:"button",className:o.join(" "),onClick:a,onKeyDown:r.props.handleOnKeyDown,"aria-label":l?x:y},C.createElement("span",{className:i.join(" ")},l?r.props.nextYearButtonLabel:r.props.nextMonthButtonLabel))}}}),w(r,"renderCurrentMonth",function(){var s=arguments.length>0&&arguments[0]!==void 0?arguments[0]:r.state.date,o=["react-datepicker__current-month"];return r.props.showYearDropdown&&o.push("react-datepicker__current-month--hasYearDropdown"),r.props.showMonthDropdown&&o.push("react-datepicker__current-month--hasMonthDropdown"),r.props.showMonthYearDropdown&&o.push("react-datepicker__current-month--hasMonthYearDropdown"),C.createElement("div",{className:o.join(" ")},oe(s,r.props.dateFormat,r.props.locale))}),w(r,"renderYearDropdown",function(){var s=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1;if(!(!r.props.showYearDropdown||s))return C.createElement(Xk,{adjustDateOnChange:r.props.adjustDateOnChange,date:r.state.date,onSelect:r.props.onSelect,setOpen:r.props.setOpen,dropdownMode:r.props.dropdownMode,onChange:r.changeYear,minDate:r.props.minDate,maxDate:r.props.maxDate,year:W(r.state.date),scrollableYearDropdown:r.props.scrollableYearDropdown,yearDropdownItemNumber:r.props.yearDropdownItemNumber})}),w(r,"renderMonthDropdown",function(){var s=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1;if(!(!r.props.showMonthDropdown||s))return C.createElement(tS,{dropdownMode:r.props.dropdownMode,locale:r.props.locale,onChange:r.changeMonth,month:Ue(r.state.date),useShortMonthInDropdown:r.props.useShortMonthInDropdown})}),w(r,"renderMonthYearDropdown",function(){var s=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1;if(!(!r.props.showMonthYearDropdown||s))return C.createElement(oS,{dropdownMode:r.props.dropdownMode,locale:r.props.locale,dateFormat:r.props.dateFormat,onChange:r.changeMonthYear,minDate:r.props.minDate,maxDate:r.props.maxDate,date:r.state.date,scrollableMonthYearDropdown:r.props.scrollableMonthYearDropdown})}),w(r,"handleTodayButtonClick",function(s){r.props.onSelect(oh(),s),r.props.setPreSelection&&r.props.setPreSelection(oh())}),w(r,"renderTodayButton",function(){if(!(!r.props.todayButton||r.props.showTimeSelectOnly))return C.createElement("div",{className:"react-datepicker__today-button",onClick:function(o){return r.handleTodayButtonClick(o)}},r.props.todayButton)}),w(r,"renderDefaultHeader",function(s){var o=s.monthDate,i=s.i;return C.createElement("div",{className:"react-datepicker__header ".concat(r.props.showTimeSelect?"react-datepicker__header--has-time-select":"")},r.renderCurrentMonth(o),C.createElement("div",{className:"react-datepicker__header__dropdown react-datepicker__header__dropdown--".concat(r.props.dropdownMode),onFocus:r.handleDropdownFocus},r.renderMonthDropdown(i!==0),r.renderMonthYearDropdown(i!==0),r.renderYearDropdown(i!==0)),C.createElement("div",{className:"react-datepicker__day-names"},r.header(o)))}),w(r,"renderCustomHeader",function(){var s=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},o=s.monthDate,i=s.i;if(r.props.showTimeSelect&&!r.state.monthContainer||r.props.showTimeSelectOnly)return null;var a=uh(r.state.date,r.props),l=ch(r.state.date,r.props),u=dh(r.state.date,r.props),c=fh(r.state.date,r.props),d=!r.props.showMonthYearPicker&&!r.props.showQuarterYearPicker&&!r.props.showYearPicker;return C.createElement("div",{className:"react-datepicker__header react-datepicker__header--custom",onFocus:r.props.onDropdownFocus},r.props.renderCustomHeader(Kt(Kt({},r.state),{},{customHeaderCount:i,monthDate:o,changeMonth:r.changeMonth,changeYear:r.changeYear,decreaseMonth:r.decreaseMonth,increaseMonth:r.increaseMonth,decreaseYear:r.decreaseYear,increaseYear:r.increaseYear,prevMonthButtonDisabled:a,nextMonthButtonDisabled:l,prevYearButtonDisabled:u,nextYearButtonDisabled:c})),d&&C.createElement("div",{className:"react-datepicker__day-names"},r.header(o)))}),w(r,"renderYearHeader",function(s){var o=s.monthDate,i=r.props,a=i.showYearPicker,l=i.yearItemNumber,u=kn(o,l),c=u.startPeriod,d=u.endPeriod;return C.createElement("div",{className:"react-datepicker__header react-datepicker-year-header"},a?"".concat(c," - ").concat(d):W(o))}),w(r,"renderHeader",function(s){switch(!0){case r.props.renderCustomHeader!==void 0:return r.renderCustomHeader(s);case(r.props.showMonthYearPicker||r.props.showQuarterYearPicker||r.props.showYearPicker):return r.renderYearHeader(s);default:return r.renderDefaultHeader(s)}}),w(r,"renderMonths",function(){var s;if(!(r.props.showTimeSelectOnly||r.props.showYearPicker)){for(var o=[],i=r.props.showPreviousMonths?r.props.monthsShown-1:0,a=r.props.showMonthYearPicker||r.props.showQuarterYearPicker?Xt(r.state.date,i):rs(r.state.date,i),l=(s=r.props.monthSelectedIn)!==null&&s!==void 0?s:i,u=0;u<r.props.monthsShown;++u){var c=u-l+i,d=r.props.showMonthYearPicker||r.props.showQuarterYearPicker?Xt(a,c):Et(a,c),f="month-".concat(u),p=u<r.props.monthsShown-1,y=u>0;o.push(C.createElement("div",{key:f,ref:function(x){r.monthContainer=x},className:"react-datepicker__month-container"},r.renderHeader({monthDate:d,i:u}),C.createElement(cS,{chooseDayAriaLabelPrefix:r.props.chooseDayAriaLabelPrefix,disabledDayAriaLabelPrefix:r.props.disabledDayAriaLabelPrefix,weekAriaLabelPrefix:r.props.weekAriaLabelPrefix,ariaLabelPrefix:r.props.monthAriaLabelPrefix,onChange:r.changeMonthYear,day:d,dayClassName:r.props.dayClassName,calendarStartDay:r.props.calendarStartDay,monthClassName:r.props.monthClassName,onDayClick:r.handleDayClick,handleOnKeyDown:r.props.handleOnDayKeyDown,handleOnMonthKeyDown:r.props.handleOnKeyDown,usePointerEvent:r.props.usePointerEvent,onDayMouseEnter:r.handleDayMouseEnter,onMouseLeave:r.handleMonthMouseLeave,onWeekSelect:r.props.onWeekSelect,orderInDisplay:u,formatWeekNumber:r.props.formatWeekNumber,locale:r.props.locale,minDate:r.props.minDate,maxDate:r.props.maxDate,excludeDates:r.props.excludeDates,excludeDateIntervals:r.props.excludeDateIntervals,highlightDates:r.props.highlightDates,holidays:r.props.holidays,selectingDate:r.state.selectingDate,includeDates:r.props.includeDates,includeDateIntervals:r.props.includeDateIntervals,inline:r.props.inline,shouldFocusDayInline:r.props.shouldFocusDayInline,fixedHeight:r.props.fixedHeight,filterDate:r.props.filterDate,preSelection:r.props.preSelection,setPreSelection:r.props.setPreSelection,selected:r.props.selected,selectsStart:r.props.selectsStart,selectsEnd:r.props.selectsEnd,selectsRange:r.props.selectsRange,selectsDisabledDaysInRange:r.props.selectsDisabledDaysInRange,selectsMultiple:r.props.selectsMultiple,selectedDates:r.props.selectedDates,showWeekNumbers:r.props.showWeekNumbers,startDate:r.props.startDate,endDate:r.props.endDate,peekNextMonth:r.props.peekNextMonth,setOpen:r.props.setOpen,shouldCloseOnSelect:r.props.shouldCloseOnSelect,renderDayContents:r.props.renderDayContents,renderMonthContent:r.props.renderMonthContent,renderQuarterContent:r.props.renderQuarterContent,renderYearContent:r.props.renderYearContent,disabledKeyboardNavigation:r.props.disabledKeyboardNavigation,showMonthYearPicker:r.props.showMonthYearPicker,showFullMonthYearPicker:r.props.showFullMonthYearPicker,showTwoColumnMonthYearPicker:r.props.showTwoColumnMonthYearPicker,showFourColumnMonthYearPicker:r.props.showFourColumnMonthYearPicker,showYearPicker:r.props.showYearPicker,showQuarterYearPicker:r.props.showQuarterYearPicker,showWeekPicker:r.props.showWeekPicker,isInputFocused:r.props.isInputFocused,containerRef:r.containerRef,monthShowsDuplicateDaysEnd:p,monthShowsDuplicateDaysStart:y})))}return o}}),w(r,"renderYears",function(){if(!r.props.showTimeSelectOnly&&r.props.showYearPicker)return C.createElement("div",{className:"react-datepicker__year--container"},r.renderHeader({monthDate:r.state.date}),C.createElement(dS,_o({onDayClick:r.handleDayClick,selectingDate:r.state.selectingDate,clearSelectingDate:r.clearSelectingDate,date:r.state.date},r.props,{onYearMouseEnter:r.handleYearMouseEnter,onYearMouseLeave:r.handleYearMouseLeave})))}),w(r,"renderTimeSection",function(){if(r.props.showTimeSelect&&(r.state.monthContainer||r.props.showTimeSelectOnly))return C.createElement(zg,{selected:r.props.selected,openToDate:r.props.openToDate,onChange:r.props.onTimeChange,timeClassName:r.props.timeClassName,format:r.props.timeFormat,includeTimes:r.props.includeTimes,intervals:r.props.timeIntervals,minTime:r.props.minTime,maxTime:r.props.maxTime,excludeTimes:r.props.excludeTimes,filterTime:r.props.filterTime,timeCaption:r.props.timeCaption,todayButton:r.props.todayButton,showMonthDropdown:r.props.showMonthDropdown,showMonthYearDropdown:r.props.showMonthYearDropdown,showYearDropdown:r.props.showYearDropdown,withPortal:r.props.withPortal,monthRef:r.state.monthContainer,injectTimes:r.props.injectTimes,locale:r.props.locale,handleOnKeyDown:r.props.handleOnKeyDown,showTimeSelectOnly:r.props.showTimeSelectOnly})}),w(r,"renderInputTimeSection",function(){var s=new Date(r.props.selected),o=pn(s)&&!!r.props.selected,i=o?"".concat(ph(s.getHours()),":").concat(ph(s.getMinutes())):"";if(r.props.showTimeInput)return C.createElement(fS,{date:s,timeString:i,timeInputLabel:r.props.timeInputLabel,onChange:r.props.onTimeChange,customTimeInput:r.props.customTimeInput})}),w(r,"renderAriaLiveRegion",function(){var s=kn(r.state.date,r.props.yearItemNumber),o=s.startPeriod,i=s.endPeriod,a;return r.props.showYearPicker?a="".concat(o," - ").concat(i):r.props.showMonthYearPicker||r.props.showQuarterYearPicker?a=W(r.state.date):a="".concat(hd(Ue(r.state.date),r.props.locale)," ").concat(W(r.state.date)),C.createElement("span",{role:"alert","aria-live":"polite",className:"react-datepicker__aria-live"},r.state.isRenderAriaLiveMessage&&a)}),w(r,"renderChildren",function(){if(r.props.children)return C.createElement("div",{className:"react-datepicker__children-container"},r.props.children)}),r.containerRef=C.createRef(),r.state={date:r.getDateInView(),selectingDate:null,monthContainer:null,isRenderAriaLiveMessage:!1},r}return Oe(t,e),Te(t,[{key:"componentDidMount",value:function(){var r=this;this.props.showTimeSelect&&(this.assignMonthContainer=function(){r.setState({monthContainer:r.monthContainer})}())}},{key:"componentDidUpdate",value:function(r){var s=this;if(this.props.preSelection&&(!K(this.props.preSelection,r.preSelection)||this.props.monthSelectedIn!==r.monthSelectedIn)){var o=!rt(this.state.date,this.props.preSelection);this.setState({date:this.props.preSelection},function(){return o&&s.handleCustomMonthChange(s.state.date)})}else this.props.openToDate&&!K(this.props.openToDate,r.openToDate)&&this.setState({date:this.props.openToDate})}},{key:"render",value:function(){var r=this.props.container||hS;return C.createElement("div",{style:{display:"contents"},ref:this.containerRef},C.createElement(r,{className:be("react-datepicker",this.props.className,{"react-datepicker--time-only":this.props.showTimeSelectOnly}),showTime:this.props.showTimeSelect||this.props.showTimeInput,showTimeSelectOnly:this.props.showTimeSelectOnly},this.renderAriaLiveRegion(),this.renderPreviousButton(),this.renderNextButton(),this.renderMonths(),this.renderYears(),this.renderTodayButton(),this.renderTimeSection(),this.renderInputTimeSection(),this.renderChildren()))}}],[{key:"defaultProps",get:function(){return{onDropdownFocus:function(){},monthsShown:1,forceShowMonthNavigation:!1,timeCaption:"Time",previousYearButtonLabel:"Previous Year",nextYearButtonLabel:"Next Year",previousMonthButtonLabel:"Previous Month",nextMonthButtonLabel:"Next Month",customTimeInput:null,yearItemNumber:Mo}}}])}(C.Component),yS=function(t){var n=t.icon,r=t.className,s=r===void 0?"":r,o=t.onClick,i="react-datepicker__calendar-icon";return C.isValidElement(n)?C.cloneElement(n,{className:"".concat(n.props.className||""," ").concat(i," ").concat(s),onClick:function(l){typeof n.props.onClick=="function"&&n.props.onClick(l),typeof o=="function"&&o(l)}}):typeof n=="string"?C.createElement("i",{className:"".concat(i," ").concat(n," ").concat(s),"aria-hidden":"true",onClick:o}):C.createElement("svg",{className:"".concat(i," ").concat(s),xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 448 512",onClick:o},C.createElement("path",{d:"M96 32V64H48C21.5 64 0 85.5 0 112v48H448V112c0-26.5-21.5-48-48-48H352V32c0-17.7-14.3-32-32-32s-32 14.3-32 32V64H160V32c0-17.7-14.3-32-32-32S96 14.3 96 32zM448 192H0V464c0 26.5 21.5 48 48 48H400c26.5 0 48-21.5 48-48V192z"}))},vS=yS,Hg=function(e){function t(n){var r;return Ne(this,t),r=Pe(this,t,[n]),r.el=document.createElement("div"),r}return Oe(t,e),Te(t,[{key:"componentDidMount",value:function(){this.portalRoot=(this.props.portalHost||document).getElementById(this.props.portalId),this.portalRoot||(this.portalRoot=document.createElement("div"),this.portalRoot.setAttribute("id",this.props.portalId),(this.props.portalHost||document.body).appendChild(this.portalRoot)),this.portalRoot.appendChild(this.el)}},{key:"componentWillUnmount",value:function(){this.portalRoot.removeChild(this.el)}},{key:"render",value:function(){return J0.createPortal(this.props.children,this.el)}}])}(C.Component),wS="[tabindex], a, button, input, select, textarea",_S=function(t){return!t.disabled&&t.tabIndex!==-1},Vg=function(e){function t(n){var r;return Ne(this,t),r=Pe(this,t,[n]),w(r,"getTabChildren",function(){return Array.prototype.slice.call(r.tabLoopRef.current.querySelectorAll(wS),1,-1).filter(_S)}),w(r,"handleFocusStart",function(){var s=r.getTabChildren();s&&s.length>1&&s[s.length-1].focus()}),w(r,"handleFocusEnd",function(){var s=r.getTabChildren();s&&s.length>1&&s[0].focus()}),r.tabLoopRef=C.createRef(),r}return Oe(t,e),Te(t,[{key:"render",value:function(){return this.props.enableTabLoop?C.createElement("div",{className:"react-datepicker__tab-loop",ref:this.tabLoopRef},C.createElement("div",{className:"react-datepicker__tab-loop__start",tabIndex:"0",onFocus:this.handleFocusStart}),this.props.children,C.createElement("div",{className:"react-datepicker__tab-loop__end",tabIndex:"0",onFocus:this.handleFocusEnd})):this.props.children}}],[{key:"defaultProps",get:function(){return{enableTabLoop:!0}}}])}(C.Component);function xS(e){var t=function(r){var s=Kt(Kt({},r),{},{popperModifiers:r.popperModifiers||[],popperProps:r.popperProps||{},hidePopper:typeof r.hidePopper=="boolean"?r.hidePopper:!0}),o=C.useRef(),i=_k(Kt({open:!s.hidePopper,whileElementsMounted:X1,placement:s.popperPlacement,middleware:[ok({padding:15}),sk(10),ik({element:o})].concat(Fn(s.popperModifiers))},s.popperProps));return C.createElement(e,_o({},s,{popperProps:Kt(Kt({},i),{},{arrowRef:o})}))};return t}var kS=function(e){function t(){return Ne(this,t),Pe(this,t,arguments)}return Oe(t,e),Te(t,[{key:"render",value:function(){var r=this.props,s=r.className,o=r.wrapperClassName,i=r.hidePopper,a=r.popperComponent,l=r.targetComponent,u=r.enableTabLoop,c=r.popperOnKeyDown,d=r.portalId,f=r.portalHost,p=r.popperProps,y=r.showArrow,_;if(!i){var x=be("react-datepicker-popper",s);_=C.createElement(Vg,{enableTabLoop:u},C.createElement("div",{ref:p.refs.setFloating,style:p.floatingStyles,className:x,"data-placement":p.placement,onKeyDown:c},a,y&&C.createElement(hk,{ref:p.arrowRef,context:p.context,fill:"currentColor",strokeWidth:1,height:8,width:16,style:{transform:"translateY(-1px)"},className:"react-datepicker__triangle"})))}this.props.popperContainer&&(_=C.createElement(this.props.popperContainer,{},_)),d&&!i&&(_=C.createElement(Hg,{portalId:d,portalHost:f},_));var g=be("react-datepicker-wrapper",o);return C.createElement(C.Fragment,null,C.createElement("div",{ref:p.refs.setReference,className:g},l),_)}}],[{key:"defaultProps",get:function(){return{hidePopper:!0}}}])}(C.Component),SS=xS(kS),_h="react-datepicker-ignore-onclickoutside",bS=Fa(gS);function DS(e,t){return e&&t?Ue(e)!==Ue(t)||W(e)!==W(t):e!==t}var Ll="Date input not valid.",CS=function(e){function t(n){var r;return Ne(this,t),r=Pe(this,t,[n]),w(r,"getPreSelection",function(){return r.props.openToDate?r.props.openToDate:r.props.selectsEnd&&r.props.startDate?r.props.startDate:r.props.selectsStart&&r.props.endDate?r.props.endDate:X()}),w(r,"modifyHolidays",function(){var s;return(s=r.props.holidays)===null||s===void 0?void 0:s.reduce(function(o,i){var a=new Date(i.date);return mo(a)?[].concat(Fn(o),[Kt(Kt({},i),{},{date:a})]):o},[])}),w(r,"calcInitialState",function(){var s,o=r.getPreSelection(),i=Ug(r.props),a=Wg(r.props),l=i&&An(o,Ft(i))?i:a&&Ln(o,Yu(a))?a:o;return{open:r.props.startOpen||!1,preventFocus:!1,preSelection:(s=r.props.selectsRange?r.props.startDate:r.props.selected)!==null&&s!==void 0?s:l,highlightDates:hh(r.props.highlightDates),focused:!1,shouldFocusDayInline:!1,isRenderAriaLiveMessage:!1}}),w(r,"clearPreventFocusTimeout",function(){r.preventFocusTimeout&&clearTimeout(r.preventFocusTimeout)}),w(r,"setFocus",function(){r.input&&r.input.focus&&r.input.focus({preventScroll:!0})}),w(r,"setBlur",function(){r.input&&r.input.blur&&r.input.blur(),r.cancelFocusInput()}),w(r,"setOpen",function(s){var o=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;r.setState({open:s,preSelection:s&&r.state.open?r.state.preSelection:r.calcInitialState().preSelection,lastPreSelectChange:Al},function(){s||r.setState(function(i){return{focused:o?i.focused:!1}},function(){!o&&r.setBlur(),r.setState({inputValue:null})})})}),w(r,"inputOk",function(){return hr(r.state.preSelection)}),w(r,"isCalendarOpen",function(){return r.props.open===void 0?r.state.open&&!r.props.disabled&&!r.props.readOnly:r.props.open}),w(r,"handleFocus",function(s){r.state.preventFocus||(r.props.onFocus(s),!r.props.preventOpenOnFocus&&!r.props.readOnly&&r.setOpen(!0)),r.setState({focused:!0})}),w(r,"sendFocusBackToInput",function(){r.preventFocusTimeout&&r.clearPreventFocusTimeout(),r.setState({preventFocus:!0},function(){r.preventFocusTimeout=setTimeout(function(){r.setFocus(),r.setState({preventFocus:!1})})})}),w(r,"cancelFocusInput",function(){clearTimeout(r.inputFocusTimeout),r.inputFocusTimeout=null}),w(r,"deferFocusInput",function(){r.cancelFocusInput(),r.inputFocusTimeout=setTimeout(function(){return r.setFocus()},1)}),w(r,"handleDropdownFocus",function(){r.cancelFocusInput()}),w(r,"handleBlur",function(s){(!r.state.open||r.props.withPortal||r.props.showTimeInput)&&r.props.onBlur(s),r.setState({focused:!1})}),w(r,"handleCalendarClickOutside",function(s){r.props.inline||r.setOpen(!1),r.props.onClickOutside(s),r.props.withPortal&&s.preventDefault()}),w(r,"handleChange",function(){for(var s=arguments.length,o=new Array(s),i=0;i<s;i++)o[i]=arguments[i];var a=o[0];if(!(r.props.onChangeRaw&&(r.props.onChangeRaw.apply(r,o),typeof a.isDefaultPrevented!="function"||a.isDefaultPrevented()))){r.setState({inputValue:a.target.value,lastPreSelectChange:ES});var l=Nk(a.target.value,r.props.dateFormat,r.props.locale,r.props.strictParsing,r.props.minDate);r.props.showTimeSelectOnly&&r.props.selected&&l&&!K(l,r.props.selected)&&(l=u1(r.props.selected,{hours:Ut(l),minutes:Yt(l),seconds:Zt(l)})),(l||!a.target.value)&&r.setSelected(l,a,!0)}}),w(r,"handleSelect",function(s,o,i){if(r.props.shouldCloseOnSelect&&!r.props.showTimeSelect&&r.sendFocusBackToInput(),r.props.onChangeRaw&&r.props.onChangeRaw(o),r.setSelected(s,o,!1,i),r.props.showDateSelect&&r.setState({isRenderAriaLiveMessage:!0}),!r.props.shouldCloseOnSelect||r.props.showTimeSelect)r.setPreSelection(s);else if(!r.props.inline){r.props.selectsRange||r.setOpen(!1);var a=r.props,l=a.startDate,u=a.endDate;l&&!u&&(r.props.swapRange||!yh(s,l))&&r.setOpen(!1)}}),w(r,"setSelected",function(s,o,i,a){var l=s;if(r.props.showYearPicker){if(l!==null&&Yg(W(l),r.props))return}else if(r.props.showMonthYearPicker){if(l!==null&&Pi(l,r.props))return}else if(l!==null&&za(l,r.props))return;var u=r.props,c=u.onChange,d=u.selectsRange,f=u.startDate,p=u.endDate,y=u.selectsMultiple,_=u.selectedDates,x=u.minTime,g=u.swapRange;if(!tr(r.props.selected,l)||r.props.allowSameDay||d||y)if(l!==null&&(r.props.selected&&(!i||!r.props.showTimeSelect&&!r.props.showTimeSelectOnly&&!r.props.showTimeInput)&&(l=jl(l,{hour:Ut(r.props.selected),minute:Yt(r.props.selected),second:Zt(r.props.selected)})),!i&&(r.props.showTimeSelect||r.props.showTimeSelectOnly)&&x&&(l=jl(l,{hour:x.getHours(),minute:x.getMinutes(),second:x.getSeconds()})),r.props.inline||r.setState({preSelection:l}),r.props.focusSelectedMonth||r.setState({monthSelectedIn:a})),d){var m=!f&&!p,v=f&&!p,k=f&&p;m?c([l,null],o):v&&(l===null?c([null,null],o):yh(l,f)?c(g?[l,f]:[l,null],o):c([f,l],o)),k&&c([l,null],o)}else if(y)if(!(_!=null&&_.length))c([l],o);else{var E=_.some(function(D){return K(D,l)});if(E){var P=_.filter(function(D){return!K(D,l)});c(P,o)}else c([].concat(Fn(_),[l]),o)}else c(l,o);i||(r.props.onSelect(l,o),r.setState({inputValue:null}))}),w(r,"setPreSelection",function(s){var o=typeof r.props.minDate<"u",i=typeof r.props.maxDate<"u",a=!0;if(s){var l=Ft(s);if(o&&i)a=Ls(s,r.props.minDate,r.props.maxDate);else if(o){var u=Ft(r.props.minDate);a=Ln(s,u)||tr(l,u)}else if(i){var c=Yu(r.props.maxDate);a=An(s,c)||tr(l,c)}}a&&r.setState({preSelection:s})}),w(r,"toggleCalendar",function(){r.setOpen(!r.state.open)}),w(r,"handleTimeChange",function(s){var o=r.props.selected?r.props.selected:r.getPreSelection(),i=r.props.selected?s:jl(o,{hour:Ut(s),minute:Yt(s)});r.setState({preSelection:i}),r.props.onChange(i),r.props.shouldCloseOnSelect&&(r.sendFocusBackToInput(),r.setOpen(!1)),r.props.showTimeInput&&r.setOpen(!0),(r.props.showTimeSelectOnly||r.props.showTimeSelect)&&r.setState({isRenderAriaLiveMessage:!0}),r.setState({inputValue:null})}),w(r,"onInputClick",function(){!r.props.disabled&&!r.props.readOnly&&r.setOpen(!0),r.props.onInputClick()}),w(r,"onInputKeyDown",function(s){r.props.onKeyDown(s);var o=s.key;if(!r.state.open&&!r.props.inline&&!r.props.preventOpenOnFocus){(o==="ArrowDown"||o==="ArrowUp"||o==="Enter")&&r.onInputClick();return}if(r.state.open){if(o==="ArrowDown"||o==="ArrowUp"){s.preventDefault();var i=r.props.showWeekPicker&&r.props.showWeekNumbers?'.react-datepicker__week-number[tabindex="0"]':r.props.showFullMonthYearPicker||r.props.showMonthYearPicker?'.react-datepicker__month-text[tabindex="0"]':'.react-datepicker__day[tabindex="0"]',a=r.calendar.componentNode&&r.calendar.componentNode.querySelector(i);a&&a.focus({preventScroll:!0});return}var l=X(r.state.preSelection);o==="Enter"?(s.preventDefault(),r.inputOk()&&r.state.lastPreSelectChange===Al?(r.handleSelect(l,s),!r.props.shouldCloseOnSelect&&r.setPreSelection(l)):r.setOpen(!1)):o==="Escape"?(s.preventDefault(),r.sendFocusBackToInput(),r.setOpen(!1)):o==="Tab"&&r.setOpen(!1),r.inputOk()||r.props.onInputError({code:1,msg:Ll})}}),w(r,"onPortalKeyDown",function(s){var o=s.key;o==="Escape"&&(s.preventDefault(),r.setState({preventFocus:!0},function(){r.setOpen(!1),setTimeout(function(){r.setFocus(),r.setState({preventFocus:!1})})}))}),w(r,"onDayKeyDown",function(s){r.props.onKeyDown(s);var o=s.key,i=s.shiftKey,a=X(r.state.preSelection);if(o==="Enter")s.preventDefault(),r.handleSelect(a,s),!r.props.shouldCloseOnSelect&&r.setPreSelection(a);else if(o==="Escape")s.preventDefault(),r.setOpen(!1),r.inputOk()||r.props.onInputError({code:1,msg:Ll});else if(!r.props.disabledKeyboardNavigation){var l;switch(o){case"ArrowLeft":r.props.showWeekPicker?l=Wf(a,1):l=Y_(a,1);break;case"ArrowRight":r.props.showWeekPicker?l=ra(a,1):l=Bn(a,1);break;case"ArrowUp":l=Wf(a,1);break;case"ArrowDown":l=ra(a,1);break;case"PageUp":l=i?ss(a,1):rs(a,1);break;case"PageDown":l=i?Xt(a,1):Et(a,1);break;case"Home":l=On(a,r.props.locale,r.props.calendarStartDay);break;case"End":l=Ik(a);break;default:l=null;break}if(!l){r.props.onInputError&&r.props.onInputError({code:1,msg:Ll});return}if(s.preventDefault(),r.setState({lastPreSelectChange:Al}),r.props.adjustDateOnChange&&r.setSelected(l),r.setPreSelection(l),r.props.inline){var u=Ue(a),c=Ue(l),d=W(a),f=W(l);u!==c||d!==f?r.setState({shouldFocusDayInline:!0}):r.setState({shouldFocusDayInline:!1})}}}),w(r,"onPopperKeyDown",function(s){var o=s.key;o==="Escape"&&(s.preventDefault(),r.sendFocusBackToInput())}),w(r,"onClearClick",function(s){s&&s.preventDefault&&s.preventDefault(),r.sendFocusBackToInput(),r.props.selectsRange?r.props.onChange([null,null],s):r.props.onChange(null,s),r.setState({inputValue:null})}),w(r,"clear",function(){r.onClearClick()}),w(r,"onScroll",function(s){typeof r.props.closeOnScroll=="boolean"&&r.props.closeOnScroll?(s.target===document||s.target===document.documentElement||s.target===document.body)&&r.setOpen(!1):typeof r.props.closeOnScroll=="function"&&r.props.closeOnScroll(s)&&r.setOpen(!1)}),w(r,"renderCalendar",function(){return!r.props.inline&&!r.isCalendarOpen()?null:C.createElement(bS,{ref:function(o){r.calendar=o},locale:r.props.locale,calendarStartDay:r.props.calendarStartDay,chooseDayAriaLabelPrefix:r.props.chooseDayAriaLabelPrefix,disabledDayAriaLabelPrefix:r.props.disabledDayAriaLabelPrefix,weekAriaLabelPrefix:r.props.weekAriaLabelPrefix,monthAriaLabelPrefix:r.props.monthAriaLabelPrefix,adjustDateOnChange:r.props.adjustDateOnChange,setOpen:r.setOpen,shouldCloseOnSelect:r.props.shouldCloseOnSelect,dateFormat:r.props.dateFormatCalendar,useWeekdaysShort:r.props.useWeekdaysShort,formatWeekDay:r.props.formatWeekDay,dropdownMode:r.props.dropdownMode,selected:r.props.selected,preSelection:r.state.preSelection,onSelect:r.handleSelect,onWeekSelect:r.props.onWeekSelect,openToDate:r.props.openToDate,minDate:r.props.minDate,maxDate:r.props.maxDate,selectsStart:r.props.selectsStart,selectsEnd:r.props.selectsEnd,selectsRange:r.props.selectsRange,selectsMultiple:r.props.selectsMultiple,selectedDates:r.props.selectedDates,startDate:r.props.startDate,endDate:r.props.endDate,excludeDates:r.props.excludeDates,excludeDateIntervals:r.props.excludeDateIntervals,filterDate:r.props.filterDate,onClickOutside:r.handleCalendarClickOutside,formatWeekNumber:r.props.formatWeekNumber,highlightDates:r.state.highlightDates,holidays:Hk(r.modifyHolidays()),includeDates:r.props.includeDates,includeDateIntervals:r.props.includeDateIntervals,includeTimes:r.props.includeTimes,injectTimes:r.props.injectTimes,inline:r.props.inline,shouldFocusDayInline:r.state.shouldFocusDayInline,peekNextMonth:r.props.peekNextMonth,showMonthDropdown:r.props.showMonthDropdown,showPreviousMonths:r.props.showPreviousMonths,useShortMonthInDropdown:r.props.useShortMonthInDropdown,showMonthYearDropdown:r.props.showMonthYearDropdown,showWeekNumbers:r.props.showWeekNumbers,showYearDropdown:r.props.showYearDropdown,withPortal:r.props.withPortal,forceShowMonthNavigation:r.props.forceShowMonthNavigation,showDisabledMonthNavigation:r.props.showDisabledMonthNavigation,scrollableYearDropdown:r.props.scrollableYearDropdown,scrollableMonthYearDropdown:r.props.scrollableMonthYearDropdown,todayButton:r.props.todayButton,weekLabel:r.props.weekLabel,outsideClickIgnoreClass:_h,fixedHeight:r.props.fixedHeight,monthsShown:r.props.monthsShown,monthSelectedIn:r.state.monthSelectedIn,onDropdownFocus:r.handleDropdownFocus,onMonthChange:r.props.onMonthChange,onYearChange:r.props.onYearChange,dayClassName:r.props.dayClassName,weekDayClassName:r.props.weekDayClassName,monthClassName:r.props.monthClassName,timeClassName:r.props.timeClassName,showDateSelect:r.props.showDateSelect,showTimeSelect:r.props.showTimeSelect,showTimeSelectOnly:r.props.showTimeSelectOnly,onTimeChange:r.handleTimeChange,timeFormat:r.props.timeFormat,timeIntervals:r.props.timeIntervals,minTime:r.props.minTime,maxTime:r.props.maxTime,excludeTimes:r.props.excludeTimes,filterTime:r.props.filterTime,timeCaption:r.props.timeCaption,className:r.props.calendarClassName,container:r.props.calendarContainer,yearItemNumber:r.props.yearItemNumber,yearDropdownItemNumber:r.props.yearDropdownItemNumber,previousMonthAriaLabel:r.props.previousMonthAriaLabel,previousMonthButtonLabel:r.props.previousMonthButtonLabel,nextMonthAriaLabel:r.props.nextMonthAriaLabel,nextMonthButtonLabel:r.props.nextMonthButtonLabel,previousYearAriaLabel:r.props.previousYearAriaLabel,previousYearButtonLabel:r.props.previousYearButtonLabel,nextYearAriaLabel:r.props.nextYearAriaLabel,nextYearButtonLabel:r.props.nextYearButtonLabel,timeInputLabel:r.props.timeInputLabel,disabledKeyboardNavigation:r.props.disabledKeyboardNavigation,renderCustomHeader:r.props.renderCustomHeader,popperProps:r.props.popperProps,renderDayContents:r.props.renderDayContents,renderMonthContent:r.props.renderMonthContent,renderQuarterContent:r.props.renderQuarterContent,renderYearContent:r.props.renderYearContent,onDayMouseEnter:r.props.onDayMouseEnter,onMonthMouseLeave:r.props.onMonthMouseLeave,onYearMouseEnter:r.props.onYearMouseEnter,onYearMouseLeave:r.props.onYearMouseLeave,selectsDisabledDaysInRange:r.props.selectsDisabledDaysInRange,showTimeInput:r.props.showTimeInput,showMonthYearPicker:r.props.showMonthYearPicker,showFullMonthYearPicker:r.props.showFullMonthYearPicker,showTwoColumnMonthYearPicker:r.props.showTwoColumnMonthYearPicker,showFourColumnMonthYearPicker:r.props.showFourColumnMonthYearPicker,showYearPicker:r.props.showYearPicker,showQuarterYearPicker:r.props.showQuarterYearPicker,showWeekPicker:r.props.showWeekPicker,excludeScrollbar:r.props.excludeScrollbar,handleOnKeyDown:r.props.onKeyDown,handleOnDayKeyDown:r.onDayKeyDown,isInputFocused:r.state.focused,customTimeInput:r.props.customTimeInput,setPreSelection:r.setPreSelection,usePointerEvent:r.props.usePointerEvent,yearClassName:r.props.yearClassName},r.props.children)}),w(r,"renderAriaLiveRegion",function(){var s=r.props,o=s.dateFormat,i=s.locale,a=r.props.showTimeInput||r.props.showTimeSelect,l=a?"PPPPp":"PPPP",u;return r.props.selectsRange?u="Selected start date: ".concat(ct(r.props.startDate,{dateFormat:l,locale:i}),". ").concat(r.props.endDate?"End date: "+ct(r.props.endDate,{dateFormat:l,locale:i}):""):r.props.showTimeSelectOnly?u="Selected time: ".concat(ct(r.props.selected,{dateFormat:o,locale:i})):r.props.showYearPicker?u="Selected year: ".concat(ct(r.props.selected,{dateFormat:"yyyy",locale:i})):r.props.showMonthYearPicker?u="Selected month: ".concat(ct(r.props.selected,{dateFormat:"MMMM yyyy",locale:i})):r.props.showQuarterYearPicker?u="Selected quarter: ".concat(ct(r.props.selected,{dateFormat:"yyyy, QQQ",locale:i})):u="Selected date: ".concat(ct(r.props.selected,{dateFormat:l,locale:i})),C.createElement("span",{role:"alert","aria-live":"polite",className:"react-datepicker__aria-live"},u)}),w(r,"renderDateInput",function(){var s,o=be(r.props.className,w({},_h,r.state.open)),i=r.props.customInput||C.createElement("input",{type:"text"}),a=r.props.customInputRef||"ref",l=typeof r.props.value=="string"?r.props.value:typeof r.state.inputValue=="string"?r.state.inputValue:r.props.selectsRange?Tk(r.props.startDate,r.props.endDate,r.props):r.props.selectsMultiple?Ok(r.props.selectedDates,r.props):ct(r.props.selected,r.props);return C.cloneElement(i,(s={},w(w(w(w(w(w(w(w(w(w(s,a,function(u){r.input=u}),"value",l),"onBlur",r.handleBlur),"onChange",r.handleChange),"onClick",r.onInputClick),"onFocus",r.handleFocus),"onKeyDown",r.onInputKeyDown),"id",r.props.id),"name",r.props.name),"form",r.props.form),w(w(w(w(w(w(w(w(w(w(s,"autoFocus",r.props.autoFocus),"placeholder",r.props.placeholderText),"disabled",r.props.disabled),"autoComplete",r.props.autoComplete),"className",be(i.props.className,o)),"title",r.props.title),"readOnly",r.props.readOnly),"required",r.props.required),"tabIndex",r.props.tabIndex),"aria-describedby",r.props.ariaDescribedBy),w(w(w(s,"aria-invalid",r.props.ariaInvalid),"aria-labelledby",r.props.ariaLabelledBy),"aria-required",r.props.ariaRequired)))}),w(r,"renderClearButton",function(){var s=r.props,o=s.isClearable,i=s.disabled,a=s.selected,l=s.startDate,u=s.endDate,c=s.clearButtonTitle,d=s.clearButtonClassName,f=d===void 0?"":d,p=s.ariaLabelClose,y=p===void 0?"Close":p,_=s.selectedDates;return o&&(a!=null||l!=null||u!=null||_!=null&&_.length)?C.createElement("button",{type:"button",className:be("react-datepicker__close-icon",f,{"react-datepicker__close-icon--disabled":i}),disabled:i,"aria-label":y,onClick:r.onClearClick,title:c,tabIndex:-1}):null}),r.state=r.calcInitialState(),r.preventFocusTimeout=null,r}return Oe(t,e),Te(t,[{key:"componentDidMount",value:function(){window.addEventListener("scroll",this.onScroll,!0)}},{key:"componentDidUpdate",value:function(r,s){r.inline&&DS(r.selected,this.props.selected)&&this.setPreSelection(this.props.selected),this.state.monthSelectedIn!==void 0&&r.monthsShown!==this.props.monthsShown&&this.setState({monthSelectedIn:0}),r.highlightDates!==this.props.highlightDates&&this.setState({highlightDates:hh(this.props.highlightDates)}),!s.focused&&!tr(r.selected,this.props.selected)&&this.setState({inputValue:null}),s.open!==this.state.open&&(s.open===!1&&this.state.open===!0&&this.props.onCalendarOpen(),s.open===!0&&this.state.open===!1&&this.props.onCalendarClose())}},{key:"componentWillUnmount",value:function(){this.clearPreventFocusTimeout(),window.removeEventListener("scroll",this.onScroll,!0)}},{key:"renderInputContainer",value:function(){var r=this.props,s=r.showIcon,o=r.icon,i=r.calendarIconClassname,a=r.toggleCalendarOnIconClick,l=this.state.open;return C.createElement("div",{className:"react-datepicker__input-container".concat(s?" react-datepicker__view-calendar-icon":"")},s&&C.createElement(vS,_o({icon:o,className:"".concat(i," ").concat(l&&"react-datepicker-ignore-onclickoutside")},a?{onClick:this.toggleCalendar}:null)),this.state.isRenderAriaLiveMessage&&this.renderAriaLiveRegion(),this.renderDateInput(),this.renderClearButton())}},{key:"render",value:function(){var r=this.renderCalendar();if(this.props.inline)return r;if(this.props.withPortal){var s=this.state.open?C.createElement(Vg,{enableTabLoop:this.props.enableTabLoop},C.createElement("div",{className:"react-datepicker__portal",tabIndex:-1,onKeyDown:this.onPortalKeyDown},r)):null;return this.state.open&&this.props.portalId&&(s=C.createElement(Hg,{portalId:this.props.portalId,portalHost:this.props.portalHost},s)),C.createElement("div",null,this.renderInputContainer(),s)}return C.createElement(SS,{className:this.props.popperClassName,wrapperClassName:this.props.wrapperClassName,hidePopper:!this.isCalendarOpen(),portalId:this.props.portalId,portalHost:this.props.portalHost,popperModifiers:this.props.popperModifiers,targetComponent:this.renderInputContainer(),popperContainer:this.props.popperContainer,popperComponent:r,popperPlacement:this.props.popperPlacement,popperProps:this.props.popperProps,popperOnKeyDown:this.onPopperKeyDown,enableTabLoop:this.props.enableTabLoop,showArrow:this.props.showPopperArrow})}}],[{key:"defaultProps",get:function(){return{allowSameDay:!1,dateFormat:"MM/dd/yyyy",dateFormatCalendar:"LLLL yyyy",onChange:function(){},disabled:!1,disabledKeyboardNavigation:!1,dropdownMode:"scroll",onFocus:function(){},onBlur:function(){},onKeyDown:function(){},onInputClick:function(){},onSelect:function(){},onClickOutside:function(){},onMonthChange:function(){},onCalendarOpen:function(){},onCalendarClose:function(){},preventOpenOnFocus:!1,onYearChange:function(){},onInputError:function(){},monthsShown:1,readOnly:!1,withPortal:!1,selectsDisabledDaysInRange:!1,shouldCloseOnSelect:!0,showTimeSelect:!1,showTimeInput:!1,showPreviousMonths:!1,showMonthYearPicker:!1,showFullMonthYearPicker:!1,showTwoColumnMonthYearPicker:!1,showFourColumnMonthYearPicker:!1,showYearPicker:!1,showQuarterYearPicker:!1,showWeekPicker:!1,strictParsing:!1,swapRange:!1,timeIntervals:30,timeCaption:"Time",previousMonthAriaLabel:"Previous Month",previousMonthButtonLabel:"Previous Month",nextMonthAriaLabel:"Next Month",nextMonthButtonLabel:"Next Month",previousYearAriaLabel:"Previous Year",previousYearButtonLabel:"Previous Year",nextYearAriaLabel:"Next Year",nextYearButtonLabel:"Next Year",timeInputLabel:"Time",enableTabLoop:!0,yearItemNumber:Mo,focusSelectedMonth:!1,showPopperArrow:!0,excludeScrollbar:!0,customTimeInput:null,calendarStartDay:void 0,toggleCalendarOnIconClick:!1,usePointerEvent:!1}}}])}(C.Component),ES="input",Al="navigate";function PS({selectedDate:e,onDateChange:t,rushStatus:n}){const r=(s,o)=>{const i=qe(o,"yyyy-MM-dd"),a=n[i]||"low";return h.jsx("div",{className:be("w-full h-full flex items-center justify-center rounded-full",{"bg-red-100 text-red-800":a==="high","bg-orange-100 text-orange-800":a==="medium","bg-green-100 text-green-800":a==="low"}),children:s})};return h.jsxs("div",{className:"w-full max-w-md",children:[h.jsx(CS,{selected:e,onChange:s=>t(s),inline:!0,renderDayContents:r,minDate:new Date}),h.jsxs("div",{className:"mt-4 flex gap-4 justify-center",children:[h.jsxs("div",{className:"flex items-center gap-2",children:[h.jsx("div",{className:"w-4 h-4 rounded-full bg-red-100"}),h.jsx("span",{className:"text-sm",children:"High Rush"})]}),h.jsxs("div",{className:"flex items-center gap-2",children:[h.jsx("div",{className:"w-4 h-4 rounded-full bg-orange-100"}),h.jsx("span",{className:"text-sm",children:"Medium Rush"})]}),h.jsxs("div",{className:"flex items-center gap-2",children:[h.jsx("div",{className:"w-4 h-4 rounded-full bg-green-100"}),h.jsx("span",{className:"text-sm",children:"Low Rush"})]})]})]})}/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var NS={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const TS=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim(),me=(e,t)=>{const n=S.forwardRef(({color:r="currentColor",size:s=24,strokeWidth:o=2,absoluteStrokeWidth:i,className:a="",children:l,...u},c)=>S.createElement("svg",{ref:c,...NS,width:s,height:s,stroke:r,strokeWidth:i?Number(o)*24/Number(s):o,className:["lucide",`lucide-${TS(e)}`,a].join(" "),...u},[...t.map(([d,f])=>S.createElement(d,f)),...Array.isArray(l)?l:[l]]));return n.displayName=`${e}`,n};/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const xo=me("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Qg=me("Banknote",[["rect",{width:"20",height:"12",x:"2",y:"6",rx:"2",key:"9lu3g6"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}],["path",{d:"M6 12h.01M18 12h.01",key:"113zkx"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const OS=me("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const qu=me("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ni=me("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Kg=me("CreditCard",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const qg=me("Info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const on=me("Loader2",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const MS=me("Lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const jS=me("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const RS=me("Minus",[["path",{d:"M5 12h14",key:"1ays0h"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Gu=me("Package",[["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}],["path",{d:"M21 8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16Z",key:"hh9hay"}],["path",{d:"m3.3 7 8.7 5 8.7-5",key:"g66t2b"}],["path",{d:"M12 22V12",key:"d0xqtd"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Gg=me("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const IS=me("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const LS=me("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const AS=me("ShoppingBag",[["path",{d:"M6 2 3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4Z",key:"hou9p0"}],["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M16 10a4 4 0 0 1-8 0",key:"1ltviw"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const $S=me("ShoppingCart",[["circle",{cx:"8",cy:"21",r:"1",key:"jimo8o"}],["circle",{cx:"19",cy:"21",r:"1",key:"13723u"}],["path",{d:"M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12",key:"9zh506"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const FS=me("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const YS=me("Trash",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const US=me("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const WS=me("XCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const BS=me("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]);function zS({id:e,name:t,price:n,quantity:r,onQuantityChange:s}){return h.jsxs("div",{className:"flex items-center justify-between p-4 border rounded-lg bg-white shadow-sm",children:[h.jsxs("div",{children:[h.jsx("h3",{className:"font-semibold text-lg",children:t}),h.jsxs("p",{className:"text-gray-600",children:["₹",n.toFixed(2)]})]}),h.jsxs("div",{className:"flex items-center gap-3",children:[h.jsx("button",{onClick:()=>s(e,Math.max(0,r-1)),className:"p-1 rounded-full hover:bg-gray-100",children:h.jsx(RS,{className:"w-5 h-5"})}),h.jsx("span",{className:"w-8 text-center",children:r}),h.jsx("button",{onClick:()=>s(e,r+1),className:"p-1 rounded-full hover:bg-gray-100",children:h.jsx(Gg,{className:"w-5 h-5"})})]})]})}function HS({selectedSlot:e,onSlotSelect:t,selectedDate:n,rushStatus:r}){const o=(()=>{const c=[];return[9,10,11,12,14,15,16,17].forEach(f=>{[0,10,20,30,40,50].forEach(p=>{`${f.toString().padStart(2,"0")}${p.toString().padStart(2,"0")}`;const y=f>=12?"PM":"AM",_=`${f>12?f-12:f}:${p.toString().padStart(2,"0")} ${y}`;let x;r&&r[_]?x=r[_]:f>=9&&f<=11?x="high":f>=14&&f<=15||f===12?x="medium":x="low",c.push({time:_,rush:x})})}),c})(),i=new Date,a=n.toDateString()===i.toDateString(),l=c=>{if(!a)return!1;const d=Ci(c,"h:mm aa",new Date);return d.setFullYear(i.getFullYear(),i.getMonth(),i.getDate()),An(d,i)},u=(c,d)=>{if(d)return"bg-gray-200 text-gray-400 cursor-not-allowed";const f="transition-colors";switch(c){case"high":return`${f} bg-red-100 hover:bg-red-200 text-red-800`;case"medium":return`${f} bg-orange-100 hover:bg-orange-200 text-orange-800`;case"low":return`${f} bg-green-100 hover:bg-green-200 text-green-800`}};return h.jsxs("div",{children:[h.jsx("div",{className:"grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-2",children:o.map(c=>{const d=l(c.time);return h.jsx("button",{onClick:()=>!d&&t(c.time),disabled:d,className:be("p-2 rounded-lg text-sm font-medium",e===c.time?"ring-2 ring-blue-500 ring-offset-2":u(c.rush,d)),children:c.time},c.time)})}),h.jsxs("div",{className:"mt-4 flex flex-wrap gap-4 justify-center text-sm",children:[h.jsxs("div",{className:"flex items-center gap-2",children:[h.jsx("div",{className:"w-4 h-4 rounded-full bg-red-100"}),h.jsx("span",{children:"High Rush"})]}),h.jsxs("div",{className:"flex items-center gap-2",children:[h.jsx("div",{className:"w-4 h-4 rounded-full bg-orange-100"}),h.jsx("span",{children:"Medium Rush"})]}),h.jsxs("div",{className:"flex items-center gap-2",children:[h.jsx("div",{className:"w-4 h-4 rounded-full bg-green-100"}),h.jsx("span",{children:"Low Rush"})]}),h.jsxs("div",{className:"flex items-center gap-2",children:[h.jsx("div",{className:"w-4 h-4 rounded-full bg-gray-200"}),h.jsx("span",{children:"Past Time"})]})]})]})}function VS({isOpen:e,onClose:t,bookingData:n,onPaymentComplete:r}){const[s,o]=S.useState(""),[i,a]=S.useState(!1),[l,u]=S.useState(null);if(!e)return null;const c=y=>{o(y),u(null)},d=async()=>{a(!0);try{await new Promise(_=>setTimeout(_,1e3));const y={method:"cash_on_delivery",status:"not_required",paymentId:null,amount:n.total_cost};u("success"),setTimeout(()=>{r(y)},1500)}catch(y){u("error"),console.error("Error processing cash on delivery:",y)}finally{a(!1)}},f=async()=>{a(!0);try{if(!window.Razorpay)throw new Error("Razorpay SDK not loaded");const y={key:"your_razorpay_key_id_here",amount:Math.round(n.total_cost*100),currency:"INR",name:"Stationery Store",description:`Booking for ${n.customer_name}`,order_id:`order_${Date.now()}`,handler:function(x){console.log("Payment successful:",x),u("success");const g={method:"online",status:"completed",paymentId:x.razorpay_payment_id,orderId:x.razorpay_order_id,signature:x.razorpay_signature,amount:n.total_cost};setTimeout(()=>{r(g)},1500)},prefill:{name:n.customer_name,email:"",contact:""},notes:{booking_date:n.date,time_slot:n.time_slot,items:JSON.stringify(n.items)},theme:{color:"#2563eb"},modal:{ondismiss:function(){a(!1),u("cancelled")}}},_=new window.Razorpay(y);_.on("payment.failed",function(x){console.error("Payment failed:",x.error),u("error"),a(!1)}),_.open()}catch(y){console.error("Error initiating payment:",y),u("error"),a(!1)}},p=()=>{s==="cash_on_delivery"?d():s==="online"&&f()};return h.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:h.jsxs("div",{className:"bg-white rounded-lg shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto",children:[h.jsxs("div",{className:"flex items-center justify-between p-6 border-b",children:[h.jsx("h2",{className:"text-xl font-semibold text-gray-900",children:"Choose Payment Method"}),h.jsx("button",{onClick:t,className:"text-gray-400 hover:text-gray-600 transition-colors",children:h.jsx(BS,{className:"w-6 h-6"})})]}),h.jsxs("div",{className:"p-6",children:[h.jsxs("div",{className:"bg-gray-50 p-4 rounded-lg mb-6",children:[h.jsx("h3",{className:"font-medium text-gray-900 mb-2",children:"Booking Summary"}),h.jsxs("div",{className:"text-sm text-gray-600 space-y-1",children:[h.jsxs("p",{children:[h.jsx("strong",{children:"Customer:"})," ",n.customer_name]}),h.jsxs("p",{children:[h.jsx("strong",{children:"Date:"})," ",new Date(n.date).toLocaleDateString()]}),h.jsxs("p",{children:[h.jsx("strong",{children:"Time:"})," ",n.time_slot]}),h.jsxs("p",{children:[h.jsx("strong",{children:"Items:"})," ",n.items.length," item(s)"]}),h.jsx("p",{className:"text-lg font-semibold text-gray-900 mt-2",children:h.jsxs("strong",{children:["Total: ₹",n.total_cost.toFixed(2)]})})]})]}),l==="success"&&h.jsxs("div",{className:"mb-6 p-4 bg-green-50 border border-green-200 rounded-lg flex items-center gap-3",children:[h.jsx(qu,{className:"w-5 h-5 text-green-600"}),h.jsxs("div",{children:[h.jsx("p",{className:"text-green-800 font-medium",children:"Payment Successful!"}),h.jsx("p",{className:"text-green-600 text-sm",children:"Redirecting to confirmation..."})]})]}),l==="error"&&h.jsxs("div",{className:"mb-6 p-4 bg-red-50 border border-red-200 rounded-lg flex items-center gap-3",children:[h.jsx(xo,{className:"w-5 h-5 text-red-600"}),h.jsxs("div",{children:[h.jsx("p",{className:"text-red-800 font-medium",children:"Payment Failed"}),h.jsx("p",{className:"text-red-600 text-sm",children:"Please try again or choose a different method."})]})]}),l==="cancelled"&&h.jsxs("div",{className:"mb-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg flex items-center gap-3",children:[h.jsx(xo,{className:"w-5 h-5 text-yellow-600"}),h.jsxs("div",{children:[h.jsx("p",{className:"text-yellow-800 font-medium",children:"Payment Cancelled"}),h.jsx("p",{className:"text-yellow-600 text-sm",children:"You can try again or choose a different method."})]})]}),!l&&h.jsxs(h.Fragment,{children:[h.jsx("h3",{className:"font-medium text-gray-900 mb-4",children:"Select Payment Method"}),h.jsxs("div",{className:"space-y-3 mb-6",children:[h.jsx("button",{onClick:()=>c("online"),className:`w-full p-4 border-2 rounded-lg transition-colors text-left ${s==="online"?"border-blue-500 bg-blue-50":"border-gray-200 hover:border-gray-300"}`,children:h.jsxs("div",{className:"flex items-center gap-3",children:[h.jsx(Kg,{className:"w-6 h-6 text-blue-600"}),h.jsxs("div",{children:[h.jsx("p",{className:"font-medium text-gray-900",children:"Online Payment"}),h.jsx("p",{className:"text-sm text-gray-600",children:"Pay securely with UPI, Cards, Net Banking"})]})]})}),h.jsx("button",{onClick:()=>c("cash_on_delivery"),className:`w-full p-4 border-2 rounded-lg transition-colors text-left ${s==="cash_on_delivery"?"border-green-500 bg-green-50":"border-gray-200 hover:border-gray-300"}`,children:h.jsxs("div",{className:"flex items-center gap-3",children:[h.jsx(Qg,{className:"w-6 h-6 text-green-600"}),h.jsxs("div",{children:[h.jsx("p",{className:"font-medium text-gray-900",children:"Cash on Delivery"}),h.jsx("p",{className:"text-sm text-gray-600",children:"Pay when you receive your order"})]})]})})]}),h.jsx("button",{onClick:p,disabled:!s||i,className:"w-full bg-blue-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2",children:i?h.jsxs(h.Fragment,{children:[h.jsx(on,{className:"w-4 h-4 animate-spin"}),"Processing..."]}):"Proceed to Payment"})]})]})]})})}const QS="modulepreload",KS=function(e){return"/"+e},xh={},ls=function(t,n,r){let s=Promise.resolve();if(n&&n.length>0){document.getElementsByTagName("link");const i=document.querySelector("meta[property=csp-nonce]"),a=(i==null?void 0:i.nonce)||(i==null?void 0:i.getAttribute("nonce"));s=Promise.allSettled(n.map(l=>{if(l=KS(l),l in xh)return;xh[l]=!0;const u=l.endsWith(".css"),c=u?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${l}"]${c}`))return;const d=document.createElement("link");if(d.rel=u?"stylesheet":QS,u||(d.as="script"),d.crossOrigin="",d.href=l,a&&d.setAttribute("nonce",a),document.head.appendChild(d),u)return new Promise((f,p)=>{d.addEventListener("load",f),d.addEventListener("error",()=>p(new Error(`Unable to preload CSS for ${l}`)))})}))}function o(i){const a=new Event("vite:preloadError",{cancelable:!0});if(a.payload=i,window.dispatchEvent(a),!a.defaultPrevented)throw i}return s.then(i=>{for(const a of i||[])a.status==="rejected"&&o(a.reason);return t().catch(o)})},qS=e=>{let t;return e?t=e:typeof fetch>"u"?t=(...n)=>ls(async()=>{const{default:r}=await Promise.resolve().then(()=>gs);return{default:r}},void 0).then(({default:r})=>r(...n)):t=fetch,(...n)=>t(...n)};class md extends Error{constructor(t,n="FunctionsError",r){super(t),this.name=n,this.context=r}}class GS extends md{constructor(t){super("Failed to send a request to the Edge Function","FunctionsFetchError",t)}}class JS extends md{constructor(t){super("Relay Error invoking the Edge Function","FunctionsRelayError",t)}}class XS extends md{constructor(t){super("Edge Function returned a non-2xx status code","FunctionsHttpError",t)}}var Ju;(function(e){e.Any="any",e.ApNortheast1="ap-northeast-1",e.ApNortheast2="ap-northeast-2",e.ApSouth1="ap-south-1",e.ApSoutheast1="ap-southeast-1",e.ApSoutheast2="ap-southeast-2",e.CaCentral1="ca-central-1",e.EuCentral1="eu-central-1",e.EuWest1="eu-west-1",e.EuWest2="eu-west-2",e.EuWest3="eu-west-3",e.SaEast1="sa-east-1",e.UsEast1="us-east-1",e.UsWest1="us-west-1",e.UsWest2="us-west-2"})(Ju||(Ju={}));var ZS=function(e,t,n,r){function s(o){return o instanceof n?o:new n(function(i){i(o)})}return new(n||(n=Promise))(function(o,i){function a(c){try{u(r.next(c))}catch(d){i(d)}}function l(c){try{u(r.throw(c))}catch(d){i(d)}}function u(c){c.done?o(c.value):s(c.value).then(a,l)}u((r=r.apply(e,t||[])).next())})};class eb{constructor(t,{headers:n={},customFetch:r,region:s=Ju.Any}={}){this.url=t,this.headers=n,this.region=s,this.fetch=qS(r)}setAuth(t){this.headers.Authorization=`Bearer ${t}`}invoke(t,n={}){var r;return ZS(this,void 0,void 0,function*(){try{const{headers:s,method:o,body:i}=n;let a={},{region:l}=n;l||(l=this.region),l&&l!=="any"&&(a["x-region"]=l);let u;i&&(s&&!Object.prototype.hasOwnProperty.call(s,"Content-Type")||!s)&&(typeof Blob<"u"&&i instanceof Blob||i instanceof ArrayBuffer?(a["Content-Type"]="application/octet-stream",u=i):typeof i=="string"?(a["Content-Type"]="text/plain",u=i):typeof FormData<"u"&&i instanceof FormData?u=i:(a["Content-Type"]="application/json",u=JSON.stringify(i)));const c=yield this.fetch(`${this.url}/${t}`,{method:o||"POST",headers:Object.assign(Object.assign(Object.assign({},a),this.headers),s),body:u}).catch(y=>{throw new GS(y)}),d=c.headers.get("x-relay-error");if(d&&d==="true")throw new JS(c);if(!c.ok)throw new XS(c);let f=((r=c.headers.get("Content-Type"))!==null&&r!==void 0?r:"text/plain").split(";")[0].trim(),p;return f==="application/json"?p=yield c.json():f==="application/octet-stream"?p=yield c.blob():f==="text/event-stream"?p=c:f==="multipart/form-data"?p=yield c.formData():p=yield c.text(),{data:p,error:null}}catch(s){return{data:null,error:s}}})}}var Ke={},gd={},Va={},jo={},Qa={},Ka={},tb=function(){if(typeof self<"u")return self;if(typeof window<"u")return window;if(typeof global<"u")return global;throw new Error("unable to locate global object")},us=tb();const nb=us.fetch,Jg=us.fetch.bind(us),Xg=us.Headers,rb=us.Request,sb=us.Response,gs=Object.freeze(Object.defineProperty({__proto__:null,Headers:Xg,Request:rb,Response:sb,default:Jg,fetch:nb},Symbol.toStringTag,{value:"Module"})),ob=Dy(gs);var qa={};Object.defineProperty(qa,"__esModule",{value:!0});let ib=class extends Error{constructor(t){super(t.message),this.name="PostgrestError",this.details=t.details,this.hint=t.hint,this.code=t.code}};qa.default=ib;var Zg=mt&&mt.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(Ka,"__esModule",{value:!0});const ab=Zg(ob),lb=Zg(qa);let ub=class{constructor(t){this.shouldThrowOnError=!1,this.method=t.method,this.url=t.url,this.headers=t.headers,this.schema=t.schema,this.body=t.body,this.shouldThrowOnError=t.shouldThrowOnError,this.signal=t.signal,this.isMaybeSingle=t.isMaybeSingle,t.fetch?this.fetch=t.fetch:typeof fetch>"u"?this.fetch=ab.default:this.fetch=fetch}throwOnError(){return this.shouldThrowOnError=!0,this}setHeader(t,n){return this.headers=Object.assign({},this.headers),this.headers[t]=n,this}then(t,n){this.schema===void 0||(["GET","HEAD"].includes(this.method)?this.headers["Accept-Profile"]=this.schema:this.headers["Content-Profile"]=this.schema),this.method!=="GET"&&this.method!=="HEAD"&&(this.headers["Content-Type"]="application/json");const r=this.fetch;let s=r(this.url.toString(),{method:this.method,headers:this.headers,body:JSON.stringify(this.body),signal:this.signal}).then(async o=>{var i,a,l;let u=null,c=null,d=null,f=o.status,p=o.statusText;if(o.ok){if(this.method!=="HEAD"){const g=await o.text();g===""||(this.headers.Accept==="text/csv"||this.headers.Accept&&this.headers.Accept.includes("application/vnd.pgrst.plan+text")?c=g:c=JSON.parse(g))}const _=(i=this.headers.Prefer)===null||i===void 0?void 0:i.match(/count=(exact|planned|estimated)/),x=(a=o.headers.get("content-range"))===null||a===void 0?void 0:a.split("/");_&&x&&x.length>1&&(d=parseInt(x[1])),this.isMaybeSingle&&this.method==="GET"&&Array.isArray(c)&&(c.length>1?(u={code:"PGRST116",details:`Results contain ${c.length} rows, application/vnd.pgrst.object+json requires 1 row`,hint:null,message:"JSON object requested, multiple (or no) rows returned"},c=null,d=null,f=406,p="Not Acceptable"):c.length===1?c=c[0]:c=null)}else{const _=await o.text();try{u=JSON.parse(_),Array.isArray(u)&&o.status===404&&(c=[],u=null,f=200,p="OK")}catch{o.status===404&&_===""?(f=204,p="No Content"):u={message:_}}if(u&&this.isMaybeSingle&&(!((l=u==null?void 0:u.details)===null||l===void 0)&&l.includes("0 rows"))&&(u=null,f=200,p="OK"),u&&this.shouldThrowOnError)throw new lb.default(u)}return{error:u,data:c,count:d,status:f,statusText:p}});return this.shouldThrowOnError||(s=s.catch(o=>{var i,a,l;return{error:{message:`${(i=o==null?void 0:o.name)!==null&&i!==void 0?i:"FetchError"}: ${o==null?void 0:o.message}`,details:`${(a=o==null?void 0:o.stack)!==null&&a!==void 0?a:""}`,hint:"",code:`${(l=o==null?void 0:o.code)!==null&&l!==void 0?l:""}`},data:null,count:null,status:0,statusText:""}})),s.then(t,n)}};Ka.default=ub;var cb=mt&&mt.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(Qa,"__esModule",{value:!0});const db=cb(Ka);let fb=class extends db.default{select(t){let n=!1;const r=(t??"*").split("").map(s=>/\s/.test(s)&&!n?"":(s==='"'&&(n=!n),s)).join("");return this.url.searchParams.set("select",r),this.headers.Prefer&&(this.headers.Prefer+=","),this.headers.Prefer+="return=representation",this}order(t,{ascending:n=!0,nullsFirst:r,foreignTable:s,referencedTable:o=s}={}){const i=o?`${o}.order`:"order",a=this.url.searchParams.get(i);return this.url.searchParams.set(i,`${a?`${a},`:""}${t}.${n?"asc":"desc"}${r===void 0?"":r?".nullsfirst":".nullslast"}`),this}limit(t,{foreignTable:n,referencedTable:r=n}={}){const s=typeof r>"u"?"limit":`${r}.limit`;return this.url.searchParams.set(s,`${t}`),this}range(t,n,{foreignTable:r,referencedTable:s=r}={}){const o=typeof s>"u"?"offset":`${s}.offset`,i=typeof s>"u"?"limit":`${s}.limit`;return this.url.searchParams.set(o,`${t}`),this.url.searchParams.set(i,`${n-t+1}`),this}abortSignal(t){return this.signal=t,this}single(){return this.headers.Accept="application/vnd.pgrst.object+json",this}maybeSingle(){return this.method==="GET"?this.headers.Accept="application/json":this.headers.Accept="application/vnd.pgrst.object+json",this.isMaybeSingle=!0,this}csv(){return this.headers.Accept="text/csv",this}geojson(){return this.headers.Accept="application/geo+json",this}explain({analyze:t=!1,verbose:n=!1,settings:r=!1,buffers:s=!1,wal:o=!1,format:i="text"}={}){var a;const l=[t?"analyze":null,n?"verbose":null,r?"settings":null,s?"buffers":null,o?"wal":null].filter(Boolean).join("|"),u=(a=this.headers.Accept)!==null&&a!==void 0?a:"application/json";return this.headers.Accept=`application/vnd.pgrst.plan+${i}; for="${u}"; options=${l};`,i==="json"?this:this}rollback(){var t;return((t=this.headers.Prefer)!==null&&t!==void 0?t:"").trim().length>0?this.headers.Prefer+=",tx=rollback":this.headers.Prefer="tx=rollback",this}returns(){return this}};Qa.default=fb;var hb=mt&&mt.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(jo,"__esModule",{value:!0});const pb=hb(Qa);let mb=class extends pb.default{eq(t,n){return this.url.searchParams.append(t,`eq.${n}`),this}neq(t,n){return this.url.searchParams.append(t,`neq.${n}`),this}gt(t,n){return this.url.searchParams.append(t,`gt.${n}`),this}gte(t,n){return this.url.searchParams.append(t,`gte.${n}`),this}lt(t,n){return this.url.searchParams.append(t,`lt.${n}`),this}lte(t,n){return this.url.searchParams.append(t,`lte.${n}`),this}like(t,n){return this.url.searchParams.append(t,`like.${n}`),this}likeAllOf(t,n){return this.url.searchParams.append(t,`like(all).{${n.join(",")}}`),this}likeAnyOf(t,n){return this.url.searchParams.append(t,`like(any).{${n.join(",")}}`),this}ilike(t,n){return this.url.searchParams.append(t,`ilike.${n}`),this}ilikeAllOf(t,n){return this.url.searchParams.append(t,`ilike(all).{${n.join(",")}}`),this}ilikeAnyOf(t,n){return this.url.searchParams.append(t,`ilike(any).{${n.join(",")}}`),this}is(t,n){return this.url.searchParams.append(t,`is.${n}`),this}in(t,n){const r=Array.from(new Set(n)).map(s=>typeof s=="string"&&new RegExp("[,()]").test(s)?`"${s}"`:`${s}`).join(",");return this.url.searchParams.append(t,`in.(${r})`),this}contains(t,n){return typeof n=="string"?this.url.searchParams.append(t,`cs.${n}`):Array.isArray(n)?this.url.searchParams.append(t,`cs.{${n.join(",")}}`):this.url.searchParams.append(t,`cs.${JSON.stringify(n)}`),this}containedBy(t,n){return typeof n=="string"?this.url.searchParams.append(t,`cd.${n}`):Array.isArray(n)?this.url.searchParams.append(t,`cd.{${n.join(",")}}`):this.url.searchParams.append(t,`cd.${JSON.stringify(n)}`),this}rangeGt(t,n){return this.url.searchParams.append(t,`sr.${n}`),this}rangeGte(t,n){return this.url.searchParams.append(t,`nxl.${n}`),this}rangeLt(t,n){return this.url.searchParams.append(t,`sl.${n}`),this}rangeLte(t,n){return this.url.searchParams.append(t,`nxr.${n}`),this}rangeAdjacent(t,n){return this.url.searchParams.append(t,`adj.${n}`),this}overlaps(t,n){return typeof n=="string"?this.url.searchParams.append(t,`ov.${n}`):this.url.searchParams.append(t,`ov.{${n.join(",")}}`),this}textSearch(t,n,{config:r,type:s}={}){let o="";s==="plain"?o="pl":s==="phrase"?o="ph":s==="websearch"&&(o="w");const i=r===void 0?"":`(${r})`;return this.url.searchParams.append(t,`${o}fts${i}.${n}`),this}match(t){return Object.entries(t).forEach(([n,r])=>{this.url.searchParams.append(n,`eq.${r}`)}),this}not(t,n,r){return this.url.searchParams.append(t,`not.${n}.${r}`),this}or(t,{foreignTable:n,referencedTable:r=n}={}){const s=r?`${r}.or`:"or";return this.url.searchParams.append(s,`(${t})`),this}filter(t,n,r){return this.url.searchParams.append(t,`${n}.${r}`),this}};jo.default=mb;var gb=mt&&mt.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(Va,"__esModule",{value:!0});const Ns=gb(jo);let yb=class{constructor(t,{headers:n={},schema:r,fetch:s}){this.url=t,this.headers=n,this.schema=r,this.fetch=s}select(t,{head:n=!1,count:r}={}){const s=n?"HEAD":"GET";let o=!1;const i=(t??"*").split("").map(a=>/\s/.test(a)&&!o?"":(a==='"'&&(o=!o),a)).join("");return this.url.searchParams.set("select",i),r&&(this.headers.Prefer=`count=${r}`),new Ns.default({method:s,url:this.url,headers:this.headers,schema:this.schema,fetch:this.fetch,allowEmpty:!1})}insert(t,{count:n,defaultToNull:r=!0}={}){const s="POST",o=[];if(this.headers.Prefer&&o.push(this.headers.Prefer),n&&o.push(`count=${n}`),r||o.push("missing=default"),this.headers.Prefer=o.join(","),Array.isArray(t)){const i=t.reduce((a,l)=>a.concat(Object.keys(l)),[]);if(i.length>0){const a=[...new Set(i)].map(l=>`"${l}"`);this.url.searchParams.set("columns",a.join(","))}}return new Ns.default({method:s,url:this.url,headers:this.headers,schema:this.schema,body:t,fetch:this.fetch,allowEmpty:!1})}upsert(t,{onConflict:n,ignoreDuplicates:r=!1,count:s,defaultToNull:o=!0}={}){const i="POST",a=[`resolution=${r?"ignore":"merge"}-duplicates`];if(n!==void 0&&this.url.searchParams.set("on_conflict",n),this.headers.Prefer&&a.push(this.headers.Prefer),s&&a.push(`count=${s}`),o||a.push("missing=default"),this.headers.Prefer=a.join(","),Array.isArray(t)){const l=t.reduce((u,c)=>u.concat(Object.keys(c)),[]);if(l.length>0){const u=[...new Set(l)].map(c=>`"${c}"`);this.url.searchParams.set("columns",u.join(","))}}return new Ns.default({method:i,url:this.url,headers:this.headers,schema:this.schema,body:t,fetch:this.fetch,allowEmpty:!1})}update(t,{count:n}={}){const r="PATCH",s=[];return this.headers.Prefer&&s.push(this.headers.Prefer),n&&s.push(`count=${n}`),this.headers.Prefer=s.join(","),new Ns.default({method:r,url:this.url,headers:this.headers,schema:this.schema,body:t,fetch:this.fetch,allowEmpty:!1})}delete({count:t}={}){const n="DELETE",r=[];return t&&r.push(`count=${t}`),this.headers.Prefer&&r.unshift(this.headers.Prefer),this.headers.Prefer=r.join(","),new Ns.default({method:n,url:this.url,headers:this.headers,schema:this.schema,fetch:this.fetch,allowEmpty:!1})}};Va.default=yb;var Ga={},Ja={};Object.defineProperty(Ja,"__esModule",{value:!0});Ja.version=void 0;Ja.version="0.0.0-automated";Object.defineProperty(Ga,"__esModule",{value:!0});Ga.DEFAULT_HEADERS=void 0;const vb=Ja;Ga.DEFAULT_HEADERS={"X-Client-Info":`postgrest-js/${vb.version}`};var ey=mt&&mt.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(gd,"__esModule",{value:!0});const wb=ey(Va),_b=ey(jo),xb=Ga;let kb=class ty{constructor(t,{headers:n={},schema:r,fetch:s}={}){this.url=t,this.headers=Object.assign(Object.assign({},xb.DEFAULT_HEADERS),n),this.schemaName=r,this.fetch=s}from(t){const n=new URL(`${this.url}/${t}`);return new wb.default(n,{headers:Object.assign({},this.headers),schema:this.schemaName,fetch:this.fetch})}schema(t){return new ty(this.url,{headers:this.headers,schema:t,fetch:this.fetch})}rpc(t,n={},{head:r=!1,get:s=!1,count:o}={}){let i;const a=new URL(`${this.url}/rpc/${t}`);let l;r||s?(i=r?"HEAD":"GET",Object.entries(n).filter(([c,d])=>d!==void 0).map(([c,d])=>[c,Array.isArray(d)?`{${d.join(",")}}`:`${d}`]).forEach(([c,d])=>{a.searchParams.append(c,d)})):(i="POST",l=n);const u=Object.assign({},this.headers);return o&&(u.Prefer=`count=${o}`),new _b.default({method:i,url:a,headers:u,schema:this.schemaName,body:l,fetch:this.fetch,allowEmpty:!1})}};gd.default=kb;var ys=mt&&mt.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(Ke,"__esModule",{value:!0});Ke.PostgrestError=Ke.PostgrestBuilder=Ke.PostgrestTransformBuilder=Ke.PostgrestFilterBuilder=Ke.PostgrestQueryBuilder=Ke.PostgrestClient=void 0;const ny=ys(gd);Ke.PostgrestClient=ny.default;const ry=ys(Va);Ke.PostgrestQueryBuilder=ry.default;const sy=ys(jo);Ke.PostgrestFilterBuilder=sy.default;const oy=ys(Qa);Ke.PostgrestTransformBuilder=oy.default;const iy=ys(Ka);Ke.PostgrestBuilder=iy.default;const ay=ys(qa);Ke.PostgrestError=ay.default;var Sb=Ke.default={PostgrestClient:ny.default,PostgrestQueryBuilder:ry.default,PostgrestFilterBuilder:sy.default,PostgrestTransformBuilder:oy.default,PostgrestBuilder:iy.default,PostgrestError:ay.default};const{PostgrestClient:bb,PostgrestQueryBuilder:yC,PostgrestFilterBuilder:vC,PostgrestTransformBuilder:wC,PostgrestBuilder:_C,PostgrestError:xC}=Sb,Db="2.11.2",Cb={"X-Client-Info":`realtime-js/${Db}`},Eb="1.0.0",ly=1e4,Pb=1e3;var Kr;(function(e){e[e.connecting=0]="connecting",e[e.open=1]="open",e[e.closing=2]="closing",e[e.closed=3]="closed"})(Kr||(Kr={}));var tt;(function(e){e.closed="closed",e.errored="errored",e.joined="joined",e.joining="joining",e.leaving="leaving"})(tt||(tt={}));var kt;(function(e){e.close="phx_close",e.error="phx_error",e.join="phx_join",e.reply="phx_reply",e.leave="phx_leave",e.access_token="access_token"})(kt||(kt={}));var Xu;(function(e){e.websocket="websocket"})(Xu||(Xu={}));var Jn;(function(e){e.Connecting="connecting",e.Open="open",e.Closing="closing",e.Closed="closed"})(Jn||(Jn={}));class Nb{constructor(){this.HEADER_LENGTH=1}decode(t,n){return t.constructor===ArrayBuffer?n(this._binaryDecode(t)):n(typeof t=="string"?JSON.parse(t):{})}_binaryDecode(t){const n=new DataView(t),r=new TextDecoder;return this._decodeBroadcast(t,n,r)}_decodeBroadcast(t,n,r){const s=n.getUint8(1),o=n.getUint8(2);let i=this.HEADER_LENGTH+2;const a=r.decode(t.slice(i,i+s));i=i+s;const l=r.decode(t.slice(i,i+o));i=i+o;const u=JSON.parse(r.decode(t.slice(i,t.byteLength)));return{ref:null,topic:a,event:l,payload:u}}}class uy{constructor(t,n){this.callback=t,this.timerCalc=n,this.timer=void 0,this.tries=0,this.callback=t,this.timerCalc=n}reset(){this.tries=0,clearTimeout(this.timer)}scheduleTimeout(){clearTimeout(this.timer),this.timer=setTimeout(()=>{this.tries=this.tries+1,this.callback()},this.timerCalc(this.tries+1))}}var te;(function(e){e.abstime="abstime",e.bool="bool",e.date="date",e.daterange="daterange",e.float4="float4",e.float8="float8",e.int2="int2",e.int4="int4",e.int4range="int4range",e.int8="int8",e.int8range="int8range",e.json="json",e.jsonb="jsonb",e.money="money",e.numeric="numeric",e.oid="oid",e.reltime="reltime",e.text="text",e.time="time",e.timestamp="timestamp",e.timestamptz="timestamptz",e.timetz="timetz",e.tsrange="tsrange",e.tstzrange="tstzrange"})(te||(te={}));const kh=(e,t,n={})=>{var r;const s=(r=n.skipTypes)!==null&&r!==void 0?r:[];return Object.keys(t).reduce((o,i)=>(o[i]=Tb(i,e,t,s),o),{})},Tb=(e,t,n,r)=>{const s=t.find(a=>a.name===e),o=s==null?void 0:s.type,i=n[e];return o&&!r.includes(o)?cy(o,i):Zu(i)},cy=(e,t)=>{if(e.charAt(0)==="_"){const n=e.slice(1,e.length);return Rb(t,n)}switch(e){case te.bool:return Ob(t);case te.float4:case te.float8:case te.int2:case te.int4:case te.int8:case te.numeric:case te.oid:return Mb(t);case te.json:case te.jsonb:return jb(t);case te.timestamp:return Ib(t);case te.abstime:case te.date:case te.daterange:case te.int4range:case te.int8range:case te.money:case te.reltime:case te.text:case te.time:case te.timestamptz:case te.timetz:case te.tsrange:case te.tstzrange:return Zu(t);default:return Zu(t)}},Zu=e=>e,Ob=e=>{switch(e){case"t":return!0;case"f":return!1;default:return e}},Mb=e=>{if(typeof e=="string"){const t=parseFloat(e);if(!Number.isNaN(t))return t}return e},jb=e=>{if(typeof e=="string")try{return JSON.parse(e)}catch(t){return console.log(`JSON parse error: ${t}`),e}return e},Rb=(e,t)=>{if(typeof e!="string")return e;const n=e.length-1,r=e[n];if(e[0]==="{"&&r==="}"){let o;const i=e.slice(1,n);try{o=JSON.parse("["+i+"]")}catch{o=i?i.split(","):[]}return o.map(a=>cy(t,a))}return e},Ib=e=>typeof e=="string"?e.replace(" ","T"):e,dy=e=>{let t=e;return t=t.replace(/^ws/i,"http"),t=t.replace(/(\/socket\/websocket|\/socket|\/websocket)\/?$/i,""),t.replace(/\/+$/,"")};class $l{constructor(t,n,r={},s=ly){this.channel=t,this.event=n,this.payload=r,this.timeout=s,this.sent=!1,this.timeoutTimer=void 0,this.ref="",this.receivedResp=null,this.recHooks=[],this.refEvent=null}resend(t){this.timeout=t,this._cancelRefEvent(),this.ref="",this.refEvent=null,this.receivedResp=null,this.sent=!1,this.send()}send(){this._hasReceived("timeout")||(this.startTimeout(),this.sent=!0,this.channel.socket.push({topic:this.channel.topic,event:this.event,payload:this.payload,ref:this.ref,join_ref:this.channel._joinRef()}))}updatePayload(t){this.payload=Object.assign(Object.assign({},this.payload),t)}receive(t,n){var r;return this._hasReceived(t)&&n((r=this.receivedResp)===null||r===void 0?void 0:r.response),this.recHooks.push({status:t,callback:n}),this}startTimeout(){if(this.timeoutTimer)return;this.ref=this.channel.socket._makeRef(),this.refEvent=this.channel._replyEventName(this.ref);const t=n=>{this._cancelRefEvent(),this._cancelTimeout(),this.receivedResp=n,this._matchReceive(n)};this.channel._on(this.refEvent,{},t),this.timeoutTimer=setTimeout(()=>{this.trigger("timeout",{})},this.timeout)}trigger(t,n){this.refEvent&&this.channel._trigger(this.refEvent,{status:t,response:n})}destroy(){this._cancelRefEvent(),this._cancelTimeout()}_cancelRefEvent(){this.refEvent&&this.channel._off(this.refEvent,{})}_cancelTimeout(){clearTimeout(this.timeoutTimer),this.timeoutTimer=void 0}_matchReceive({status:t,response:n}){this.recHooks.filter(r=>r.status===t).forEach(r=>r.callback(n))}_hasReceived(t){return this.receivedResp&&this.receivedResp.status===t}}var Sh;(function(e){e.SYNC="sync",e.JOIN="join",e.LEAVE="leave"})(Sh||(Sh={}));class Qs{constructor(t,n){this.channel=t,this.state={},this.pendingDiffs=[],this.joinRef=null,this.caller={onJoin:()=>{},onLeave:()=>{},onSync:()=>{}};const r=(n==null?void 0:n.events)||{state:"presence_state",diff:"presence_diff"};this.channel._on(r.state,{},s=>{const{onJoin:o,onLeave:i,onSync:a}=this.caller;this.joinRef=this.channel._joinRef(),this.state=Qs.syncState(this.state,s,o,i),this.pendingDiffs.forEach(l=>{this.state=Qs.syncDiff(this.state,l,o,i)}),this.pendingDiffs=[],a()}),this.channel._on(r.diff,{},s=>{const{onJoin:o,onLeave:i,onSync:a}=this.caller;this.inPendingSyncState()?this.pendingDiffs.push(s):(this.state=Qs.syncDiff(this.state,s,o,i),a())}),this.onJoin((s,o,i)=>{this.channel._trigger("presence",{event:"join",key:s,currentPresences:o,newPresences:i})}),this.onLeave((s,o,i)=>{this.channel._trigger("presence",{event:"leave",key:s,currentPresences:o,leftPresences:i})}),this.onSync(()=>{this.channel._trigger("presence",{event:"sync"})})}static syncState(t,n,r,s){const o=this.cloneDeep(t),i=this.transformState(n),a={},l={};return this.map(o,(u,c)=>{i[u]||(l[u]=c)}),this.map(i,(u,c)=>{const d=o[u];if(d){const f=c.map(x=>x.presence_ref),p=d.map(x=>x.presence_ref),y=c.filter(x=>p.indexOf(x.presence_ref)<0),_=d.filter(x=>f.indexOf(x.presence_ref)<0);y.length>0&&(a[u]=y),_.length>0&&(l[u]=_)}else a[u]=c}),this.syncDiff(o,{joins:a,leaves:l},r,s)}static syncDiff(t,n,r,s){const{joins:o,leaves:i}={joins:this.transformState(n.joins),leaves:this.transformState(n.leaves)};return r||(r=()=>{}),s||(s=()=>{}),this.map(o,(a,l)=>{var u;const c=(u=t[a])!==null&&u!==void 0?u:[];if(t[a]=this.cloneDeep(l),c.length>0){const d=t[a].map(p=>p.presence_ref),f=c.filter(p=>d.indexOf(p.presence_ref)<0);t[a].unshift(...f)}r(a,c,l)}),this.map(i,(a,l)=>{let u=t[a];if(!u)return;const c=l.map(d=>d.presence_ref);u=u.filter(d=>c.indexOf(d.presence_ref)<0),t[a]=u,s(a,u,l),u.length===0&&delete t[a]}),t}static map(t,n){return Object.getOwnPropertyNames(t).map(r=>n(r,t[r]))}static transformState(t){return t=this.cloneDeep(t),Object.getOwnPropertyNames(t).reduce((n,r)=>{const s=t[r];return"metas"in s?n[r]=s.metas.map(o=>(o.presence_ref=o.phx_ref,delete o.phx_ref,delete o.phx_ref_prev,o)):n[r]=s,n},{})}static cloneDeep(t){return JSON.parse(JSON.stringify(t))}onJoin(t){this.caller.onJoin=t}onLeave(t){this.caller.onLeave=t}onSync(t){this.caller.onSync=t}inPendingSyncState(){return!this.joinRef||this.joinRef!==this.channel._joinRef()}}var bh;(function(e){e.ALL="*",e.INSERT="INSERT",e.UPDATE="UPDATE",e.DELETE="DELETE"})(bh||(bh={}));var Dh;(function(e){e.BROADCAST="broadcast",e.PRESENCE="presence",e.POSTGRES_CHANGES="postgres_changes",e.SYSTEM="system"})(Dh||(Dh={}));var Vt;(function(e){e.SUBSCRIBED="SUBSCRIBED",e.TIMED_OUT="TIMED_OUT",e.CLOSED="CLOSED",e.CHANNEL_ERROR="CHANNEL_ERROR"})(Vt||(Vt={}));class yd{constructor(t,n={config:{}},r){this.topic=t,this.params=n,this.socket=r,this.bindings={},this.state=tt.closed,this.joinedOnce=!1,this.pushBuffer=[],this.subTopic=t.replace(/^realtime:/i,""),this.params.config=Object.assign({broadcast:{ack:!1,self:!1},presence:{key:""},private:!1},n.config),this.timeout=this.socket.timeout,this.joinPush=new $l(this,kt.join,this.params,this.timeout),this.rejoinTimer=new uy(()=>this._rejoinUntilConnected(),this.socket.reconnectAfterMs),this.joinPush.receive("ok",()=>{this.state=tt.joined,this.rejoinTimer.reset(),this.pushBuffer.forEach(s=>s.send()),this.pushBuffer=[]}),this._onClose(()=>{this.rejoinTimer.reset(),this.socket.log("channel",`close ${this.topic} ${this._joinRef()}`),this.state=tt.closed,this.socket._remove(this)}),this._onError(s=>{this._isLeaving()||this._isClosed()||(this.socket.log("channel",`error ${this.topic}`,s),this.state=tt.errored,this.rejoinTimer.scheduleTimeout())}),this.joinPush.receive("timeout",()=>{this._isJoining()&&(this.socket.log("channel",`timeout ${this.topic}`,this.joinPush.timeout),this.state=tt.errored,this.rejoinTimer.scheduleTimeout())}),this._on(kt.reply,{},(s,o)=>{this._trigger(this._replyEventName(o),s)}),this.presence=new Qs(this),this.broadcastEndpointURL=dy(this.socket.endPoint)+"/api/broadcast",this.private=this.params.config.private||!1}subscribe(t,n=this.timeout){var r,s;if(this.socket.isConnected()||this.socket.connect(),this.joinedOnce)throw"tried to subscribe multiple times. 'subscribe' can only be called a single time per channel instance";{const{config:{broadcast:o,presence:i,private:a}}=this.params;this._onError(c=>t==null?void 0:t(Vt.CHANNEL_ERROR,c)),this._onClose(()=>t==null?void 0:t(Vt.CLOSED));const l={},u={broadcast:o,presence:i,postgres_changes:(s=(r=this.bindings.postgres_changes)===null||r===void 0?void 0:r.map(c=>c.filter))!==null&&s!==void 0?s:[],private:a};this.socket.accessTokenValue&&(l.access_token=this.socket.accessTokenValue),this.updateJoinPayload(Object.assign({config:u},l)),this.joinedOnce=!0,this._rejoin(n),this.joinPush.receive("ok",async({postgres_changes:c})=>{var d;if(this.socket.setAuth(),c===void 0){t==null||t(Vt.SUBSCRIBED);return}else{const f=this.bindings.postgres_changes,p=(d=f==null?void 0:f.length)!==null&&d!==void 0?d:0,y=[];for(let _=0;_<p;_++){const x=f[_],{filter:{event:g,schema:m,table:v,filter:k}}=x,E=c&&c[_];if(E&&E.event===g&&E.schema===m&&E.table===v&&E.filter===k)y.push(Object.assign(Object.assign({},x),{id:E.id}));else{this.unsubscribe(),t==null||t(Vt.CHANNEL_ERROR,new Error("mismatch between server and client bindings for postgres changes"));return}}this.bindings.postgres_changes=y,t&&t(Vt.SUBSCRIBED);return}}).receive("error",c=>{t==null||t(Vt.CHANNEL_ERROR,new Error(JSON.stringify(Object.values(c).join(", ")||"error")))}).receive("timeout",()=>{t==null||t(Vt.TIMED_OUT)})}return this}presenceState(){return this.presence.state}async track(t,n={}){return await this.send({type:"presence",event:"track",payload:t},n.timeout||this.timeout)}async untrack(t={}){return await this.send({type:"presence",event:"untrack"},t)}on(t,n,r){return this._on(t,n,r)}async send(t,n={}){var r,s;if(!this._canPush()&&t.type==="broadcast"){const{event:o,payload:i}=t,l={method:"POST",headers:{Authorization:this.socket.accessTokenValue?`Bearer ${this.socket.accessTokenValue}`:"",apikey:this.socket.apiKey?this.socket.apiKey:"","Content-Type":"application/json"},body:JSON.stringify({messages:[{topic:this.subTopic,event:o,payload:i,private:this.private}]})};try{const u=await this._fetchWithTimeout(this.broadcastEndpointURL,l,(r=n.timeout)!==null&&r!==void 0?r:this.timeout);return await((s=u.body)===null||s===void 0?void 0:s.cancel()),u.ok?"ok":"error"}catch(u){return u.name==="AbortError"?"timed out":"error"}}else return new Promise(o=>{var i,a,l;const u=this._push(t.type,t,n.timeout||this.timeout);t.type==="broadcast"&&!(!((l=(a=(i=this.params)===null||i===void 0?void 0:i.config)===null||a===void 0?void 0:a.broadcast)===null||l===void 0)&&l.ack)&&o("ok"),u.receive("ok",()=>o("ok")),u.receive("error",()=>o("error")),u.receive("timeout",()=>o("timed out"))})}updateJoinPayload(t){this.joinPush.updatePayload(t)}unsubscribe(t=this.timeout){this.state=tt.leaving;const n=()=>{this.socket.log("channel",`leave ${this.topic}`),this._trigger(kt.close,"leave",this._joinRef())};return this.rejoinTimer.reset(),this.joinPush.destroy(),new Promise(r=>{const s=new $l(this,kt.leave,{},t);s.receive("ok",()=>{n(),r("ok")}).receive("timeout",()=>{n(),r("timed out")}).receive("error",()=>{r("error")}),s.send(),this._canPush()||s.trigger("ok",{})})}async _fetchWithTimeout(t,n,r){const s=new AbortController,o=setTimeout(()=>s.abort(),r),i=await this.socket.fetch(t,Object.assign(Object.assign({},n),{signal:s.signal}));return clearTimeout(o),i}_push(t,n,r=this.timeout){if(!this.joinedOnce)throw`tried to push '${t}' to '${this.topic}' before joining. Use channel.subscribe() before pushing events`;let s=new $l(this,t,n,r);return this._canPush()?s.send():(s.startTimeout(),this.pushBuffer.push(s)),s}_onMessage(t,n,r){return n}_isMember(t){return this.topic===t}_joinRef(){return this.joinPush.ref}_trigger(t,n,r){var s,o;const i=t.toLocaleLowerCase(),{close:a,error:l,leave:u,join:c}=kt;if(r&&[a,l,u,c].indexOf(i)>=0&&r!==this._joinRef())return;let f=this._onMessage(i,n,r);if(n&&!f)throw"channel onMessage callbacks must return the payload, modified or unmodified";["insert","update","delete"].includes(i)?(s=this.bindings.postgres_changes)===null||s===void 0||s.filter(p=>{var y,_,x;return((y=p.filter)===null||y===void 0?void 0:y.event)==="*"||((x=(_=p.filter)===null||_===void 0?void 0:_.event)===null||x===void 0?void 0:x.toLocaleLowerCase())===i}).map(p=>p.callback(f,r)):(o=this.bindings[i])===null||o===void 0||o.filter(p=>{var y,_,x,g,m,v;if(["broadcast","presence","postgres_changes"].includes(i))if("id"in p){const k=p.id,E=(y=p.filter)===null||y===void 0?void 0:y.event;return k&&((_=n.ids)===null||_===void 0?void 0:_.includes(k))&&(E==="*"||(E==null?void 0:E.toLocaleLowerCase())===((x=n.data)===null||x===void 0?void 0:x.type.toLocaleLowerCase()))}else{const k=(m=(g=p==null?void 0:p.filter)===null||g===void 0?void 0:g.event)===null||m===void 0?void 0:m.toLocaleLowerCase();return k==="*"||k===((v=n==null?void 0:n.event)===null||v===void 0?void 0:v.toLocaleLowerCase())}else return p.type.toLocaleLowerCase()===i}).map(p=>{if(typeof f=="object"&&"ids"in f){const y=f.data,{schema:_,table:x,commit_timestamp:g,type:m,errors:v}=y;f=Object.assign(Object.assign({},{schema:_,table:x,commit_timestamp:g,eventType:m,new:{},old:{},errors:v}),this._getPayloadRecords(y))}p.callback(f,r)})}_isClosed(){return this.state===tt.closed}_isJoined(){return this.state===tt.joined}_isJoining(){return this.state===tt.joining}_isLeaving(){return this.state===tt.leaving}_replyEventName(t){return`chan_reply_${t}`}_on(t,n,r){const s=t.toLocaleLowerCase(),o={type:s,filter:n,callback:r};return this.bindings[s]?this.bindings[s].push(o):this.bindings[s]=[o],this}_off(t,n){const r=t.toLocaleLowerCase();return this.bindings[r]=this.bindings[r].filter(s=>{var o;return!(((o=s.type)===null||o===void 0?void 0:o.toLocaleLowerCase())===r&&yd.isEqual(s.filter,n))}),this}static isEqual(t,n){if(Object.keys(t).length!==Object.keys(n).length)return!1;for(const r in t)if(t[r]!==n[r])return!1;return!0}_rejoinUntilConnected(){this.rejoinTimer.scheduleTimeout(),this.socket.isConnected()&&this._rejoin()}_onClose(t){this._on(kt.close,{},t)}_onError(t){this._on(kt.error,{},n=>t(n))}_canPush(){return this.socket.isConnected()&&this._isJoined()}_rejoin(t=this.timeout){this._isLeaving()||(this.socket._leaveOpenTopic(this.topic),this.state=tt.joining,this.joinPush.resend(t))}_getPayloadRecords(t){const n={new:{},old:{}};return(t.type==="INSERT"||t.type==="UPDATE")&&(n.new=kh(t.columns,t.record)),(t.type==="UPDATE"||t.type==="DELETE")&&(n.old=kh(t.columns,t.old_record)),n}}const Lb=()=>{},Ab=typeof WebSocket<"u",$b=`
  addEventListener("message", (e) => {
    if (e.data.event === "start") {
      setInterval(() => postMessage({ event: "keepAlive" }), e.data.interval);
    }
  });`;class Fb{constructor(t,n){var r;this.accessTokenValue=null,this.apiKey=null,this.channels=[],this.endPoint="",this.httpEndpoint="",this.headers=Cb,this.params={},this.timeout=ly,this.heartbeatIntervalMs=3e4,this.heartbeatTimer=void 0,this.pendingHeartbeatRef=null,this.ref=0,this.logger=Lb,this.conn=null,this.sendBuffer=[],this.serializer=new Nb,this.stateChangeCallbacks={open:[],close:[],error:[],message:[]},this.accessToken=null,this._resolveFetch=o=>{let i;return o?i=o:typeof fetch>"u"?i=(...a)=>ls(async()=>{const{default:l}=await Promise.resolve().then(()=>gs);return{default:l}},void 0).then(({default:l})=>l(...a)):i=fetch,(...a)=>i(...a)},this.endPoint=`${t}/${Xu.websocket}`,this.httpEndpoint=dy(t),n!=null&&n.transport?this.transport=n.transport:this.transport=null,n!=null&&n.params&&(this.params=n.params),n!=null&&n.headers&&(this.headers=Object.assign(Object.assign({},this.headers),n.headers)),n!=null&&n.timeout&&(this.timeout=n.timeout),n!=null&&n.logger&&(this.logger=n.logger),n!=null&&n.heartbeatIntervalMs&&(this.heartbeatIntervalMs=n.heartbeatIntervalMs);const s=(r=n==null?void 0:n.params)===null||r===void 0?void 0:r.apikey;if(s&&(this.accessTokenValue=s,this.apiKey=s),this.reconnectAfterMs=n!=null&&n.reconnectAfterMs?n.reconnectAfterMs:o=>[1e3,2e3,5e3,1e4][o-1]||1e4,this.encode=n!=null&&n.encode?n.encode:(o,i)=>i(JSON.stringify(o)),this.decode=n!=null&&n.decode?n.decode:this.serializer.decode.bind(this.serializer),this.reconnectTimer=new uy(async()=>{this.disconnect(),this.connect()},this.reconnectAfterMs),this.fetch=this._resolveFetch(n==null?void 0:n.fetch),n!=null&&n.worker){if(typeof window<"u"&&!window.Worker)throw new Error("Web Worker is not supported");this.worker=(n==null?void 0:n.worker)||!1,this.workerUrl=n==null?void 0:n.workerUrl}this.accessToken=(n==null?void 0:n.accessToken)||null}connect(){if(!this.conn){if(this.transport){this.conn=new this.transport(this.endpointURL(),void 0,{headers:this.headers});return}if(Ab){this.conn=new WebSocket(this.endpointURL()),this.setupConnection();return}this.conn=new Yb(this.endpointURL(),void 0,{close:()=>{this.conn=null}}),ls(async()=>{const{default:t}=await import("./browser-5OH9Xkx9.js").then(n=>n.b);return{default:t}},[]).then(({default:t})=>{this.conn=new t(this.endpointURL(),void 0,{headers:this.headers}),this.setupConnection()})}}endpointURL(){return this._appendParams(this.endPoint,Object.assign({},this.params,{vsn:Eb}))}disconnect(t,n){this.conn&&(this.conn.onclose=function(){},t?this.conn.close(t,n??""):this.conn.close(),this.conn=null,this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.reconnectTimer.reset())}getChannels(){return this.channels}async removeChannel(t){const n=await t.unsubscribe();return this.channels.length===0&&this.disconnect(),n}async removeAllChannels(){const t=await Promise.all(this.channels.map(n=>n.unsubscribe()));return this.disconnect(),t}log(t,n,r){this.logger(t,n,r)}connectionState(){switch(this.conn&&this.conn.readyState){case Kr.connecting:return Jn.Connecting;case Kr.open:return Jn.Open;case Kr.closing:return Jn.Closing;default:return Jn.Closed}}isConnected(){return this.connectionState()===Jn.Open}channel(t,n={config:{}}){const r=new yd(`realtime:${t}`,n,this);return this.channels.push(r),r}push(t){const{topic:n,event:r,payload:s,ref:o}=t,i=()=>{this.encode(t,a=>{var l;(l=this.conn)===null||l===void 0||l.send(a)})};this.log("push",`${n} ${r} (${o})`,s),this.isConnected()?i():this.sendBuffer.push(i)}async setAuth(t=null){let n=t||this.accessToken&&await this.accessToken()||this.accessTokenValue;if(n){let r=null;try{r=JSON.parse(atob(n.split(".")[1]))}catch{}if(r&&r.exp&&!(Math.floor(Date.now()/1e3)-r.exp<0))return this.log("auth",`InvalidJWTToken: Invalid value for JWT claim "exp" with value ${r.exp}`),Promise.reject(`InvalidJWTToken: Invalid value for JWT claim "exp" with value ${r.exp}`);this.accessTokenValue=n,this.channels.forEach(s=>{n&&s.updateJoinPayload({access_token:n}),s.joinedOnce&&s._isJoined()&&s._push(kt.access_token,{access_token:n})})}}async sendHeartbeat(){var t;if(this.isConnected()){if(this.pendingHeartbeatRef){this.pendingHeartbeatRef=null,this.log("transport","heartbeat timeout. Attempting to re-establish connection"),(t=this.conn)===null||t===void 0||t.close(Pb,"hearbeat timeout");return}this.pendingHeartbeatRef=this._makeRef(),this.push({topic:"phoenix",event:"heartbeat",payload:{},ref:this.pendingHeartbeatRef}),this.setAuth()}}flushSendBuffer(){this.isConnected()&&this.sendBuffer.length>0&&(this.sendBuffer.forEach(t=>t()),this.sendBuffer=[])}_makeRef(){let t=this.ref+1;return t===this.ref?this.ref=0:this.ref=t,this.ref.toString()}_leaveOpenTopic(t){let n=this.channels.find(r=>r.topic===t&&(r._isJoined()||r._isJoining()));n&&(this.log("transport",`leaving duplicate topic "${t}"`),n.unsubscribe())}_remove(t){this.channels=this.channels.filter(n=>n._joinRef()!==t._joinRef())}setupConnection(){this.conn&&(this.conn.binaryType="arraybuffer",this.conn.onopen=()=>this._onConnOpen(),this.conn.onerror=t=>this._onConnError(t),this.conn.onmessage=t=>this._onConnMessage(t),this.conn.onclose=t=>this._onConnClose(t))}_onConnMessage(t){this.decode(t.data,n=>{let{topic:r,event:s,payload:o,ref:i}=n;i&&i===this.pendingHeartbeatRef&&(this.pendingHeartbeatRef=null),this.log("receive",`${o.status||""} ${r} ${s} ${i&&"("+i+")"||""}`,o),this.channels.filter(a=>a._isMember(r)).forEach(a=>a._trigger(s,o,i)),this.stateChangeCallbacks.message.forEach(a=>a(n))})}async _onConnOpen(){if(this.log("transport",`connected to ${this.endpointURL()}`),this.flushSendBuffer(),this.reconnectTimer.reset(),!this.worker)this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.heartbeatTimer=setInterval(()=>this.sendHeartbeat(),this.heartbeatIntervalMs);else{this.workerUrl?this.log("worker",`starting worker for from ${this.workerUrl}`):this.log("worker","starting default worker");const t=this._workerObjectUrl(this.workerUrl);this.workerRef=new Worker(t),this.workerRef.onerror=n=>{this.log("worker","worker error",n.message),this.workerRef.terminate()},this.workerRef.onmessage=n=>{n.data.event==="keepAlive"&&this.sendHeartbeat()},this.workerRef.postMessage({event:"start",interval:this.heartbeatIntervalMs})}this.stateChangeCallbacks.open.forEach(t=>t())}_onConnClose(t){this.log("transport","close",t),this._triggerChanError(),this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.reconnectTimer.scheduleTimeout(),this.stateChangeCallbacks.close.forEach(n=>n(t))}_onConnError(t){this.log("transport",t.message),this._triggerChanError(),this.stateChangeCallbacks.error.forEach(n=>n(t))}_triggerChanError(){this.channels.forEach(t=>t._trigger(kt.error))}_appendParams(t,n){if(Object.keys(n).length===0)return t;const r=t.match(/\?/)?"&":"?",s=new URLSearchParams(n);return`${t}${r}${s}`}_workerObjectUrl(t){let n;if(t)n=t;else{const r=new Blob([$b],{type:"application/javascript"});n=URL.createObjectURL(r)}return n}}class Yb{constructor(t,n,r){this.binaryType="arraybuffer",this.onclose=()=>{},this.onerror=()=>{},this.onmessage=()=>{},this.onopen=()=>{},this.readyState=Kr.connecting,this.send=()=>{},this.url=null,this.url=t,this.close=r.close}}class vd extends Error{constructor(t){super(t),this.__isStorageError=!0,this.name="StorageError"}}function De(e){return typeof e=="object"&&e!==null&&"__isStorageError"in e}class Ub extends vd{constructor(t,n){super(t),this.name="StorageApiError",this.status=n}toJSON(){return{name:this.name,message:this.message,status:this.status}}}class ec extends vd{constructor(t,n){super(t),this.name="StorageUnknownError",this.originalError=n}}var Wb=function(e,t,n,r){function s(o){return o instanceof n?o:new n(function(i){i(o)})}return new(n||(n=Promise))(function(o,i){function a(c){try{u(r.next(c))}catch(d){i(d)}}function l(c){try{u(r.throw(c))}catch(d){i(d)}}function u(c){c.done?o(c.value):s(c.value).then(a,l)}u((r=r.apply(e,t||[])).next())})};const fy=e=>{let t;return e?t=e:typeof fetch>"u"?t=(...n)=>ls(async()=>{const{default:r}=await Promise.resolve().then(()=>gs);return{default:r}},void 0).then(({default:r})=>r(...n)):t=fetch,(...n)=>t(...n)},Bb=()=>Wb(void 0,void 0,void 0,function*(){return typeof Response>"u"?(yield ls(()=>Promise.resolve().then(()=>gs),void 0)).Response:Response}),tc=e=>{if(Array.isArray(e))return e.map(n=>tc(n));if(typeof e=="function"||e!==Object(e))return e;const t={};return Object.entries(e).forEach(([n,r])=>{const s=n.replace(/([-_][a-z])/gi,o=>o.toUpperCase().replace(/[-_]/g,""));t[s]=tc(r)}),t};var wr=function(e,t,n,r){function s(o){return o instanceof n?o:new n(function(i){i(o)})}return new(n||(n=Promise))(function(o,i){function a(c){try{u(r.next(c))}catch(d){i(d)}}function l(c){try{u(r.throw(c))}catch(d){i(d)}}function u(c){c.done?o(c.value):s(c.value).then(a,l)}u((r=r.apply(e,t||[])).next())})};const Fl=e=>e.msg||e.message||e.error_description||e.error||JSON.stringify(e),zb=(e,t,n)=>wr(void 0,void 0,void 0,function*(){const r=yield Bb();e instanceof r&&!(n!=null&&n.noResolveJson)?e.json().then(s=>{t(new Ub(Fl(s),e.status||500))}).catch(s=>{t(new ec(Fl(s),s))}):t(new ec(Fl(e),e))}),Hb=(e,t,n,r)=>{const s={method:e,headers:(t==null?void 0:t.headers)||{}};return e==="GET"?s:(s.headers=Object.assign({"Content-Type":"application/json"},t==null?void 0:t.headers),r&&(s.body=JSON.stringify(r)),Object.assign(Object.assign({},s),n))};function Ro(e,t,n,r,s,o){return wr(this,void 0,void 0,function*(){return new Promise((i,a)=>{e(n,Hb(t,r,s,o)).then(l=>{if(!l.ok)throw l;return r!=null&&r.noResolveJson?l:l.json()}).then(l=>i(l)).catch(l=>zb(l,a,r))})})}function ma(e,t,n,r){return wr(this,void 0,void 0,function*(){return Ro(e,"GET",t,n,r)})}function mn(e,t,n,r,s){return wr(this,void 0,void 0,function*(){return Ro(e,"POST",t,r,s,n)})}function Vb(e,t,n,r,s){return wr(this,void 0,void 0,function*(){return Ro(e,"PUT",t,r,s,n)})}function Qb(e,t,n,r){return wr(this,void 0,void 0,function*(){return Ro(e,"HEAD",t,Object.assign(Object.assign({},n),{noResolveJson:!0}),r)})}function hy(e,t,n,r,s){return wr(this,void 0,void 0,function*(){return Ro(e,"DELETE",t,r,s,n)})}var Ve=function(e,t,n,r){function s(o){return o instanceof n?o:new n(function(i){i(o)})}return new(n||(n=Promise))(function(o,i){function a(c){try{u(r.next(c))}catch(d){i(d)}}function l(c){try{u(r.throw(c))}catch(d){i(d)}}function u(c){c.done?o(c.value):s(c.value).then(a,l)}u((r=r.apply(e,t||[])).next())})};const Kb={limit:100,offset:0,sortBy:{column:"name",order:"asc"}},Ch={cacheControl:"3600",contentType:"text/plain;charset=UTF-8",upsert:!1};class qb{constructor(t,n={},r,s){this.url=t,this.headers=n,this.bucketId=r,this.fetch=fy(s)}uploadOrUpdate(t,n,r,s){return Ve(this,void 0,void 0,function*(){try{let o;const i=Object.assign(Object.assign({},Ch),s);let a=Object.assign(Object.assign({},this.headers),t==="POST"&&{"x-upsert":String(i.upsert)});const l=i.metadata;typeof Blob<"u"&&r instanceof Blob?(o=new FormData,o.append("cacheControl",i.cacheControl),l&&o.append("metadata",this.encodeMetadata(l)),o.append("",r)):typeof FormData<"u"&&r instanceof FormData?(o=r,o.append("cacheControl",i.cacheControl),l&&o.append("metadata",this.encodeMetadata(l))):(o=r,a["cache-control"]=`max-age=${i.cacheControl}`,a["content-type"]=i.contentType,l&&(a["x-metadata"]=this.toBase64(this.encodeMetadata(l)))),s!=null&&s.headers&&(a=Object.assign(Object.assign({},a),s.headers));const u=this._removeEmptyFolders(n),c=this._getFinalPath(u),d=yield this.fetch(`${this.url}/object/${c}`,Object.assign({method:t,body:o,headers:a},i!=null&&i.duplex?{duplex:i.duplex}:{})),f=yield d.json();return d.ok?{data:{path:u,id:f.Id,fullPath:f.Key},error:null}:{data:null,error:f}}catch(o){if(De(o))return{data:null,error:o};throw o}})}upload(t,n,r){return Ve(this,void 0,void 0,function*(){return this.uploadOrUpdate("POST",t,n,r)})}uploadToSignedUrl(t,n,r,s){return Ve(this,void 0,void 0,function*(){const o=this._removeEmptyFolders(t),i=this._getFinalPath(o),a=new URL(this.url+`/object/upload/sign/${i}`);a.searchParams.set("token",n);try{let l;const u=Object.assign({upsert:Ch.upsert},s),c=Object.assign(Object.assign({},this.headers),{"x-upsert":String(u.upsert)});typeof Blob<"u"&&r instanceof Blob?(l=new FormData,l.append("cacheControl",u.cacheControl),l.append("",r)):typeof FormData<"u"&&r instanceof FormData?(l=r,l.append("cacheControl",u.cacheControl)):(l=r,c["cache-control"]=`max-age=${u.cacheControl}`,c["content-type"]=u.contentType);const d=yield this.fetch(a.toString(),{method:"PUT",body:l,headers:c}),f=yield d.json();return d.ok?{data:{path:o,fullPath:f.Key},error:null}:{data:null,error:f}}catch(l){if(De(l))return{data:null,error:l};throw l}})}createSignedUploadUrl(t,n){return Ve(this,void 0,void 0,function*(){try{let r=this._getFinalPath(t);const s=Object.assign({},this.headers);n!=null&&n.upsert&&(s["x-upsert"]="true");const o=yield mn(this.fetch,`${this.url}/object/upload/sign/${r}`,{},{headers:s}),i=new URL(this.url+o.url),a=i.searchParams.get("token");if(!a)throw new vd("No token returned by API");return{data:{signedUrl:i.toString(),path:t,token:a},error:null}}catch(r){if(De(r))return{data:null,error:r};throw r}})}update(t,n,r){return Ve(this,void 0,void 0,function*(){return this.uploadOrUpdate("PUT",t,n,r)})}move(t,n,r){return Ve(this,void 0,void 0,function*(){try{return{data:yield mn(this.fetch,`${this.url}/object/move`,{bucketId:this.bucketId,sourceKey:t,destinationKey:n,destinationBucket:r==null?void 0:r.destinationBucket},{headers:this.headers}),error:null}}catch(s){if(De(s))return{data:null,error:s};throw s}})}copy(t,n,r){return Ve(this,void 0,void 0,function*(){try{return{data:{path:(yield mn(this.fetch,`${this.url}/object/copy`,{bucketId:this.bucketId,sourceKey:t,destinationKey:n,destinationBucket:r==null?void 0:r.destinationBucket},{headers:this.headers})).Key},error:null}}catch(s){if(De(s))return{data:null,error:s};throw s}})}createSignedUrl(t,n,r){return Ve(this,void 0,void 0,function*(){try{let s=this._getFinalPath(t),o=yield mn(this.fetch,`${this.url}/object/sign/${s}`,Object.assign({expiresIn:n},r!=null&&r.transform?{transform:r.transform}:{}),{headers:this.headers});const i=r!=null&&r.download?`&download=${r.download===!0?"":r.download}`:"";return o={signedUrl:encodeURI(`${this.url}${o.signedURL}${i}`)},{data:o,error:null}}catch(s){if(De(s))return{data:null,error:s};throw s}})}createSignedUrls(t,n,r){return Ve(this,void 0,void 0,function*(){try{const s=yield mn(this.fetch,`${this.url}/object/sign/${this.bucketId}`,{expiresIn:n,paths:t},{headers:this.headers}),o=r!=null&&r.download?`&download=${r.download===!0?"":r.download}`:"";return{data:s.map(i=>Object.assign(Object.assign({},i),{signedUrl:i.signedURL?encodeURI(`${this.url}${i.signedURL}${o}`):null})),error:null}}catch(s){if(De(s))return{data:null,error:s};throw s}})}download(t,n){return Ve(this,void 0,void 0,function*(){const s=typeof(n==null?void 0:n.transform)<"u"?"render/image/authenticated":"object",o=this.transformOptsToQueryString((n==null?void 0:n.transform)||{}),i=o?`?${o}`:"";try{const a=this._getFinalPath(t);return{data:yield(yield ma(this.fetch,`${this.url}/${s}/${a}${i}`,{headers:this.headers,noResolveJson:!0})).blob(),error:null}}catch(a){if(De(a))return{data:null,error:a};throw a}})}info(t){return Ve(this,void 0,void 0,function*(){const n=this._getFinalPath(t);try{const r=yield ma(this.fetch,`${this.url}/object/info/${n}`,{headers:this.headers});return{data:tc(r),error:null}}catch(r){if(De(r))return{data:null,error:r};throw r}})}exists(t){return Ve(this,void 0,void 0,function*(){const n=this._getFinalPath(t);try{return yield Qb(this.fetch,`${this.url}/object/${n}`,{headers:this.headers}),{data:!0,error:null}}catch(r){if(De(r)&&r instanceof ec){const s=r.originalError;if([400,404].includes(s==null?void 0:s.status))return{data:!1,error:r}}throw r}})}getPublicUrl(t,n){const r=this._getFinalPath(t),s=[],o=n!=null&&n.download?`download=${n.download===!0?"":n.download}`:"";o!==""&&s.push(o);const a=typeof(n==null?void 0:n.transform)<"u"?"render/image":"object",l=this.transformOptsToQueryString((n==null?void 0:n.transform)||{});l!==""&&s.push(l);let u=s.join("&");return u!==""&&(u=`?${u}`),{data:{publicUrl:encodeURI(`${this.url}/${a}/public/${r}${u}`)}}}remove(t){return Ve(this,void 0,void 0,function*(){try{return{data:yield hy(this.fetch,`${this.url}/object/${this.bucketId}`,{prefixes:t},{headers:this.headers}),error:null}}catch(n){if(De(n))return{data:null,error:n};throw n}})}list(t,n,r){return Ve(this,void 0,void 0,function*(){try{const s=Object.assign(Object.assign(Object.assign({},Kb),n),{prefix:t||""});return{data:yield mn(this.fetch,`${this.url}/object/list/${this.bucketId}`,s,{headers:this.headers},r),error:null}}catch(s){if(De(s))return{data:null,error:s};throw s}})}encodeMetadata(t){return JSON.stringify(t)}toBase64(t){return typeof Buffer<"u"?Buffer.from(t).toString("base64"):btoa(t)}_getFinalPath(t){return`${this.bucketId}/${t}`}_removeEmptyFolders(t){return t.replace(/^\/|\/$/g,"").replace(/\/+/g,"/")}transformOptsToQueryString(t){const n=[];return t.width&&n.push(`width=${t.width}`),t.height&&n.push(`height=${t.height}`),t.resize&&n.push(`resize=${t.resize}`),t.format&&n.push(`format=${t.format}`),t.quality&&n.push(`quality=${t.quality}`),n.join("&")}}const Gb="2.7.1",Jb={"X-Client-Info":`storage-js/${Gb}`};var br=function(e,t,n,r){function s(o){return o instanceof n?o:new n(function(i){i(o)})}return new(n||(n=Promise))(function(o,i){function a(c){try{u(r.next(c))}catch(d){i(d)}}function l(c){try{u(r.throw(c))}catch(d){i(d)}}function u(c){c.done?o(c.value):s(c.value).then(a,l)}u((r=r.apply(e,t||[])).next())})};class Xb{constructor(t,n={},r){this.url=t,this.headers=Object.assign(Object.assign({},Jb),n),this.fetch=fy(r)}listBuckets(){return br(this,void 0,void 0,function*(){try{return{data:yield ma(this.fetch,`${this.url}/bucket`,{headers:this.headers}),error:null}}catch(t){if(De(t))return{data:null,error:t};throw t}})}getBucket(t){return br(this,void 0,void 0,function*(){try{return{data:yield ma(this.fetch,`${this.url}/bucket/${t}`,{headers:this.headers}),error:null}}catch(n){if(De(n))return{data:null,error:n};throw n}})}createBucket(t,n={public:!1}){return br(this,void 0,void 0,function*(){try{return{data:yield mn(this.fetch,`${this.url}/bucket`,{id:t,name:t,public:n.public,file_size_limit:n.fileSizeLimit,allowed_mime_types:n.allowedMimeTypes},{headers:this.headers}),error:null}}catch(r){if(De(r))return{data:null,error:r};throw r}})}updateBucket(t,n){return br(this,void 0,void 0,function*(){try{return{data:yield Vb(this.fetch,`${this.url}/bucket/${t}`,{id:t,name:t,public:n.public,file_size_limit:n.fileSizeLimit,allowed_mime_types:n.allowedMimeTypes},{headers:this.headers}),error:null}}catch(r){if(De(r))return{data:null,error:r};throw r}})}emptyBucket(t){return br(this,void 0,void 0,function*(){try{return{data:yield mn(this.fetch,`${this.url}/bucket/${t}/empty`,{},{headers:this.headers}),error:null}}catch(n){if(De(n))return{data:null,error:n};throw n}})}deleteBucket(t){return br(this,void 0,void 0,function*(){try{return{data:yield hy(this.fetch,`${this.url}/bucket/${t}`,{},{headers:this.headers}),error:null}}catch(n){if(De(n))return{data:null,error:n};throw n}})}}class Zb extends Xb{constructor(t,n={},r){super(t,n,r)}from(t){return new qb(this.url,this.headers,t,this.fetch)}}const eD="2.48.1";let As="";typeof Deno<"u"?As="deno":typeof document<"u"?As="web":typeof navigator<"u"&&navigator.product==="ReactNative"?As="react-native":As="node";const tD={"X-Client-Info":`supabase-js-${As}/${eD}`},nD={headers:tD},rD={schema:"public"},sD={autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,flowType:"implicit"},oD={};var iD=function(e,t,n,r){function s(o){return o instanceof n?o:new n(function(i){i(o)})}return new(n||(n=Promise))(function(o,i){function a(c){try{u(r.next(c))}catch(d){i(d)}}function l(c){try{u(r.throw(c))}catch(d){i(d)}}function u(c){c.done?o(c.value):s(c.value).then(a,l)}u((r=r.apply(e,t||[])).next())})};const aD=e=>{let t;return e?t=e:typeof fetch>"u"?t=Jg:t=fetch,(...n)=>t(...n)},lD=()=>typeof Headers>"u"?Xg:Headers,uD=(e,t,n)=>{const r=aD(n),s=lD();return(o,i)=>iD(void 0,void 0,void 0,function*(){var a;const l=(a=yield t())!==null&&a!==void 0?a:e;let u=new s(i==null?void 0:i.headers);return u.has("apikey")||u.set("apikey",e),u.has("Authorization")||u.set("Authorization",`Bearer ${l}`),r(o,Object.assign(Object.assign({},i),{headers:u}))})};var cD=function(e,t,n,r){function s(o){return o instanceof n?o:new n(function(i){i(o)})}return new(n||(n=Promise))(function(o,i){function a(c){try{u(r.next(c))}catch(d){i(d)}}function l(c){try{u(r.throw(c))}catch(d){i(d)}}function u(c){c.done?o(c.value):s(c.value).then(a,l)}u((r=r.apply(e,t||[])).next())})};function dD(e){return e.replace(/\/$/,"")}function fD(e,t){const{db:n,auth:r,realtime:s,global:o}=e,{db:i,auth:a,realtime:l,global:u}=t,c={db:Object.assign(Object.assign({},i),n),auth:Object.assign(Object.assign({},a),r),realtime:Object.assign(Object.assign({},l),s),global:Object.assign(Object.assign({},u),o),accessToken:()=>cD(this,void 0,void 0,function*(){return""})};return e.accessToken?c.accessToken=e.accessToken:delete c.accessToken,c}const py="2.67.3",hD="http://localhost:9999",pD="supabase.auth.token",mD={"X-Client-Info":`gotrue-js/${py}`},Eh=10,nc="X-Supabase-Api-Version",my={"2024-01-01":{timestamp:Date.parse("2024-01-01T00:00:00.0Z"),name:"2024-01-01"}};function gD(e){return Math.round(Date.now()/1e3)+e}function yD(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(e){const t=Math.random()*16|0;return(e=="x"?t:t&3|8).toString(16)})}const Nt=()=>typeof window<"u"&&typeof document<"u",Vn={tested:!1,writable:!1},Ks=()=>{if(!Nt())return!1;try{if(typeof globalThis.localStorage!="object")return!1}catch{return!1}if(Vn.tested)return Vn.writable;const e=`lswt-${Math.random()}${Math.random()}`;try{globalThis.localStorage.setItem(e,e),globalThis.localStorage.removeItem(e),Vn.tested=!0,Vn.writable=!0}catch{Vn.tested=!0,Vn.writable=!1}return Vn.writable};function vD(e){const t={},n=new URL(e);if(n.hash&&n.hash[0]==="#")try{new URLSearchParams(n.hash.substring(1)).forEach((s,o)=>{t[o]=s})}catch{}return n.searchParams.forEach((r,s)=>{t[s]=r}),t}const gy=e=>{let t;return e?t=e:typeof fetch>"u"?t=(...n)=>ls(async()=>{const{default:r}=await Promise.resolve().then(()=>gs);return{default:r}},void 0).then(({default:r})=>r(...n)):t=fetch,(...n)=>t(...n)},wD=e=>typeof e=="object"&&e!==null&&"status"in e&&"ok"in e&&"json"in e&&typeof e.json=="function",yy=async(e,t,n)=>{await e.setItem(t,JSON.stringify(n))},ii=async(e,t)=>{const n=await e.getItem(t);if(!n)return null;try{return JSON.parse(n)}catch{return n}},ai=async(e,t)=>{await e.removeItem(t)};function _D(e){const t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";let n="",r,s,o,i,a,l,u,c=0;for(e=e.replace("-","+").replace("_","/");c<e.length;)i=t.indexOf(e.charAt(c++)),a=t.indexOf(e.charAt(c++)),l=t.indexOf(e.charAt(c++)),u=t.indexOf(e.charAt(c++)),r=i<<2|a>>4,s=(a&15)<<4|l>>2,o=(l&3)<<6|u,n=n+String.fromCharCode(r),l!=64&&s!=0&&(n=n+String.fromCharCode(s)),u!=64&&o!=0&&(n=n+String.fromCharCode(o));return n}class Xa{constructor(){this.promise=new Xa.promiseConstructor((t,n)=>{this.resolve=t,this.reject=n})}}Xa.promiseConstructor=Promise;function Ph(e){const t=/^([a-z0-9_-]{4})*($|[a-z0-9_-]{3}=?$|[a-z0-9_-]{2}(==)?$)$/i,n=e.split(".");if(n.length!==3)throw new Error("JWT is not valid: not a JWT structure");if(!t.test(n[1]))throw new Error("JWT is not valid: payload is not in base64url format");const r=n[1];return JSON.parse(_D(r))}async function xD(e){return await new Promise(t=>{setTimeout(()=>t(null),e)})}function kD(e,t){return new Promise((r,s)=>{(async()=>{for(let o=0;o<1/0;o++)try{const i=await e(o);if(!t(o,null,i)){r(i);return}}catch(i){if(!t(o,i)){s(i);return}}})()})}function SD(e){return("0"+e.toString(16)).substr(-2)}function bD(){const t=new Uint32Array(56);if(typeof crypto>"u"){const n="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~",r=n.length;let s="";for(let o=0;o<56;o++)s+=n.charAt(Math.floor(Math.random()*r));return s}return crypto.getRandomValues(t),Array.from(t,SD).join("")}async function DD(e){const n=new TextEncoder().encode(e),r=await crypto.subtle.digest("SHA-256",n),s=new Uint8Array(r);return Array.from(s).map(o=>String.fromCharCode(o)).join("")}function CD(e){return btoa(e).replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,"")}async function ED(e){if(!(typeof crypto<"u"&&typeof crypto.subtle<"u"&&typeof TextEncoder<"u"))return console.warn("WebCrypto API is not supported. Code challenge method will default to use plain instead of sha256."),e;const n=await DD(e);return CD(n)}async function Dr(e,t,n=!1){const r=bD();let s=r;n&&(s+="/PASSWORD_RECOVERY"),await yy(e,`${t}-code-verifier`,s);const o=await ED(r);return[o,r===o?"plain":"s256"]}const PD=/^2[0-9]{3}-(0[1-9]|1[0-2])-(0[1-9]|1[0-9]|2[0-9]|3[0-1])$/i;function ND(e){const t=e.headers.get(nc);if(!t||!t.match(PD))return null;try{return new Date(`${t}T00:00:00.0Z`)}catch{return null}}class wd extends Error{constructor(t,n,r){super(t),this.__isAuthError=!0,this.name="AuthError",this.status=n,this.code=r}}function U(e){return typeof e=="object"&&e!==null&&"__isAuthError"in e}class TD extends wd{constructor(t,n,r){super(t,n,r),this.name="AuthApiError",this.status=n,this.code=r}}function OD(e){return U(e)&&e.name==="AuthApiError"}class vy extends wd{constructor(t,n){super(t),this.name="AuthUnknownError",this.originalError=n}}class _r extends wd{constructor(t,n,r,s){super(t,r,s),this.name=n,this.status=r}}class cn extends _r{constructor(){super("Auth session missing!","AuthSessionMissingError",400,void 0)}}function MD(e){return U(e)&&e.name==="AuthSessionMissingError"}class Yl extends _r{constructor(){super("Auth session or user missing","AuthInvalidTokenResponseError",500,void 0)}}class li extends _r{constructor(t){super(t,"AuthInvalidCredentialsError",400,void 0)}}class ui extends _r{constructor(t,n=null){super(t,"AuthImplicitGrantRedirectError",500,void 0),this.details=null,this.details=n}toJSON(){return{name:this.name,message:this.message,status:this.status,details:this.details}}}function jD(e){return U(e)&&e.name==="AuthImplicitGrantRedirectError"}class Nh extends _r{constructor(t,n=null){super(t,"AuthPKCEGrantCodeExchangeError",500,void 0),this.details=null,this.details=n}toJSON(){return{name:this.name,message:this.message,status:this.status,details:this.details}}}class rc extends _r{constructor(t,n){super(t,"AuthRetryableFetchError",n,void 0)}}function Ul(e){return U(e)&&e.name==="AuthRetryableFetchError"}class Th extends _r{constructor(t,n,r){super(t,"AuthWeakPasswordError",n,"weak_password"),this.reasons=r}}var RD=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var s=0,r=Object.getOwnPropertySymbols(e);s<r.length;s++)t.indexOf(r[s])<0&&Object.prototype.propertyIsEnumerable.call(e,r[s])&&(n[r[s]]=e[r[s]]);return n};const Gn=e=>e.msg||e.message||e.error_description||e.error||JSON.stringify(e),ID=[502,503,504];async function Oh(e){var t;if(!wD(e))throw new rc(Gn(e),0);if(ID.includes(e.status))throw new rc(Gn(e),e.status);let n;try{n=await e.json()}catch(o){throw new vy(Gn(o),o)}let r;const s=ND(e);if(s&&s.getTime()>=my["2024-01-01"].timestamp&&typeof n=="object"&&n&&typeof n.code=="string"?r=n.code:typeof n=="object"&&n&&typeof n.error_code=="string"&&(r=n.error_code),r){if(r==="weak_password")throw new Th(Gn(n),e.status,((t=n.weak_password)===null||t===void 0?void 0:t.reasons)||[]);if(r==="session_not_found")throw new cn}else if(typeof n=="object"&&n&&typeof n.weak_password=="object"&&n.weak_password&&Array.isArray(n.weak_password.reasons)&&n.weak_password.reasons.length&&n.weak_password.reasons.reduce((o,i)=>o&&typeof i=="string",!0))throw new Th(Gn(n),e.status,n.weak_password.reasons);throw new TD(Gn(n),e.status||500,r)}const LD=(e,t,n,r)=>{const s={method:e,headers:(t==null?void 0:t.headers)||{}};return e==="GET"?s:(s.headers=Object.assign({"Content-Type":"application/json;charset=UTF-8"},t==null?void 0:t.headers),s.body=JSON.stringify(r),Object.assign(Object.assign({},s),n))};async function z(e,t,n,r){var s;const o=Object.assign({},r==null?void 0:r.headers);o[nc]||(o[nc]=my["2024-01-01"].name),r!=null&&r.jwt&&(o.Authorization=`Bearer ${r.jwt}`);const i=(s=r==null?void 0:r.query)!==null&&s!==void 0?s:{};r!=null&&r.redirectTo&&(i.redirect_to=r.redirectTo);const a=Object.keys(i).length?"?"+new URLSearchParams(i).toString():"",l=await AD(e,t,n+a,{headers:o,noResolveJson:r==null?void 0:r.noResolveJson},{},r==null?void 0:r.body);return r!=null&&r.xform?r==null?void 0:r.xform(l):{data:Object.assign({},l),error:null}}async function AD(e,t,n,r,s,o){const i=LD(t,r,s,o);let a;try{a=await e(n,Object.assign({},i))}catch(l){throw console.error(l),new rc(Gn(l),0)}if(a.ok||await Oh(a),r!=null&&r.noResolveJson)return a;try{return await a.json()}catch(l){await Oh(l)}}function dn(e){var t;let n=null;UD(e)&&(n=Object.assign({},e),e.expires_at||(n.expires_at=gD(e.expires_in)));const r=(t=e.user)!==null&&t!==void 0?t:e;return{data:{session:n,user:r},error:null}}function Mh(e){const t=dn(e);return!t.error&&e.weak_password&&typeof e.weak_password=="object"&&Array.isArray(e.weak_password.reasons)&&e.weak_password.reasons.length&&e.weak_password.message&&typeof e.weak_password.message=="string"&&e.weak_password.reasons.reduce((n,r)=>n&&typeof r=="string",!0)&&(t.data.weak_password=e.weak_password),t}function vn(e){var t;return{data:{user:(t=e.user)!==null&&t!==void 0?t:e},error:null}}function $D(e){return{data:e,error:null}}function FD(e){const{action_link:t,email_otp:n,hashed_token:r,redirect_to:s,verification_type:o}=e,i=RD(e,["action_link","email_otp","hashed_token","redirect_to","verification_type"]),a={action_link:t,email_otp:n,hashed_token:r,redirect_to:s,verification_type:o},l=Object.assign({},i);return{data:{properties:a,user:l},error:null}}function YD(e){return e}function UD(e){return e.access_token&&e.refresh_token&&e.expires_in}var WD=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var s=0,r=Object.getOwnPropertySymbols(e);s<r.length;s++)t.indexOf(r[s])<0&&Object.prototype.propertyIsEnumerable.call(e,r[s])&&(n[r[s]]=e[r[s]]);return n};class BD{constructor({url:t="",headers:n={},fetch:r}){this.url=t,this.headers=n,this.fetch=gy(r),this.mfa={listFactors:this._listFactors.bind(this),deleteFactor:this._deleteFactor.bind(this)}}async signOut(t,n="global"){try{return await z(this.fetch,"POST",`${this.url}/logout?scope=${n}`,{headers:this.headers,jwt:t,noResolveJson:!0}),{data:null,error:null}}catch(r){if(U(r))return{data:null,error:r};throw r}}async inviteUserByEmail(t,n={}){try{return await z(this.fetch,"POST",`${this.url}/invite`,{body:{email:t,data:n.data},headers:this.headers,redirectTo:n.redirectTo,xform:vn})}catch(r){if(U(r))return{data:{user:null},error:r};throw r}}async generateLink(t){try{const{options:n}=t,r=WD(t,["options"]),s=Object.assign(Object.assign({},r),n);return"newEmail"in r&&(s.new_email=r==null?void 0:r.newEmail,delete s.newEmail),await z(this.fetch,"POST",`${this.url}/admin/generate_link`,{body:s,headers:this.headers,xform:FD,redirectTo:n==null?void 0:n.redirectTo})}catch(n){if(U(n))return{data:{properties:null,user:null},error:n};throw n}}async createUser(t){try{return await z(this.fetch,"POST",`${this.url}/admin/users`,{body:t,headers:this.headers,xform:vn})}catch(n){if(U(n))return{data:{user:null},error:n};throw n}}async listUsers(t){var n,r,s,o,i,a,l;try{const u={nextPage:null,lastPage:0,total:0},c=await z(this.fetch,"GET",`${this.url}/admin/users`,{headers:this.headers,noResolveJson:!0,query:{page:(r=(n=t==null?void 0:t.page)===null||n===void 0?void 0:n.toString())!==null&&r!==void 0?r:"",per_page:(o=(s=t==null?void 0:t.perPage)===null||s===void 0?void 0:s.toString())!==null&&o!==void 0?o:""},xform:YD});if(c.error)throw c.error;const d=await c.json(),f=(i=c.headers.get("x-total-count"))!==null&&i!==void 0?i:0,p=(l=(a=c.headers.get("link"))===null||a===void 0?void 0:a.split(","))!==null&&l!==void 0?l:[];return p.length>0&&(p.forEach(y=>{const _=parseInt(y.split(";")[0].split("=")[1].substring(0,1)),x=JSON.parse(y.split(";")[1].split("=")[1]);u[`${x}Page`]=_}),u.total=parseInt(f)),{data:Object.assign(Object.assign({},d),u),error:null}}catch(u){if(U(u))return{data:{users:[]},error:u};throw u}}async getUserById(t){try{return await z(this.fetch,"GET",`${this.url}/admin/users/${t}`,{headers:this.headers,xform:vn})}catch(n){if(U(n))return{data:{user:null},error:n};throw n}}async updateUserById(t,n){try{return await z(this.fetch,"PUT",`${this.url}/admin/users/${t}`,{body:n,headers:this.headers,xform:vn})}catch(r){if(U(r))return{data:{user:null},error:r};throw r}}async deleteUser(t,n=!1){try{return await z(this.fetch,"DELETE",`${this.url}/admin/users/${t}`,{headers:this.headers,body:{should_soft_delete:n},xform:vn})}catch(r){if(U(r))return{data:{user:null},error:r};throw r}}async _listFactors(t){try{const{data:n,error:r}=await z(this.fetch,"GET",`${this.url}/admin/users/${t.userId}/factors`,{headers:this.headers,xform:s=>({data:{factors:s},error:null})});return{data:n,error:r}}catch(n){if(U(n))return{data:null,error:n};throw n}}async _deleteFactor(t){try{return{data:await z(this.fetch,"DELETE",`${this.url}/admin/users/${t.userId}/factors/${t.id}`,{headers:this.headers}),error:null}}catch(n){if(U(n))return{data:null,error:n};throw n}}}const zD={getItem:e=>Ks()?globalThis.localStorage.getItem(e):null,setItem:(e,t)=>{Ks()&&globalThis.localStorage.setItem(e,t)},removeItem:e=>{Ks()&&globalThis.localStorage.removeItem(e)}};function jh(e={}){return{getItem:t=>e[t]||null,setItem:(t,n)=>{e[t]=n},removeItem:t=>{delete e[t]}}}function HD(){if(typeof globalThis!="object")try{Object.defineProperty(Object.prototype,"__magic__",{get:function(){return this},configurable:!0}),__magic__.globalThis=__magic__,delete Object.prototype.__magic__}catch{typeof self<"u"&&(self.globalThis=self)}}const Cr={debug:!!(globalThis&&Ks()&&globalThis.localStorage&&globalThis.localStorage.getItem("supabase.gotrue-js.locks.debug")==="true")};class wy extends Error{constructor(t){super(t),this.isAcquireTimeout=!0}}class VD extends wy{}async function QD(e,t,n){Cr.debug&&console.log("@supabase/gotrue-js: navigatorLock: acquire lock",e,t);const r=new globalThis.AbortController;return t>0&&setTimeout(()=>{r.abort(),Cr.debug&&console.log("@supabase/gotrue-js: navigatorLock acquire timed out",e)},t),await Promise.resolve().then(()=>globalThis.navigator.locks.request(e,t===0?{mode:"exclusive",ifAvailable:!0}:{mode:"exclusive",signal:r.signal},async s=>{if(s){Cr.debug&&console.log("@supabase/gotrue-js: navigatorLock: acquired",e,s.name);try{return await n()}finally{Cr.debug&&console.log("@supabase/gotrue-js: navigatorLock: released",e,s.name)}}else{if(t===0)throw Cr.debug&&console.log("@supabase/gotrue-js: navigatorLock: not immediately available",e),new VD(`Acquiring an exclusive Navigator LockManager lock "${e}" immediately failed`);if(Cr.debug)try{const o=await globalThis.navigator.locks.query();console.log("@supabase/gotrue-js: Navigator LockManager state",JSON.stringify(o,null,"  "))}catch(o){console.warn("@supabase/gotrue-js: Error when querying Navigator LockManager state",o)}return console.warn("@supabase/gotrue-js: Navigator LockManager returned a null lock when using #request without ifAvailable set to true, it appears this browser is not following the LockManager spec https://developer.mozilla.org/en-US/docs/Web/API/LockManager/request"),await n()}}))}HD();const KD={url:hD,storageKey:pD,autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,headers:mD,flowType:"implicit",debug:!1,hasCustomAuthorizationHeader:!1},Ts=30*1e3,Rh=3;async function Ih(e,t,n){return await n()}class ko{constructor(t){var n,r;this.memoryStorage=null,this.stateChangeEmitters=new Map,this.autoRefreshTicker=null,this.visibilityChangedCallback=null,this.refreshingDeferred=null,this.initializePromise=null,this.detectSessionInUrl=!0,this.hasCustomAuthorizationHeader=!1,this.suppressGetSessionWarning=!1,this.lockAcquired=!1,this.pendingInLock=[],this.broadcastChannel=null,this.logger=console.log,this.instanceID=ko.nextInstanceID,ko.nextInstanceID+=1,this.instanceID>0&&Nt()&&console.warn("Multiple GoTrueClient instances detected in the same browser context. It is not an error, but this should be avoided as it may produce undefined behavior when used concurrently under the same storage key.");const s=Object.assign(Object.assign({},KD),t);if(this.logDebugMessages=!!s.debug,typeof s.debug=="function"&&(this.logger=s.debug),this.persistSession=s.persistSession,this.storageKey=s.storageKey,this.autoRefreshToken=s.autoRefreshToken,this.admin=new BD({url:s.url,headers:s.headers,fetch:s.fetch}),this.url=s.url,this.headers=s.headers,this.fetch=gy(s.fetch),this.lock=s.lock||Ih,this.detectSessionInUrl=s.detectSessionInUrl,this.flowType=s.flowType,this.hasCustomAuthorizationHeader=s.hasCustomAuthorizationHeader,s.lock?this.lock=s.lock:Nt()&&(!((n=globalThis==null?void 0:globalThis.navigator)===null||n===void 0)&&n.locks)?this.lock=QD:this.lock=Ih,this.mfa={verify:this._verify.bind(this),enroll:this._enroll.bind(this),unenroll:this._unenroll.bind(this),challenge:this._challenge.bind(this),listFactors:this._listFactors.bind(this),challengeAndVerify:this._challengeAndVerify.bind(this),getAuthenticatorAssuranceLevel:this._getAuthenticatorAssuranceLevel.bind(this)},this.persistSession?s.storage?this.storage=s.storage:Ks()?this.storage=zD:(this.memoryStorage={},this.storage=jh(this.memoryStorage)):(this.memoryStorage={},this.storage=jh(this.memoryStorage)),Nt()&&globalThis.BroadcastChannel&&this.persistSession&&this.storageKey){try{this.broadcastChannel=new globalThis.BroadcastChannel(this.storageKey)}catch(o){console.error("Failed to create a new BroadcastChannel, multi-tab state changes will not be available",o)}(r=this.broadcastChannel)===null||r===void 0||r.addEventListener("message",async o=>{this._debug("received broadcast notification from other tab or client",o),await this._notifyAllSubscribers(o.data.event,o.data.session,!1)})}this.initialize()}_debug(...t){return this.logDebugMessages&&this.logger(`GoTrueClient@${this.instanceID} (${py}) ${new Date().toISOString()}`,...t),this}async initialize(){return this.initializePromise?await this.initializePromise:(this.initializePromise=(async()=>await this._acquireLock(-1,async()=>await this._initialize()))(),await this.initializePromise)}async _initialize(){var t;try{const n=vD(window.location.href);let r="none";if(this._isImplicitGrantCallback(n)?r="implicit":await this._isPKCECallback(n)&&(r="pkce"),Nt()&&this.detectSessionInUrl&&r!=="none"){const{data:s,error:o}=await this._getSessionFromURL(n,r);if(o){if(this._debug("#_initialize()","error detecting session from URL",o),jD(o)){const l=(t=o.details)===null||t===void 0?void 0:t.code;if(l==="identity_already_exists"||l==="identity_not_found"||l==="single_identity_not_deletable")return{error:o}}return await this._removeSession(),{error:o}}const{session:i,redirectType:a}=s;return this._debug("#_initialize()","detected session in URL",i,"redirect type",a),await this._saveSession(i),setTimeout(async()=>{a==="recovery"?await this._notifyAllSubscribers("PASSWORD_RECOVERY",i):await this._notifyAllSubscribers("SIGNED_IN",i)},0),{error:null}}return await this._recoverAndRefresh(),{error:null}}catch(n){return U(n)?{error:n}:{error:new vy("Unexpected error during initialization",n)}}finally{await this._handleVisibilityChange(),this._debug("#_initialize()","end")}}async signInAnonymously(t){var n,r,s;try{const o=await z(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,body:{data:(r=(n=t==null?void 0:t.options)===null||n===void 0?void 0:n.data)!==null&&r!==void 0?r:{},gotrue_meta_security:{captcha_token:(s=t==null?void 0:t.options)===null||s===void 0?void 0:s.captchaToken}},xform:dn}),{data:i,error:a}=o;if(a||!i)return{data:{user:null,session:null},error:a};const l=i.session,u=i.user;return i.session&&(await this._saveSession(i.session),await this._notifyAllSubscribers("SIGNED_IN",l)),{data:{user:u,session:l},error:null}}catch(o){if(U(o))return{data:{user:null,session:null},error:o};throw o}}async signUp(t){var n,r,s;try{let o;if("email"in t){const{email:c,password:d,options:f}=t;let p=null,y=null;this.flowType==="pkce"&&([p,y]=await Dr(this.storage,this.storageKey)),o=await z(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,redirectTo:f==null?void 0:f.emailRedirectTo,body:{email:c,password:d,data:(n=f==null?void 0:f.data)!==null&&n!==void 0?n:{},gotrue_meta_security:{captcha_token:f==null?void 0:f.captchaToken},code_challenge:p,code_challenge_method:y},xform:dn})}else if("phone"in t){const{phone:c,password:d,options:f}=t;o=await z(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,body:{phone:c,password:d,data:(r=f==null?void 0:f.data)!==null&&r!==void 0?r:{},channel:(s=f==null?void 0:f.channel)!==null&&s!==void 0?s:"sms",gotrue_meta_security:{captcha_token:f==null?void 0:f.captchaToken}},xform:dn})}else throw new li("You must provide either an email or phone number and a password");const{data:i,error:a}=o;if(a||!i)return{data:{user:null,session:null},error:a};const l=i.session,u=i.user;return i.session&&(await this._saveSession(i.session),await this._notifyAllSubscribers("SIGNED_IN",l)),{data:{user:u,session:l},error:null}}catch(o){if(U(o))return{data:{user:null,session:null},error:o};throw o}}async signInWithPassword(t){try{let n;if("email"in t){const{email:o,password:i,options:a}=t;n=await z(this.fetch,"POST",`${this.url}/token?grant_type=password`,{headers:this.headers,body:{email:o,password:i,gotrue_meta_security:{captcha_token:a==null?void 0:a.captchaToken}},xform:Mh})}else if("phone"in t){const{phone:o,password:i,options:a}=t;n=await z(this.fetch,"POST",`${this.url}/token?grant_type=password`,{headers:this.headers,body:{phone:o,password:i,gotrue_meta_security:{captcha_token:a==null?void 0:a.captchaToken}},xform:Mh})}else throw new li("You must provide either an email or phone number and a password");const{data:r,error:s}=n;return s?{data:{user:null,session:null},error:s}:!r||!r.session||!r.user?{data:{user:null,session:null},error:new Yl}:(r.session&&(await this._saveSession(r.session),await this._notifyAllSubscribers("SIGNED_IN",r.session)),{data:Object.assign({user:r.user,session:r.session},r.weak_password?{weakPassword:r.weak_password}:null),error:s})}catch(n){if(U(n))return{data:{user:null,session:null},error:n};throw n}}async signInWithOAuth(t){var n,r,s,o;return await this._handleProviderSignIn(t.provider,{redirectTo:(n=t.options)===null||n===void 0?void 0:n.redirectTo,scopes:(r=t.options)===null||r===void 0?void 0:r.scopes,queryParams:(s=t.options)===null||s===void 0?void 0:s.queryParams,skipBrowserRedirect:(o=t.options)===null||o===void 0?void 0:o.skipBrowserRedirect})}async exchangeCodeForSession(t){return await this.initializePromise,this._acquireLock(-1,async()=>this._exchangeCodeForSession(t))}async _exchangeCodeForSession(t){const n=await ii(this.storage,`${this.storageKey}-code-verifier`),[r,s]=(n??"").split("/");try{const{data:o,error:i}=await z(this.fetch,"POST",`${this.url}/token?grant_type=pkce`,{headers:this.headers,body:{auth_code:t,code_verifier:r},xform:dn});if(await ai(this.storage,`${this.storageKey}-code-verifier`),i)throw i;return!o||!o.session||!o.user?{data:{user:null,session:null,redirectType:null},error:new Yl}:(o.session&&(await this._saveSession(o.session),await this._notifyAllSubscribers("SIGNED_IN",o.session)),{data:Object.assign(Object.assign({},o),{redirectType:s??null}),error:i})}catch(o){if(U(o))return{data:{user:null,session:null,redirectType:null},error:o};throw o}}async signInWithIdToken(t){try{const{options:n,provider:r,token:s,access_token:o,nonce:i}=t,a=await z(this.fetch,"POST",`${this.url}/token?grant_type=id_token`,{headers:this.headers,body:{provider:r,id_token:s,access_token:o,nonce:i,gotrue_meta_security:{captcha_token:n==null?void 0:n.captchaToken}},xform:dn}),{data:l,error:u}=a;return u?{data:{user:null,session:null},error:u}:!l||!l.session||!l.user?{data:{user:null,session:null},error:new Yl}:(l.session&&(await this._saveSession(l.session),await this._notifyAllSubscribers("SIGNED_IN",l.session)),{data:l,error:u})}catch(n){if(U(n))return{data:{user:null,session:null},error:n};throw n}}async signInWithOtp(t){var n,r,s,o,i;try{if("email"in t){const{email:a,options:l}=t;let u=null,c=null;this.flowType==="pkce"&&([u,c]=await Dr(this.storage,this.storageKey));const{error:d}=await z(this.fetch,"POST",`${this.url}/otp`,{headers:this.headers,body:{email:a,data:(n=l==null?void 0:l.data)!==null&&n!==void 0?n:{},create_user:(r=l==null?void 0:l.shouldCreateUser)!==null&&r!==void 0?r:!0,gotrue_meta_security:{captcha_token:l==null?void 0:l.captchaToken},code_challenge:u,code_challenge_method:c},redirectTo:l==null?void 0:l.emailRedirectTo});return{data:{user:null,session:null},error:d}}if("phone"in t){const{phone:a,options:l}=t,{data:u,error:c}=await z(this.fetch,"POST",`${this.url}/otp`,{headers:this.headers,body:{phone:a,data:(s=l==null?void 0:l.data)!==null&&s!==void 0?s:{},create_user:(o=l==null?void 0:l.shouldCreateUser)!==null&&o!==void 0?o:!0,gotrue_meta_security:{captcha_token:l==null?void 0:l.captchaToken},channel:(i=l==null?void 0:l.channel)!==null&&i!==void 0?i:"sms"}});return{data:{user:null,session:null,messageId:u==null?void 0:u.message_id},error:c}}throw new li("You must provide either an email or phone number.")}catch(a){if(U(a))return{data:{user:null,session:null},error:a};throw a}}async verifyOtp(t){var n,r;try{let s,o;"options"in t&&(s=(n=t.options)===null||n===void 0?void 0:n.redirectTo,o=(r=t.options)===null||r===void 0?void 0:r.captchaToken);const{data:i,error:a}=await z(this.fetch,"POST",`${this.url}/verify`,{headers:this.headers,body:Object.assign(Object.assign({},t),{gotrue_meta_security:{captcha_token:o}}),redirectTo:s,xform:dn});if(a)throw a;if(!i)throw new Error("An error occurred on token verification.");const l=i.session,u=i.user;return l!=null&&l.access_token&&(await this._saveSession(l),await this._notifyAllSubscribers(t.type=="recovery"?"PASSWORD_RECOVERY":"SIGNED_IN",l)),{data:{user:u,session:l},error:null}}catch(s){if(U(s))return{data:{user:null,session:null},error:s};throw s}}async signInWithSSO(t){var n,r,s;try{let o=null,i=null;return this.flowType==="pkce"&&([o,i]=await Dr(this.storage,this.storageKey)),await z(this.fetch,"POST",`${this.url}/sso`,{body:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},"providerId"in t?{provider_id:t.providerId}:null),"domain"in t?{domain:t.domain}:null),{redirect_to:(r=(n=t.options)===null||n===void 0?void 0:n.redirectTo)!==null&&r!==void 0?r:void 0}),!((s=t==null?void 0:t.options)===null||s===void 0)&&s.captchaToken?{gotrue_meta_security:{captcha_token:t.options.captchaToken}}:null),{skip_http_redirect:!0,code_challenge:o,code_challenge_method:i}),headers:this.headers,xform:$D})}catch(o){if(U(o))return{data:null,error:o};throw o}}async reauthenticate(){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._reauthenticate())}async _reauthenticate(){try{return await this._useSession(async t=>{const{data:{session:n},error:r}=t;if(r)throw r;if(!n)throw new cn;const{error:s}=await z(this.fetch,"GET",`${this.url}/reauthenticate`,{headers:this.headers,jwt:n.access_token});return{data:{user:null,session:null},error:s}})}catch(t){if(U(t))return{data:{user:null,session:null},error:t};throw t}}async resend(t){try{const n=`${this.url}/resend`;if("email"in t){const{email:r,type:s,options:o}=t,{error:i}=await z(this.fetch,"POST",n,{headers:this.headers,body:{email:r,type:s,gotrue_meta_security:{captcha_token:o==null?void 0:o.captchaToken}},redirectTo:o==null?void 0:o.emailRedirectTo});return{data:{user:null,session:null},error:i}}else if("phone"in t){const{phone:r,type:s,options:o}=t,{data:i,error:a}=await z(this.fetch,"POST",n,{headers:this.headers,body:{phone:r,type:s,gotrue_meta_security:{captcha_token:o==null?void 0:o.captchaToken}}});return{data:{user:null,session:null,messageId:i==null?void 0:i.message_id},error:a}}throw new li("You must provide either an email or phone number and a type")}catch(n){if(U(n))return{data:{user:null,session:null},error:n};throw n}}async getSession(){return await this.initializePromise,await this._acquireLock(-1,async()=>this._useSession(async n=>n))}async _acquireLock(t,n){this._debug("#_acquireLock","begin",t);try{if(this.lockAcquired){const r=this.pendingInLock.length?this.pendingInLock[this.pendingInLock.length-1]:Promise.resolve(),s=(async()=>(await r,await n()))();return this.pendingInLock.push((async()=>{try{await s}catch{}})()),s}return await this.lock(`lock:${this.storageKey}`,t,async()=>{this._debug("#_acquireLock","lock acquired for storage key",this.storageKey);try{this.lockAcquired=!0;const r=n();for(this.pendingInLock.push((async()=>{try{await r}catch{}})()),await r;this.pendingInLock.length;){const s=[...this.pendingInLock];await Promise.all(s),this.pendingInLock.splice(0,s.length)}return await r}finally{this._debug("#_acquireLock","lock released for storage key",this.storageKey),this.lockAcquired=!1}})}finally{this._debug("#_acquireLock","end")}}async _useSession(t){this._debug("#_useSession","begin");try{const n=await this.__loadSession();return await t(n)}finally{this._debug("#_useSession","end")}}async __loadSession(){this._debug("#__loadSession()","begin"),this.lockAcquired||this._debug("#__loadSession()","used outside of an acquired lock!",new Error().stack);try{let t=null;const n=await ii(this.storage,this.storageKey);if(this._debug("#getSession()","session from storage",n),n!==null&&(this._isValidSession(n)?t=n:(this._debug("#getSession()","session from storage is not valid"),await this._removeSession())),!t)return{data:{session:null},error:null};const r=t.expires_at?t.expires_at<=Date.now()/1e3:!1;if(this._debug("#__loadSession()",`session has${r?"":" not"} expired`,"expires_at",t.expires_at),!r){if(this.storage.isServer){let i=this.suppressGetSessionWarning;t=new Proxy(t,{get:(l,u,c)=>(!i&&u==="user"&&(console.warn("Using the user object as returned from supabase.auth.getSession() or from some supabase.auth.onAuthStateChange() events could be insecure! This value comes directly from the storage medium (usually cookies on the server) and may not be authentic. Use supabase.auth.getUser() instead which authenticates the data by contacting the Supabase Auth server."),i=!0,this.suppressGetSessionWarning=!0),Reflect.get(l,u,c))})}return{data:{session:t},error:null}}const{session:s,error:o}=await this._callRefreshToken(t.refresh_token);return o?{data:{session:null},error:o}:{data:{session:s},error:null}}finally{this._debug("#__loadSession()","end")}}async getUser(t){return t?await this._getUser(t):(await this.initializePromise,await this._acquireLock(-1,async()=>await this._getUser()))}async _getUser(t){try{return t?await z(this.fetch,"GET",`${this.url}/user`,{headers:this.headers,jwt:t,xform:vn}):await this._useSession(async n=>{var r,s,o;const{data:i,error:a}=n;if(a)throw a;return!(!((r=i.session)===null||r===void 0)&&r.access_token)&&!this.hasCustomAuthorizationHeader?{data:{user:null},error:new cn}:await z(this.fetch,"GET",`${this.url}/user`,{headers:this.headers,jwt:(o=(s=i.session)===null||s===void 0?void 0:s.access_token)!==null&&o!==void 0?o:void 0,xform:vn})})}catch(n){if(U(n))return MD(n)&&(await this._removeSession(),await ai(this.storage,`${this.storageKey}-code-verifier`)),{data:{user:null},error:n};throw n}}async updateUser(t,n={}){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._updateUser(t,n))}async _updateUser(t,n={}){try{return await this._useSession(async r=>{const{data:s,error:o}=r;if(o)throw o;if(!s.session)throw new cn;const i=s.session;let a=null,l=null;this.flowType==="pkce"&&t.email!=null&&([a,l]=await Dr(this.storage,this.storageKey));const{data:u,error:c}=await z(this.fetch,"PUT",`${this.url}/user`,{headers:this.headers,redirectTo:n==null?void 0:n.emailRedirectTo,body:Object.assign(Object.assign({},t),{code_challenge:a,code_challenge_method:l}),jwt:i.access_token,xform:vn});if(c)throw c;return i.user=u.user,await this._saveSession(i),await this._notifyAllSubscribers("USER_UPDATED",i),{data:{user:i.user},error:null}})}catch(r){if(U(r))return{data:{user:null},error:r};throw r}}_decodeJWT(t){return Ph(t)}async setSession(t){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._setSession(t))}async _setSession(t){try{if(!t.access_token||!t.refresh_token)throw new cn;const n=Date.now()/1e3;let r=n,s=!0,o=null;const i=Ph(t.access_token);if(i.exp&&(r=i.exp,s=r<=n),s){const{session:a,error:l}=await this._callRefreshToken(t.refresh_token);if(l)return{data:{user:null,session:null},error:l};if(!a)return{data:{user:null,session:null},error:null};o=a}else{const{data:a,error:l}=await this._getUser(t.access_token);if(l)throw l;o={access_token:t.access_token,refresh_token:t.refresh_token,user:a.user,token_type:"bearer",expires_in:r-n,expires_at:r},await this._saveSession(o),await this._notifyAllSubscribers("SIGNED_IN",o)}return{data:{user:o.user,session:o},error:null}}catch(n){if(U(n))return{data:{session:null,user:null},error:n};throw n}}async refreshSession(t){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._refreshSession(t))}async _refreshSession(t){try{return await this._useSession(async n=>{var r;if(!t){const{data:i,error:a}=n;if(a)throw a;t=(r=i.session)!==null&&r!==void 0?r:void 0}if(!(t!=null&&t.refresh_token))throw new cn;const{session:s,error:o}=await this._callRefreshToken(t.refresh_token);return o?{data:{user:null,session:null},error:o}:s?{data:{user:s.user,session:s},error:null}:{data:{user:null,session:null},error:null}})}catch(n){if(U(n))return{data:{user:null,session:null},error:n};throw n}}async _getSessionFromURL(t,n){try{if(!Nt())throw new ui("No browser detected.");if(t.error||t.error_description||t.error_code)throw new ui(t.error_description||"Error in URL with unspecified error_description",{error:t.error||"unspecified_error",code:t.error_code||"unspecified_code"});switch(n){case"implicit":if(this.flowType==="pkce")throw new Nh("Not a valid PKCE flow url.");break;case"pkce":if(this.flowType==="implicit")throw new ui("Not a valid implicit grant flow url.");break;default:}if(n==="pkce"){if(this._debug("#_initialize()","begin","is PKCE flow",!0),!t.code)throw new Nh("No code detected.");const{data:m,error:v}=await this._exchangeCodeForSession(t.code);if(v)throw v;const k=new URL(window.location.href);return k.searchParams.delete("code"),window.history.replaceState(window.history.state,"",k.toString()),{data:{session:m.session,redirectType:null},error:null}}const{provider_token:r,provider_refresh_token:s,access_token:o,refresh_token:i,expires_in:a,expires_at:l,token_type:u}=t;if(!o||!a||!i||!u)throw new ui("No session defined in URL");const c=Math.round(Date.now()/1e3),d=parseInt(a);let f=c+d;l&&(f=parseInt(l));const p=f-c;p*1e3<=Ts&&console.warn(`@supabase/gotrue-js: Session as retrieved from URL expires in ${p}s, should have been closer to ${d}s`);const y=f-d;c-y>=120?console.warn("@supabase/gotrue-js: Session as retrieved from URL was issued over 120s ago, URL could be stale",y,f,c):c-y<0&&console.warn("@supabase/gotrue-js: Session as retrieved from URL was issued in the future? Check the device clock for skew",y,f,c);const{data:_,error:x}=await this._getUser(o);if(x)throw x;const g={provider_token:r,provider_refresh_token:s,access_token:o,expires_in:d,expires_at:f,refresh_token:i,token_type:u,user:_.user};return window.location.hash="",this._debug("#_getSessionFromURL()","clearing window.location.hash"),{data:{session:g,redirectType:t.type},error:null}}catch(r){if(U(r))return{data:{session:null,redirectType:null},error:r};throw r}}_isImplicitGrantCallback(t){return!!(t.access_token||t.error_description)}async _isPKCECallback(t){const n=await ii(this.storage,`${this.storageKey}-code-verifier`);return!!(t.code&&n)}async signOut(t={scope:"global"}){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._signOut(t))}async _signOut({scope:t}={scope:"global"}){return await this._useSession(async n=>{var r;const{data:s,error:o}=n;if(o)return{error:o};const i=(r=s.session)===null||r===void 0?void 0:r.access_token;if(i){const{error:a}=await this.admin.signOut(i,t);if(a&&!(OD(a)&&(a.status===404||a.status===401||a.status===403)))return{error:a}}return t!=="others"&&(await this._removeSession(),await ai(this.storage,`${this.storageKey}-code-verifier`)),{error:null}})}onAuthStateChange(t){const n=yD(),r={id:n,callback:t,unsubscribe:()=>{this._debug("#unsubscribe()","state change callback with id removed",n),this.stateChangeEmitters.delete(n)}};return this._debug("#onAuthStateChange()","registered callback with id",n),this.stateChangeEmitters.set(n,r),(async()=>(await this.initializePromise,await this._acquireLock(-1,async()=>{this._emitInitialSession(n)})))(),{data:{subscription:r}}}async _emitInitialSession(t){return await this._useSession(async n=>{var r,s;try{const{data:{session:o},error:i}=n;if(i)throw i;await((r=this.stateChangeEmitters.get(t))===null||r===void 0?void 0:r.callback("INITIAL_SESSION",o)),this._debug("INITIAL_SESSION","callback id",t,"session",o)}catch(o){await((s=this.stateChangeEmitters.get(t))===null||s===void 0?void 0:s.callback("INITIAL_SESSION",null)),this._debug("INITIAL_SESSION","callback id",t,"error",o),console.error(o)}})}async resetPasswordForEmail(t,n={}){let r=null,s=null;this.flowType==="pkce"&&([r,s]=await Dr(this.storage,this.storageKey,!0));try{return await z(this.fetch,"POST",`${this.url}/recover`,{body:{email:t,code_challenge:r,code_challenge_method:s,gotrue_meta_security:{captcha_token:n.captchaToken}},headers:this.headers,redirectTo:n.redirectTo})}catch(o){if(U(o))return{data:null,error:o};throw o}}async getUserIdentities(){var t;try{const{data:n,error:r}=await this.getUser();if(r)throw r;return{data:{identities:(t=n.user.identities)!==null&&t!==void 0?t:[]},error:null}}catch(n){if(U(n))return{data:null,error:n};throw n}}async linkIdentity(t){var n;try{const{data:r,error:s}=await this._useSession(async o=>{var i,a,l,u,c;const{data:d,error:f}=o;if(f)throw f;const p=await this._getUrlForProvider(`${this.url}/user/identities/authorize`,t.provider,{redirectTo:(i=t.options)===null||i===void 0?void 0:i.redirectTo,scopes:(a=t.options)===null||a===void 0?void 0:a.scopes,queryParams:(l=t.options)===null||l===void 0?void 0:l.queryParams,skipBrowserRedirect:!0});return await z(this.fetch,"GET",p,{headers:this.headers,jwt:(c=(u=d.session)===null||u===void 0?void 0:u.access_token)!==null&&c!==void 0?c:void 0})});if(s)throw s;return Nt()&&!(!((n=t.options)===null||n===void 0)&&n.skipBrowserRedirect)&&window.location.assign(r==null?void 0:r.url),{data:{provider:t.provider,url:r==null?void 0:r.url},error:null}}catch(r){if(U(r))return{data:{provider:t.provider,url:null},error:r};throw r}}async unlinkIdentity(t){try{return await this._useSession(async n=>{var r,s;const{data:o,error:i}=n;if(i)throw i;return await z(this.fetch,"DELETE",`${this.url}/user/identities/${t.identity_id}`,{headers:this.headers,jwt:(s=(r=o.session)===null||r===void 0?void 0:r.access_token)!==null&&s!==void 0?s:void 0})})}catch(n){if(U(n))return{data:null,error:n};throw n}}async _refreshAccessToken(t){const n=`#_refreshAccessToken(${t.substring(0,5)}...)`;this._debug(n,"begin");try{const r=Date.now();return await kD(async s=>(s>0&&await xD(200*Math.pow(2,s-1)),this._debug(n,"refreshing attempt",s),await z(this.fetch,"POST",`${this.url}/token?grant_type=refresh_token`,{body:{refresh_token:t},headers:this.headers,xform:dn})),(s,o)=>{const i=200*Math.pow(2,s);return o&&Ul(o)&&Date.now()+i-r<Ts})}catch(r){if(this._debug(n,"error",r),U(r))return{data:{session:null,user:null},error:r};throw r}finally{this._debug(n,"end")}}_isValidSession(t){return typeof t=="object"&&t!==null&&"access_token"in t&&"refresh_token"in t&&"expires_at"in t}async _handleProviderSignIn(t,n){const r=await this._getUrlForProvider(`${this.url}/authorize`,t,{redirectTo:n.redirectTo,scopes:n.scopes,queryParams:n.queryParams});return this._debug("#_handleProviderSignIn()","provider",t,"options",n,"url",r),Nt()&&!n.skipBrowserRedirect&&window.location.assign(r),{data:{provider:t,url:r},error:null}}async _recoverAndRefresh(){var t;const n="#_recoverAndRefresh()";this._debug(n,"begin");try{const r=await ii(this.storage,this.storageKey);if(this._debug(n,"session from storage",r),!this._isValidSession(r)){this._debug(n,"session is not valid"),r!==null&&await this._removeSession();return}const s=Math.round(Date.now()/1e3),o=((t=r.expires_at)!==null&&t!==void 0?t:1/0)<s+Eh;if(this._debug(n,`session has${o?"":" not"} expired with margin of ${Eh}s`),o){if(this.autoRefreshToken&&r.refresh_token){const{error:i}=await this._callRefreshToken(r.refresh_token);i&&(console.error(i),Ul(i)||(this._debug(n,"refresh failed with a non-retryable error, removing the session",i),await this._removeSession()))}}else await this._notifyAllSubscribers("SIGNED_IN",r)}catch(r){this._debug(n,"error",r),console.error(r);return}finally{this._debug(n,"end")}}async _callRefreshToken(t){var n,r;if(!t)throw new cn;if(this.refreshingDeferred)return this.refreshingDeferred.promise;const s=`#_callRefreshToken(${t.substring(0,5)}...)`;this._debug(s,"begin");try{this.refreshingDeferred=new Xa;const{data:o,error:i}=await this._refreshAccessToken(t);if(i)throw i;if(!o.session)throw new cn;await this._saveSession(o.session),await this._notifyAllSubscribers("TOKEN_REFRESHED",o.session);const a={session:o.session,error:null};return this.refreshingDeferred.resolve(a),a}catch(o){if(this._debug(s,"error",o),U(o)){const i={session:null,error:o};return Ul(o)||await this._removeSession(),(n=this.refreshingDeferred)===null||n===void 0||n.resolve(i),i}throw(r=this.refreshingDeferred)===null||r===void 0||r.reject(o),o}finally{this.refreshingDeferred=null,this._debug(s,"end")}}async _notifyAllSubscribers(t,n,r=!0){const s=`#_notifyAllSubscribers(${t})`;this._debug(s,"begin",n,`broadcast = ${r}`);try{this.broadcastChannel&&r&&this.broadcastChannel.postMessage({event:t,session:n});const o=[],i=Array.from(this.stateChangeEmitters.values()).map(async a=>{try{await a.callback(t,n)}catch(l){o.push(l)}});if(await Promise.all(i),o.length>0){for(let a=0;a<o.length;a+=1)console.error(o[a]);throw o[0]}}finally{this._debug(s,"end")}}async _saveSession(t){this._debug("#_saveSession()",t),this.suppressGetSessionWarning=!0,await yy(this.storage,this.storageKey,t)}async _removeSession(){this._debug("#_removeSession()"),await ai(this.storage,this.storageKey),await this._notifyAllSubscribers("SIGNED_OUT",null)}_removeVisibilityChangedCallback(){this._debug("#_removeVisibilityChangedCallback()");const t=this.visibilityChangedCallback;this.visibilityChangedCallback=null;try{t&&Nt()&&(window!=null&&window.removeEventListener)&&window.removeEventListener("visibilitychange",t)}catch(n){console.error("removing visibilitychange callback failed",n)}}async _startAutoRefresh(){await this._stopAutoRefresh(),this._debug("#_startAutoRefresh()");const t=setInterval(()=>this._autoRefreshTokenTick(),Ts);this.autoRefreshTicker=t,t&&typeof t=="object"&&typeof t.unref=="function"?t.unref():typeof Deno<"u"&&typeof Deno.unrefTimer=="function"&&Deno.unrefTimer(t),setTimeout(async()=>{await this.initializePromise,await this._autoRefreshTokenTick()},0)}async _stopAutoRefresh(){this._debug("#_stopAutoRefresh()");const t=this.autoRefreshTicker;this.autoRefreshTicker=null,t&&clearInterval(t)}async startAutoRefresh(){this._removeVisibilityChangedCallback(),await this._startAutoRefresh()}async stopAutoRefresh(){this._removeVisibilityChangedCallback(),await this._stopAutoRefresh()}async _autoRefreshTokenTick(){this._debug("#_autoRefreshTokenTick()","begin");try{await this._acquireLock(0,async()=>{try{const t=Date.now();try{return await this._useSession(async n=>{const{data:{session:r}}=n;if(!r||!r.refresh_token||!r.expires_at){this._debug("#_autoRefreshTokenTick()","no session");return}const s=Math.floor((r.expires_at*1e3-t)/Ts);this._debug("#_autoRefreshTokenTick()",`access token expires in ${s} ticks, a tick lasts ${Ts}ms, refresh threshold is ${Rh} ticks`),s<=Rh&&await this._callRefreshToken(r.refresh_token)})}catch(n){console.error("Auto refresh tick failed with error. This is likely a transient error.",n)}}finally{this._debug("#_autoRefreshTokenTick()","end")}})}catch(t){if(t.isAcquireTimeout||t instanceof wy)this._debug("auto refresh token tick lock not available");else throw t}}async _handleVisibilityChange(){if(this._debug("#_handleVisibilityChange()"),!Nt()||!(window!=null&&window.addEventListener))return this.autoRefreshToken&&this.startAutoRefresh(),!1;try{this.visibilityChangedCallback=async()=>await this._onVisibilityChanged(!1),window==null||window.addEventListener("visibilitychange",this.visibilityChangedCallback),await this._onVisibilityChanged(!0)}catch(t){console.error("_handleVisibilityChange",t)}}async _onVisibilityChanged(t){const n=`#_onVisibilityChanged(${t})`;this._debug(n,"visibilityState",document.visibilityState),document.visibilityState==="visible"?(this.autoRefreshToken&&this._startAutoRefresh(),t||(await this.initializePromise,await this._acquireLock(-1,async()=>{if(document.visibilityState!=="visible"){this._debug(n,"acquired the lock to recover the session, but the browser visibilityState is no longer visible, aborting");return}await this._recoverAndRefresh()}))):document.visibilityState==="hidden"&&this.autoRefreshToken&&this._stopAutoRefresh()}async _getUrlForProvider(t,n,r){const s=[`provider=${encodeURIComponent(n)}`];if(r!=null&&r.redirectTo&&s.push(`redirect_to=${encodeURIComponent(r.redirectTo)}`),r!=null&&r.scopes&&s.push(`scopes=${encodeURIComponent(r.scopes)}`),this.flowType==="pkce"){const[o,i]=await Dr(this.storage,this.storageKey),a=new URLSearchParams({code_challenge:`${encodeURIComponent(o)}`,code_challenge_method:`${encodeURIComponent(i)}`});s.push(a.toString())}if(r!=null&&r.queryParams){const o=new URLSearchParams(r.queryParams);s.push(o.toString())}return r!=null&&r.skipBrowserRedirect&&s.push(`skip_http_redirect=${r.skipBrowserRedirect}`),`${t}?${s.join("&")}`}async _unenroll(t){try{return await this._useSession(async n=>{var r;const{data:s,error:o}=n;return o?{data:null,error:o}:await z(this.fetch,"DELETE",`${this.url}/factors/${t.factorId}`,{headers:this.headers,jwt:(r=s==null?void 0:s.session)===null||r===void 0?void 0:r.access_token})})}catch(n){if(U(n))return{data:null,error:n};throw n}}async _enroll(t){try{return await this._useSession(async n=>{var r,s;const{data:o,error:i}=n;if(i)return{data:null,error:i};const a=Object.assign({friendly_name:t.friendlyName,factor_type:t.factorType},t.factorType==="phone"?{phone:t.phone}:{issuer:t.issuer}),{data:l,error:u}=await z(this.fetch,"POST",`${this.url}/factors`,{body:a,headers:this.headers,jwt:(r=o==null?void 0:o.session)===null||r===void 0?void 0:r.access_token});return u?{data:null,error:u}:(t.factorType==="totp"&&(!((s=l==null?void 0:l.totp)===null||s===void 0)&&s.qr_code)&&(l.totp.qr_code=`data:image/svg+xml;utf-8,${l.totp.qr_code}`),{data:l,error:null})})}catch(n){if(U(n))return{data:null,error:n};throw n}}async _verify(t){return this._acquireLock(-1,async()=>{try{return await this._useSession(async n=>{var r;const{data:s,error:o}=n;if(o)return{data:null,error:o};const{data:i,error:a}=await z(this.fetch,"POST",`${this.url}/factors/${t.factorId}/verify`,{body:{code:t.code,challenge_id:t.challengeId},headers:this.headers,jwt:(r=s==null?void 0:s.session)===null||r===void 0?void 0:r.access_token});return a?{data:null,error:a}:(await this._saveSession(Object.assign({expires_at:Math.round(Date.now()/1e3)+i.expires_in},i)),await this._notifyAllSubscribers("MFA_CHALLENGE_VERIFIED",i),{data:i,error:a})})}catch(n){if(U(n))return{data:null,error:n};throw n}})}async _challenge(t){return this._acquireLock(-1,async()=>{try{return await this._useSession(async n=>{var r;const{data:s,error:o}=n;return o?{data:null,error:o}:await z(this.fetch,"POST",`${this.url}/factors/${t.factorId}/challenge`,{body:{channel:t.channel},headers:this.headers,jwt:(r=s==null?void 0:s.session)===null||r===void 0?void 0:r.access_token})})}catch(n){if(U(n))return{data:null,error:n};throw n}})}async _challengeAndVerify(t){const{data:n,error:r}=await this._challenge({factorId:t.factorId});return r?{data:null,error:r}:await this._verify({factorId:t.factorId,challengeId:n.id,code:t.code})}async _listFactors(){const{data:{user:t},error:n}=await this.getUser();if(n)return{data:null,error:n};const r=(t==null?void 0:t.factors)||[],s=r.filter(i=>i.factor_type==="totp"&&i.status==="verified"),o=r.filter(i=>i.factor_type==="phone"&&i.status==="verified");return{data:{all:r,totp:s,phone:o},error:null}}async _getAuthenticatorAssuranceLevel(){return this._acquireLock(-1,async()=>await this._useSession(async t=>{var n,r;const{data:{session:s},error:o}=t;if(o)return{data:null,error:o};if(!s)return{data:{currentLevel:null,nextLevel:null,currentAuthenticationMethods:[]},error:null};const i=this._decodeJWT(s.access_token);let a=null;i.aal&&(a=i.aal);let l=a;((r=(n=s.user.factors)===null||n===void 0?void 0:n.filter(d=>d.status==="verified"))!==null&&r!==void 0?r:[]).length>0&&(l="aal2");const c=i.amr||[];return{data:{currentLevel:a,nextLevel:l,currentAuthenticationMethods:c},error:null}}))}}ko.nextInstanceID=0;const qD=ko;class GD extends qD{constructor(t){super(t)}}var JD=function(e,t,n,r){function s(o){return o instanceof n?o:new n(function(i){i(o)})}return new(n||(n=Promise))(function(o,i){function a(c){try{u(r.next(c))}catch(d){i(d)}}function l(c){try{u(r.throw(c))}catch(d){i(d)}}function u(c){c.done?o(c.value):s(c.value).then(a,l)}u((r=r.apply(e,t||[])).next())})};class XD{constructor(t,n,r){var s,o,i;if(this.supabaseUrl=t,this.supabaseKey=n,!t)throw new Error("supabaseUrl is required.");if(!n)throw new Error("supabaseKey is required.");const a=dD(t);this.realtimeUrl=`${a}/realtime/v1`.replace(/^http/i,"ws"),this.authUrl=`${a}/auth/v1`,this.storageUrl=`${a}/storage/v1`,this.functionsUrl=`${a}/functions/v1`;const l=`sb-${new URL(this.authUrl).hostname.split(".")[0]}-auth-token`,u={db:rD,realtime:oD,auth:Object.assign(Object.assign({},sD),{storageKey:l}),global:nD},c=fD(r??{},u);this.storageKey=(s=c.auth.storageKey)!==null&&s!==void 0?s:"",this.headers=(o=c.global.headers)!==null&&o!==void 0?o:{},c.accessToken?(this.accessToken=c.accessToken,this.auth=new Proxy({},{get:(d,f)=>{throw new Error(`@supabase/supabase-js: Supabase Client is configured with the accessToken option, accessing supabase.auth.${String(f)} is not possible`)}})):this.auth=this._initSupabaseAuthClient((i=c.auth)!==null&&i!==void 0?i:{},this.headers,c.global.fetch),this.fetch=uD(n,this._getAccessToken.bind(this),c.global.fetch),this.realtime=this._initRealtimeClient(Object.assign({headers:this.headers,accessToken:this._getAccessToken.bind(this)},c.realtime)),this.rest=new bb(`${a}/rest/v1`,{headers:this.headers,schema:c.db.schema,fetch:this.fetch}),c.accessToken||this._listenForAuthEvents()}get functions(){return new eb(this.functionsUrl,{headers:this.headers,customFetch:this.fetch})}get storage(){return new Zb(this.storageUrl,this.headers,this.fetch)}from(t){return this.rest.from(t)}schema(t){return this.rest.schema(t)}rpc(t,n={},r={}){return this.rest.rpc(t,n,r)}channel(t,n={config:{}}){return this.realtime.channel(t,n)}getChannels(){return this.realtime.getChannels()}removeChannel(t){return this.realtime.removeChannel(t)}removeAllChannels(){return this.realtime.removeAllChannels()}_getAccessToken(){var t,n;return JD(this,void 0,void 0,function*(){if(this.accessToken)return yield this.accessToken();const{data:r}=yield this.auth.getSession();return(n=(t=r.session)===null||t===void 0?void 0:t.access_token)!==null&&n!==void 0?n:null})}_initSupabaseAuthClient({autoRefreshToken:t,persistSession:n,detectSessionInUrl:r,storage:s,storageKey:o,flowType:i,lock:a,debug:l},u,c){const d={Authorization:`Bearer ${this.supabaseKey}`,apikey:`${this.supabaseKey}`};return new GD({url:this.authUrl,headers:Object.assign(Object.assign({},d),u),storageKey:o,autoRefreshToken:t,persistSession:n,detectSessionInUrl:r,storage:s,flowType:i,lock:a,debug:l,fetch:c,hasCustomAuthorizationHeader:"Authorization"in this.headers})}_initRealtimeClient(t){return new Fb(this.realtimeUrl,Object.assign(Object.assign({},t),{params:Object.assign({apikey:this.supabaseKey},t==null?void 0:t.params)}))}_listenForAuthEvents(){return this.auth.onAuthStateChange((n,r)=>{this._handleTokenChanged(n,"CLIENT",r==null?void 0:r.access_token)})}_handleTokenChanged(t,n,r){(t==="TOKEN_REFRESHED"||t==="SIGNED_IN")&&this.changedAccessToken!==r?this.changedAccessToken=r:t==="SIGNED_OUT"&&(this.realtime.setAuth(),n=="STORAGE"&&this.auth.signOut(),this.changedAccessToken=void 0)}}const ZD=(e,t,n)=>new XD(e,t,n),ga="https://srnfszurrbcvoqkceofj.supabase.co",ya="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNybmZzenVycmJjdm9xa2Nlb2ZqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDAxODIwOTYsImV4cCI6MjA1NTc1ODA5Nn0.zziuiN9fjgJBIFOVssuO2xhBz9GvaqYQW_bs3McV_OA",cs=e=>!e||e==="your_supabase_project_url_here"||e==="your_supabase_anon_key_here"||e.includes("your-project")||e.includes("your_");cs(ga)&&console.warn("⚠️  VITE_SUPABASE_URL is not configured. Please set your Supabase URL in .env.local");cs(ya)&&console.warn("⚠️  VITE_SUPABASE_ANON_KEY is not configured. Please set your Supabase anon key in .env.local");const eC=()=>({auth:{getSession:()=>Promise.resolve({data:{session:null},error:null}),signInWithPassword:()=>Promise.resolve({error:new Error("Supabase not configured")}),signOut:()=>Promise.resolve({error:null}),onAuthStateChange:()=>({data:{subscription:{unsubscribe:()=>{}}}})},from:()=>({select:()=>({data:[],error:null}),insert:()=>({error:new Error("Supabase not configured")}),delete:()=>({error:new Error("Supabase not configured")}),upsert:()=>({error:new Error("Supabase not configured")}),eq:function(){return this},order:function(){return this}})}),Ge=cs(ga)||cs(ya)?eC():ZD(ga,ya,{auth:{autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0}}),Lt=!(cs(ga)||cs(ya)),tC=()=>new Promise((e,t)=>{if(window.Razorpay){e(!0);return}const n=document.getElementById("razorpay-script");if(n){n.onload=()=>e(!0),n.onerror=()=>t(new Error("Failed to load Razorpay script"));return}const r=document.createElement("script");r.id="razorpay-script",r.src="https://checkout.razorpay.com/v1/checkout.js",r.async=!0,r.onload=()=>{console.log("Razorpay script loaded successfully"),e(!0)},r.onerror=()=>{console.error("Failed to load Razorpay script"),t(new Error("Failed to load Razorpay script"))},document.head.appendChild(r)});function nC(){const e=ed(),[t,n]=S.useState([]),[r,s]=S.useState({}),[o,i]=S.useState(new Date),[a,l]=S.useState(null),[u,c]=S.useState({}),[d,f]=S.useState(!0),[p,y]=S.useState(null),[_,x]=S.useState(!1),[g,m]=S.useState(""),[v,k]=S.useState(!1),[E,P]=S.useState(null);S.useEffect(()=>{(async()=>{f(!0),y(null);try{await Promise.all([D(),b(),tC().catch(V=>console.warn("Razorpay script loading failed:",V))])}catch(V){y("Failed to load data. Please refresh the page."),console.error("Error loading data:",V)}finally{f(!1)}})()},[]),S.useEffect(()=>{b()},[o]);const D=async()=>{if(!Lt){n([{id:1,name:"Notebook",price:25.99},{id:2,name:"Pen Set",price:15.5},{id:3,name:"Highlighters",price:12},{id:4,name:"Sticky Notes",price:8.75},{id:5,name:"Stapler",price:22}]);return}const{data:A,error:V}=await Ge.from("stationery_items").select("*").order("name");if(V)throw console.error("Error fetching items:",V),V;n(A||[])},b=async()=>{if(!Lt){c({"9:00 AM":"high","9:10 AM":"high","9:20 AM":"high","10:00 AM":"medium","10:10 AM":"medium","2:00 PM":"low","2:10 PM":"low","3:00 PM":"medium"});return}const A=o.toISOString().split("T")[0],{data:V,error:Y}=await Ge.from("rush_status").select("*").eq("date",A);if(Y)throw console.error("Error fetching rush status:",Y),Y;const ie=(V||[]).reduce((N,L)=>(N[L.time_slot]=L.status,N),{});c(ie)},M=(A,V)=>{s(Y=>({...Y,[A]:V}))},O=t.reduce((A,V)=>A+V.price*(r[V.id]||0),0),F=async()=>{if(!g.trim()){alert("Please enter your name");return}if(!a){alert("Please select a time slot");return}const A=t.filter(Y=>r[Y.id]>0);if(A.length===0){alert("Please select at least one item");return}const V={customer_name:g.trim(),date:o.toISOString().split("T")[0],time_slot:a,items:A.map(Y=>({id:Y.id,name:Y.name,price:Y.price,quantity:r[Y.id]})),total_cost:O};P(V),k(!0)},H=async A=>{var V;x(!0);try{if(!Lt){await new Promise(xr=>setTimeout(xr,1e3)),k(!1);const le=A.method==="online"?"Online Payment":"Cash on Delivery",we=A.status==="completed"?"Successful":"Pending";alert(`Demo booking confirmed for ${E.customer_name}!

Date: ${o.toLocaleDateString()}
Time: ${a}
Total: ₹${O.toFixed(2)}
Payment: ${le} (${we})

Note: This is a demo. Configure Supabase to enable real bookings.`),s({}),l(null),m(""),P(null);return}const Y={...E,payment_method:A.method,payment_status:A.status,payment_id:A.paymentId,payment_amount:A.amount,payment_currency:"INR",payment_completed_at:A.status==="completed"?new Date().toISOString():null},{data:ie,error:N}=await Ge.from("bookings").insert(Y).select();if(N)throw N;k(!1);const L=A.method==="online"?"Online Payment":"Cash on Delivery",$=A.status==="completed"?"Successful":"Pending";alert(`Booking confirmed for ${E.customer_name}!

Booking ID: ${(V=ie[0])==null?void 0:V.id}
Date: ${o.toLocaleDateString()}
Time: ${a}
Total: ₹${O.toFixed(2)}
Payment: ${L} (${$})

${A.paymentId?`Payment ID: ${A.paymentId}`:""}`),s({}),l(null),m(""),P(null)}catch(Y){console.error("Error creating booking:",Y),alert("Failed to create booking. Please try again.")}finally{x(!1)}},ee=()=>{k(!1),P(null)};return d?h.jsx("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:h.jsxs("div",{className:"flex flex-col items-center gap-4",children:[h.jsx(on,{className:"w-8 h-8 animate-spin text-blue-600"}),h.jsx("p",{className:"text-gray-600",children:"Loading stationery items..."})]})}):p?h.jsx("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:h.jsxs("div",{className:"flex flex-col items-center gap-4 text-center",children:[h.jsx(xo,{className:"w-12 h-12 text-red-500"}),h.jsx("h2",{className:"text-xl font-semibold text-gray-900",children:"Something went wrong"}),h.jsx("p",{className:"text-gray-600 max-w-md",children:p}),h.jsx("button",{onClick:()=>window.location.reload(),className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700",children:"Refresh Page"})]})}):h.jsxs("div",{className:"min-h-screen bg-gray-50",children:[h.jsx("header",{className:"bg-white shadow-sm",children:h.jsxs("div",{className:"max-w-7xl mx-auto px-4 py-4 flex justify-between items-center",children:[h.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Stationery Store"}),h.jsxs("button",{onClick:()=>e("/admin/login"),className:"flex items-center gap-2 px-4 py-2 rounded-lg text-gray-600 hover:text-gray-900 hover:bg-gray-100",children:[h.jsx(LS,{className:"w-5 h-5"}),h.jsx("span",{className:"text-sm font-medium",children:"Admin"})]})]})}),!Lt&&h.jsx("div",{className:"bg-blue-50 border-l-4 border-blue-400 p-4",children:h.jsx("div",{className:"max-w-7xl mx-auto flex",children:h.jsxs("div",{className:"flex",children:[h.jsx("div",{className:"flex-shrink-0",children:h.jsx(qg,{className:"h-5 w-5 text-blue-400"})}),h.jsx("div",{className:"ml-3",children:h.jsxs("p",{className:"text-sm text-blue-700",children:[h.jsx("strong",{children:"Demo Mode:"})," Supabase is not configured. The app is running with mock data. To enable full functionality, please set your Supabase credentials in ",h.jsx("code",{className:"bg-blue-100 px-1 rounded",children:".env.local"})]})})]})})}),h.jsx("main",{className:"max-w-7xl mx-auto px-4 py-8",children:h.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[h.jsxs("div",{children:[h.jsx("h2",{className:"text-xl font-semibold mb-4",children:"Available Items"}),h.jsx("div",{className:"space-y-4",children:t.map(A=>h.jsx(zS,{...A,quantity:r[A.id]||0,onQuantityChange:M},A.id))}),h.jsx("div",{className:"mt-6 p-4 bg-white rounded-lg shadow-sm",children:h.jsxs("div",{className:"flex items-center justify-between",children:[h.jsxs("div",{className:"flex items-center gap-2",children:[h.jsx($S,{className:"w-5 h-5"}),h.jsx("span",{className:"font-semibold",children:"Total Cost:"})]}),h.jsxs("span",{className:"text-xl font-bold",children:["₹",O.toFixed(2)]})]})})]}),h.jsxs("div",{children:[h.jsx("h2",{className:"text-xl font-semibold mb-4",children:"Booking Details"}),h.jsxs("div",{className:"bg-white p-6 rounded-lg shadow-sm",children:[h.jsxs("div",{className:"mb-6",children:[h.jsx("label",{htmlFor:"customerName",className:"block text-sm font-medium text-gray-700 mb-2",children:"Your Name *"}),h.jsx("input",{type:"text",id:"customerName",value:g,onChange:A=>m(A.target.value),placeholder:"Enter your full name",className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none transition-colors",required:!0})]}),h.jsxs("div",{className:"mb-6",children:[h.jsx("h3",{className:"text-lg font-medium mb-3",children:"Select Date"}),h.jsx(PS,{selectedDate:o,onDateChange:i,rushStatus:u})]}),h.jsxs("div",{className:"mt-6",children:[h.jsx("h3",{className:"text-lg font-medium mb-3",children:"Available Time Slots"}),h.jsx(HS,{selectedDate:o,selectedSlot:a,onSlotSelect:l,rushStatus:u})]}),h.jsx("button",{onClick:F,disabled:_||!g.trim()||!a||O===0,className:"mt-6 w-full bg-blue-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2",children:_?h.jsxs(h.Fragment,{children:[h.jsx(on,{className:"w-4 h-4 animate-spin"}),"Processing..."]}):"Confirm Booking"}),(!g.trim()||!a||O===0)&&!_&&h.jsxs("div",{className:"mt-2 text-sm text-gray-600 text-center",children:[!g.trim()&&!a&&O===0&&"Please enter your name, select items and a time slot",!g.trim()&&a&&O>0&&"Please enter your name",g.trim()&&!a&&O===0&&"Please select items and a time slot",g.trim()&&!a&&O>0&&"Please select a time slot",g.trim()&&a&&O===0&&"Please select at least one item"]})]})]})]})}),v&&E&&h.jsx(VS,{isOpen:v,onClose:ee,bookingData:E,onPaymentComplete:H})]})}const _y=S.createContext(null);function rC({children:e}){const[t,n]=S.useState(!1),[r,s]=S.useState(!0);S.useEffect(()=>{(async()=>{try{const{data:{session:u},error:c}=await Ge.auth.getSession();c&&console.error("Error getting session:",c),n(!!u)}catch(u){console.error("Error in getInitialSession:",u),n(!1)}finally{s(!1)}})();const{data:{subscription:l}}=Ge.auth.onAuthStateChange((u,c)=>{n(!!c),s(!1)});return()=>l.unsubscribe()},[]);const o=async(a,l)=>{s(!0);try{const{error:u}=await Ge.auth.signInWithPassword({email:a,password:l});if(u)throw u}finally{s(!1)}},i=async()=>{s(!0);try{const{error:a}=await Ge.auth.signOut();if(a)throw a}finally{s(!1)}};return h.jsx(_y.Provider,{value:{isAuthenticated:t,loading:r,login:o,logout:i},children:e})}const Za=()=>{const e=S.useContext(_y);if(!e)throw new Error("useAuth must be used within an AuthProvider");return e};function sC({selectedDate:e,rushStatuses:t,onRushStatusChange:n}){const[r,s]=S.useState(null);S.useState("low");const o=Array.from({length:48},(c,d)=>{const f=Math.floor(d/6)+9,p=d%6*10,y=f>=12?"PM":"AM";return`${f>12?f-12:f}:${p.toString().padStart(2,"0")} ${y}`}),i=c=>{const f=`${qe(e,"yyyy-MM-dd")}_${c}`;return t[f]||"low"},a=c=>{s(c)},l=c=>{if(r){const d=qe(e,"yyyy-MM-dd");n(d,r,c),s(null)}},u=c=>{const d=i(c),f=r===c,p="p-2 text-xs rounded-lg transition-colors cursor-pointer border-2";if(f)return`${p} border-blue-500 bg-blue-50`;switch(d){case"high":return`${p} border-red-200 bg-red-100 hover:bg-red-200 text-red-800`;case"medium":return`${p} border-orange-200 bg-orange-100 hover:bg-orange-200 text-orange-800`;case"low":return`${p} border-green-200 bg-green-100 hover:bg-green-200 text-green-800`;default:return`${p} border-gray-200 bg-gray-100 hover:bg-gray-200 text-gray-800`}};return h.jsxs("div",{className:"space-y-6",children:[h.jsxs("div",{className:"bg-blue-50 p-4 rounded-lg",children:[h.jsx("h4",{className:"font-medium text-blue-900 mb-2",children:"How to set rush status:"}),h.jsxs("ol",{className:"text-sm text-blue-800 space-y-1",children:[h.jsx("li",{children:"1. Click on a time slot to select it"}),h.jsx("li",{children:"2. Choose the rush level using the buttons below"}),h.jsx("li",{children:"3. The time slot color will update automatically"})]})]}),r&&h.jsxs("div",{className:"bg-white p-4 rounded-lg border border-gray-200",children:[h.jsxs("h4",{className:"font-medium text-gray-900 mb-3",children:["Set rush status for ",r," on ",qe(e,"MMMM d, yyyy"),":"]}),h.jsxs("div",{className:"flex gap-2",children:[h.jsx("button",{onClick:()=>l("low"),className:"px-4 py-2 bg-green-100 hover:bg-green-200 text-green-800 rounded-lg font-medium transition-colors",children:"Low Rush"}),h.jsx("button",{onClick:()=>l("medium"),className:"px-4 py-2 bg-orange-100 hover:bg-orange-200 text-orange-800 rounded-lg font-medium transition-colors",children:"Medium Rush"}),h.jsx("button",{onClick:()=>l("high"),className:"px-4 py-2 bg-red-100 hover:bg-red-200 text-red-800 rounded-lg font-medium transition-colors",children:"High Rush"}),h.jsx("button",{onClick:()=>s(null),className:"px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-800 rounded-lg font-medium transition-colors",children:"Cancel"})]})]}),h.jsx("div",{className:"grid grid-cols-6 gap-2",children:o.map(c=>h.jsx("button",{onClick:()=>a(c),className:u(c),title:`${c} - ${i(c)} rush`,children:h.jsxs("div",{className:"text-center",children:[h.jsx("div",{className:"font-medium",children:c}),h.jsx("div",{className:"text-xs opacity-75 capitalize",children:i(c)})]})},c))}),h.jsxs("div",{className:"flex flex-wrap gap-4 justify-center text-sm",children:[h.jsxs("div",{className:"flex items-center gap-2",children:[h.jsx("div",{className:"w-4 h-4 rounded-full bg-green-100 border border-green-200"}),h.jsx("span",{children:"Low Rush"})]}),h.jsxs("div",{className:"flex items-center gap-2",children:[h.jsx("div",{className:"w-4 h-4 rounded-full bg-orange-100 border border-orange-200"}),h.jsx("span",{children:"Medium Rush"})]}),h.jsxs("div",{className:"flex items-center gap-2",children:[h.jsx("div",{className:"w-4 h-4 rounded-full bg-red-100 border border-red-200"}),h.jsx("span",{children:"High Rush"})]}),h.jsxs("div",{className:"flex items-center gap-2",children:[h.jsx("div",{className:"w-4 h-4 rounded-full bg-blue-50 border border-blue-500"}),h.jsx("span",{children:"Selected"})]})]})]})}function oC(){const[e,t]=S.useState([]),[n,r]=S.useState(!0),[s,o]=S.useState(null),[i,a]=S.useState(""),[l,u]=S.useState("");S.useState("all");const[c,d]=S.useState(null);S.useEffect(()=>{f()},[]);const f=async()=>{r(!0),o(null);try{if(!Lt){t([{id:1,customer_name:"John Doe",date:"2025-05-23",time_slot:"10:00 AM",items:[{name:"Notebook",quantity:2,price:25.99},{name:"Pen Set",quantity:1,price:15.5}],total_cost:67.48,payment_method:"online",payment_status:"completed",payment_id:"pay_demo123456",payment_amount:67.48,payment_currency:"INR",payment_completed_at:"2025-05-23T10:32:00Z",created_at:"2025-05-23T10:30:00Z"},{id:2,customer_name:"Jane Smith",date:"2025-05-23",time_slot:"2:00 PM",items:[{name:"Highlighters",quantity:1,price:12}],total_cost:12,payment_method:"cash_on_delivery",payment_status:"not_required",payment_id:null,payment_amount:12,payment_currency:"INR",payment_completed_at:null,created_at:"2025-05-23T11:15:00Z"},{id:3,customer_name:"Mike Johnson",date:"2025-05-24",time_slot:"11:00 AM",items:[{name:"Stapler",quantity:1,price:22}],total_cost:22,payment_method:"online",payment_status:"failed",payment_id:null,payment_amount:22,payment_currency:"INR",payment_completed_at:null,created_at:"2025-05-24T09:45:00Z"}]),r(!1);return}const{data:P,error:D}=await Ge.from("bookings").select("*").order("created_at",{ascending:!1});if(D)throw D;t(P||[])}catch(P){console.error("Error fetching orders:",P),o("Failed to load orders. Please try again.")}finally{r(!1)}},p=async P=>{if(confirm("Are you sure you want to delete this order? This action cannot be undone.")){if(!Lt){t(e.filter(D=>D.id!==P));return}d(P);try{const{error:D}=await Ge.from("bookings").delete().eq("id",P);if(D)throw D;t(e.filter(b=>b.id!==P))}catch(D){console.error("Error deleting order:",D),alert("Failed to delete order. Please try again.")}finally{d(null)}}},y=e.filter(P=>{const D=P.customer_name.toLowerCase().includes(i.toLowerCase())||P.id.toString().includes(i),b=!l||P.date===l;return D&&b}),_=()=>y.reduce((P,D)=>P+D.total_cost,0),x=()=>{const P={};return y.forEach(D=>{P[D.date]||(P[D.date]=[]),P[D.date].push(D)}),P},g=P=>P==="online"?Kg:Qg,m=P=>{switch(P){case"completed":return qu;case"failed":return WS;case"pending":return Ni;case"not_required":return qu;default:return Ni}},v=P=>{switch(P){case"completed":return"text-green-600";case"failed":return"text-red-600";case"pending":return"text-yellow-600";case"not_required":return"text-blue-600";default:return"text-gray-600"}},k=(P,D)=>{if(P==="cash_on_delivery")return"Cash on Delivery";switch(D){case"completed":return"Payment Successful";case"failed":return"Payment Failed";case"pending":return"Payment Pending";default:return"Unknown Status"}};if(n)return h.jsx("div",{className:"flex items-center justify-center py-12",children:h.jsxs("div",{className:"flex flex-col items-center gap-4",children:[h.jsx(on,{className:"w-8 h-8 animate-spin text-blue-600"}),h.jsx("p",{className:"text-gray-600",children:"Loading orders..."})]})});if(s)return h.jsx("div",{className:"flex items-center justify-center py-12",children:h.jsxs("div",{className:"flex flex-col items-center gap-4 text-center",children:[h.jsx(xo,{className:"w-12 h-12 text-red-500"}),h.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Error Loading Orders"}),h.jsx("p",{className:"text-gray-600 max-w-md",children:s}),h.jsx("button",{onClick:f,className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700",children:"Try Again"})]})});const E=x();return h.jsxs("div",{className:"space-y-6",children:[h.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[h.jsx("div",{className:"bg-white p-6 rounded-lg shadow-sm",children:h.jsxs("div",{className:"flex items-center gap-3",children:[h.jsx(Gu,{className:"w-8 h-8 text-blue-600"}),h.jsxs("div",{children:[h.jsx("p",{className:"text-sm text-gray-600",children:"Total Orders"}),h.jsx("p",{className:"text-2xl font-bold text-gray-900",children:y.length})]})]})}),h.jsx("div",{className:"bg-white p-6 rounded-lg shadow-sm",children:h.jsxs("div",{className:"flex items-center gap-3",children:[h.jsx(OS,{className:"w-8 h-8 text-green-600"}),h.jsxs("div",{children:[h.jsx("p",{className:"text-sm text-gray-600",children:"Total Revenue"}),h.jsxs("p",{className:"text-2xl font-bold text-gray-900",children:["₹",_().toFixed(2)]})]})]})}),h.jsx("div",{className:"bg-white p-6 rounded-lg shadow-sm",children:h.jsxs("div",{className:"flex items-center gap-3",children:[h.jsx(US,{className:"w-8 h-8 text-purple-600"}),h.jsxs("div",{children:[h.jsx("p",{className:"text-sm text-gray-600",children:"Unique Customers"}),h.jsx("p",{className:"text-2xl font-bold text-gray-900",children:new Set(y.map(P=>P.customer_name)).size})]})]})})]}),h.jsx("div",{className:"bg-white p-4 rounded-lg shadow-sm",children:h.jsxs("div",{className:"flex flex-col sm:flex-row gap-4",children:[h.jsx("div",{className:"flex-1",children:h.jsxs("div",{className:"relative",children:[h.jsx(IS,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"}),h.jsx("input",{type:"text",placeholder:"Search by customer name or order ID...",value:i,onChange:P=>a(P.target.value),className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"})]})}),h.jsx("div",{className:"sm:w-48",children:h.jsx("input",{type:"date",value:l,onChange:P=>u(P.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"})}),l&&h.jsx("button",{onClick:()=>u(""),className:"px-4 py-2 text-gray-600 hover:text-gray-900",children:"Clear Date"})]})}),y.length===0?h.jsxs("div",{className:"text-center py-12",children:[h.jsx(Gu,{className:"w-16 h-16 text-gray-300 mx-auto mb-4"}),h.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"No Orders Found"}),h.jsx("p",{className:"text-gray-600",children:i||l?"Try adjusting your filters":"No orders have been placed yet"})]}):h.jsx("div",{className:"space-y-6",children:Object.entries(E).map(([P,D])=>h.jsxs("div",{className:"bg-white rounded-lg shadow-sm overflow-hidden",children:[h.jsxs("div",{className:"bg-gray-50 px-6 py-3 border-b",children:[h.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:qe(new Date(P),"EEEE, MMMM d, yyyy")}),h.jsxs("p",{className:"text-sm text-gray-600",children:[D.length," order",D.length!==1?"s":""," • ₹",D.reduce((b,M)=>b+M.total_cost,0).toFixed(2)," total"]})]}),h.jsx("div",{className:"divide-y divide-gray-200",children:D.map(b=>h.jsx("div",{className:"p-6",children:h.jsxs("div",{className:"flex items-start justify-between",children:[h.jsxs("div",{className:"flex-1",children:[h.jsxs("div",{className:"flex items-center gap-4 mb-3",children:[h.jsx("h4",{className:"text-lg font-semibold text-gray-900",children:b.customer_name}),h.jsxs("span",{className:"text-sm text-gray-500",children:["Order #",b.id]}),h.jsxs("div",{className:"flex items-center gap-1 text-sm text-gray-600",children:[h.jsx(Ni,{className:"w-4 h-4"}),b.time_slot]}),h.jsxs("div",{className:`flex items-center gap-1 text-sm ${v(b.payment_status)}`,children:[C.createElement(g(b.payment_method),{className:"w-4 h-4"}),C.createElement(m(b.payment_status),{className:"w-4 h-4"}),h.jsx("span",{className:"font-medium",children:k(b.payment_method,b.payment_status)})]})]}),h.jsx("div",{className:"space-y-2",children:b.items.map((M,O)=>h.jsxs("div",{className:"flex justify-between text-sm",children:[h.jsxs("span",{className:"text-gray-700",children:[M.name," × ",M.quantity]}),h.jsxs("span",{className:"text-gray-900 font-medium",children:["₹",(M.price*M.quantity).toFixed(2)]})]},O))}),h.jsx("div",{className:"mt-3 pt-3 border-t border-gray-200",children:h.jsxs("div",{className:"flex justify-between items-start",children:[h.jsxs("div",{className:"text-sm text-gray-600",children:[h.jsxs("p",{children:["Ordered: ",qe(new Date(b.created_at),"h:mm a")]}),b.payment_id&&h.jsxs("p",{className:"mt-1",children:["Payment ID: ",h.jsx("span",{className:"font-mono text-xs",children:b.payment_id})]}),b.payment_completed_at&&h.jsxs("p",{className:"mt-1",children:["Paid: ",qe(new Date(b.payment_completed_at),"h:mm a")]})]}),h.jsxs("span",{className:"text-lg font-bold text-gray-900",children:["Total: ₹",b.total_cost.toFixed(2)]})]})})]}),h.jsx("button",{onClick:()=>p(b.id),disabled:c===b.id,className:"ml-4 p-2 text-red-600 hover:text-red-800 hover:bg-red-50 rounded-lg transition-colors disabled:opacity-50",title:"Delete order",children:c===b.id?h.jsx(on,{className:"w-4 h-4 animate-spin"}):h.jsx(FS,{className:"w-4 h-4"})})]})},b.id))})]},P))})]})}function iC(){const{logout:e}=Za(),[t,n]=S.useState([]),[r,s]=S.useState({name:"",price:""}),[o,i]=S.useState(new Date),[a,l]=S.useState("orders"),[u,c]=S.useState(!1),[d,f]=S.useState(!0),[p,y]=S.useState(null),[_,x]=S.useState({}),[g,m]=S.useState(!1);S.useEffect(()=>{(async()=>{f(!0),y(null);try{await Promise.all([v(),k()])}catch(M){y("Failed to load dashboard data. Please refresh the page."),console.error("Error loading dashboard data:",M)}finally{f(!1)}})()},[o]);const v=async()=>{try{if(!Lt){n([{id:1,name:"Notebook",price:25.99},{id:2,name:"Pen Set",price:15.5},{id:3,name:"Highlighters",price:12},{id:4,name:"Sticky Notes",price:8.75},{id:5,name:"Stapler",price:22}]);return}const{data:b,error:M}=await Ge.from("stationery_items").select("*").order("name");if(M)throw M;n(b||[])}catch(b){throw console.error("Error fetching items:",b),b}},k=async()=>{try{if(!Lt){const F={[`${qe(o,"yyyy-MM-dd")}_9:00 AM`]:"high",[`${qe(o,"yyyy-MM-dd")}_10:00 AM`]:"medium",[`${qe(o,"yyyy-MM-dd")}_2:00 PM`]:"low"};x(F);return}const{data:b,error:M}=await Ge.from("rush_status").select("*").eq("date",qe(o,"yyyy-MM-dd"));if(M)throw M;const O={};b==null||b.forEach(F=>{const H=`${F.date}_${F.time_slot}`;O[H]=F.status}),x(O)}catch(b){throw console.error("Error fetching rush statuses:",b),b}},E=async()=>{if(!r.name||!r.price){alert("Please fill in all fields");return}c(!0);try{const{error:b}=await Ge.from("stationery_items").insert({name:r.name,price:parseFloat(r.price)});if(b)throw b;s({name:"",price:""}),await v(),alert("Item added successfully!")}catch(b){console.error("Error adding item:",b),alert("Failed to add item")}finally{c(!1)}},P=async b=>{if(confirm("Are you sure you want to delete this item?")){c(!0);try{const{error:M}=await Ge.from("stationery_items").delete().eq("id",b);if(M)throw M;await v(),alert("Item deleted successfully!")}catch(M){console.error("Error deleting item:",M),alert("Failed to delete item")}finally{c(!1)}}},D=async(b,M,O)=>{m(!0);try{if(!Lt){const ee=`${b}_${M}`;x(A=>({...A,[ee]:O}));return}const{error:F}=await Ge.from("rush_status").upsert({date:b,time_slot:M,status:O});if(F)throw F;const H=`${b}_${M}`;x(ee=>({...ee,[H]:O}))}catch(F){console.error("Error updating rush status:",F),alert("Failed to update rush status. Please try again.")}finally{m(!1)}};return d?h.jsx("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:h.jsxs("div",{className:"flex flex-col items-center gap-4",children:[h.jsx(on,{className:"w-8 h-8 animate-spin text-blue-600"}),h.jsx("p",{className:"text-gray-600",children:"Loading dashboard..."})]})}):p?h.jsx("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:h.jsxs("div",{className:"flex flex-col items-center gap-4 text-center",children:[h.jsx(xo,{className:"w-12 h-12 text-red-500"}),h.jsx("h2",{className:"text-xl font-semibold text-gray-900",children:"Something went wrong"}),h.jsx("p",{className:"text-gray-600 max-w-md",children:p}),h.jsx("button",{onClick:()=>window.location.reload(),className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700",children:"Refresh Page"})]})}):h.jsxs("div",{className:"min-h-screen bg-gray-50",children:[h.jsx("header",{className:"bg-white shadow-sm",children:h.jsxs("div",{className:"max-w-7xl mx-auto px-4 py-4 flex justify-between items-center",children:[h.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Admin Dashboard"}),h.jsxs("button",{onClick:()=>e(),className:"flex items-center gap-2 px-4 py-2 text-sm font-medium text-gray-700 hover:text-gray-900",children:[h.jsx(jS,{className:"w-4 h-4"}),"Logout"]})]})}),!Lt&&h.jsx("div",{className:"bg-orange-50 border-l-4 border-orange-400 p-4",children:h.jsx("div",{className:"max-w-7xl mx-auto flex",children:h.jsxs("div",{className:"flex",children:[h.jsx("div",{className:"flex-shrink-0",children:h.jsx(qg,{className:"h-5 w-5 text-orange-400"})}),h.jsx("div",{className:"ml-3",children:h.jsxs("p",{className:"text-sm text-orange-700",children:[h.jsx("strong",{children:"Demo Mode:"})," Admin functions are limited. Configure Supabase in ",h.jsx("code",{className:"bg-orange-100 px-1 rounded",children:".env.local"})," to enable full admin functionality."]})})]})})}),h.jsxs("main",{className:"max-w-7xl mx-auto px-4 py-8",children:[h.jsxs("div",{className:"flex gap-4 mb-6",children:[h.jsxs("button",{onClick:()=>l("orders"),className:`flex items-center gap-2 px-4 py-2 rounded-lg font-medium ${a==="orders"?"bg-blue-600 text-white":"bg-white text-gray-700 hover:bg-gray-50"}`,children:[h.jsx(AS,{className:"w-4 h-4"}),"Orders"]}),h.jsxs("button",{onClick:()=>l("items"),className:`flex items-center gap-2 px-4 py-2 rounded-lg font-medium ${a==="items"?"bg-blue-600 text-white":"bg-white text-gray-700 hover:bg-gray-50"}`,children:[h.jsx(Gu,{className:"w-4 h-4"}),"Manage Items"]}),h.jsxs("button",{onClick:()=>l("rush"),className:`flex items-center gap-2 px-4 py-2 rounded-lg font-medium ${a==="rush"?"bg-blue-600 text-white":"bg-white text-gray-700 hover:bg-gray-50"}`,children:[h.jsx(Ni,{className:"w-4 h-4"}),"Rush Hours"]})]}),a==="orders"&&h.jsx(oC,{}),a==="items"&&h.jsxs("div",{className:"bg-white rounded-lg shadow-sm p-6",children:[h.jsx("h2",{className:"text-xl font-semibold mb-4",children:"Stationery Items"}),h.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-6",children:[h.jsx("input",{type:"text",placeholder:"Item name",value:r.name,onChange:b=>s({...r,name:b.target.value}),className:"px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"}),h.jsx("input",{type:"number",placeholder:"Price",value:r.price,onChange:b=>s({...r,price:b.target.value}),className:"px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"}),h.jsxs("button",{onClick:E,disabled:u,className:"flex items-center justify-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50",children:[h.jsx(Gg,{className:"w-4 h-4"}),"Add Item"]})]}),h.jsx("div",{className:"space-y-2",children:t.map(b=>h.jsxs("div",{className:"flex items-center justify-between p-3 border rounded-lg",children:[h.jsxs("div",{children:[h.jsx("span",{className:"font-medium",children:b.name}),h.jsxs("span",{className:"text-gray-600 ml-2",children:["₹",b.price]})]}),h.jsx("button",{onClick:()=>P(b.id),disabled:u,className:"p-1 text-red-600 hover:bg-red-50 rounded disabled:opacity-50",children:h.jsx(YS,{className:"w-4 h-4"})})]},b.id))})]}),a==="rush"&&h.jsxs("div",{className:"bg-white rounded-lg shadow-sm p-6",children:[h.jsx("h2",{className:"text-xl font-semibold mb-4",children:"Rush Hour Management"}),h.jsx("p",{className:"text-gray-600 mb-6",children:"Manage rush hour status for different time slots. Customers will see these colors when booking."}),h.jsxs("div",{className:"mb-6",children:[h.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Select Date"}),h.jsx("input",{type:"date",value:qe(o,"yyyy-MM-dd"),onChange:b=>i(new Date(b.target.value)),className:"px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),g&&h.jsxs("div",{className:"mb-4 flex items-center gap-2 text-blue-600",children:[h.jsx(on,{className:"w-4 h-4 animate-spin"}),h.jsx("span",{className:"text-sm",children:"Updating rush status..."})]}),h.jsx(sC,{selectedDate:o,rushStatuses:_,onRushStatusChange:D})]})]})]})}function aC(){const[e,t]=S.useState(""),[n,r]=S.useState(""),[s,o]=S.useState(""),[i,a]=S.useState(!1),{login:l}=Za(),u=ed(),c=async d=>{d.preventDefault(),a(!0),o("");try{await l(e,n),u("/admin")}catch(f){console.error("Login error:",f),o("Invalid credentials. Please check your email and password.")}finally{a(!1)}};return h.jsx("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8",children:h.jsxs("div",{className:"max-w-md w-full space-y-8",children:[h.jsxs("div",{children:[h.jsx("div",{className:"mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-blue-100",children:h.jsx(MS,{className:"h-6 w-6 text-blue-600"})}),h.jsx("h2",{className:"mt-6 text-center text-3xl font-extrabold text-gray-900",children:"Admin Login"})]}),h.jsxs("form",{className:"mt-8 space-y-6",onSubmit:c,children:[s&&h.jsx("div",{className:"rounded-md bg-red-50 p-4",children:h.jsx("p",{className:"text-sm text-red-700",children:s})}),h.jsxs("div",{className:"rounded-md shadow-sm -space-y-px",children:[h.jsxs("div",{children:[h.jsx("label",{htmlFor:"email-address",className:"sr-only",children:"Email address"}),h.jsx("input",{id:"email-address",name:"email",type:"email",autoComplete:"email",required:!0,className:"appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm",placeholder:"Email address",value:e,onChange:d=>t(d.target.value)})]}),h.jsxs("div",{children:[h.jsx("label",{htmlFor:"password",className:"sr-only",children:"Password"}),h.jsx("input",{id:"password",name:"password",type:"password",autoComplete:"current-password",required:!0,className:"appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm",placeholder:"Password",value:n,onChange:d=>r(d.target.value)})]})]}),h.jsx("div",{children:h.jsx("button",{type:"submit",disabled:i,className:"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed",children:i?h.jsxs("div",{className:"flex items-center gap-2",children:[h.jsx(on,{className:"w-4 h-4 animate-spin"}),"Signing in..."]}):"Sign in"})})]})]})})}function xy(){return h.jsx("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:h.jsxs("div",{className:"flex flex-col items-center gap-4",children:[h.jsx(on,{className:"w-8 h-8 animate-spin text-blue-600"}),h.jsx("p",{className:"text-gray-600",children:"Loading..."})]})})}function lC({children:e}){const{isAuthenticated:t,loading:n}=Za();return n?h.jsx(xy,{}):t?h.jsx(h.Fragment,{children:e}):h.jsx($w,{to:"/admin/login"})}function uC(){const{loading:e}=Za();return e?h.jsx(xy,{}):h.jsxs(Yw,{children:[h.jsx(ki,{path:"/",element:h.jsx(nC,{})}),h.jsx(ki,{path:"/admin/login",element:h.jsx(aC,{})}),h.jsx(ki,{path:"/admin/*",element:h.jsx(lC,{children:h.jsx(iC,{})})})]})}function cC(){return h.jsx(rC,{children:h.jsx(Bw,{children:h.jsx(uC,{})})})}Gm(document.getElementById("root")).render(h.jsx(S.StrictMode,{children:h.jsx(cC,{})}));export{Lh as g};
