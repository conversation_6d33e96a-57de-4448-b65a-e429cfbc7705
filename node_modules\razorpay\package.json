{"name": "razorpay", "version": "2.9.6", "description": "Official Node SDK for Razorpay API", "main": "dist/razorpay", "typings": "dist/razorpay", "scripts": {"prepublish": "npm test", "clean": "rm -rf dist && mkdir dist", "cp-types": "mkdir dist/types && cp lib/types/* dist/types && cp lib/utils/*d.ts dist/utils", "cp-ts": "cp lib/razorpay.d.ts dist/ && cp lib/oAuthTokenClient.d.ts dist/ && npm run cp-types", "build:commonjs": "babel lib -d dist", "build": "npm run clean && npm run build:commonjs && npm run cp-ts", "debug": "npm run build && node-debug examples/index.js", "test": "npm run build && mocha --recursive --require babel-register test/ && nyc --reporter=text mocha", "coverage": "nyc report --reporter=text-lcov > coverage.lcov"}, "repository": {"type": "git", "url": "https://github.com/razorpay/razorpay-node.git"}, "keywords": ["razorpay", "payments", "node", "nodejs", "razorpay-node"], "files": ["dist"], "mocha": {"recursive": true, "full-trace": true}, "license": "MIT", "devDependencies": {"@types/node": "^20.12.12", "babel-cli": "^6.26.0", "babel-preset-env": "^1.7.0", "babel-preset-stage-0": "^6.24.0", "babel-register": "^6.26.0", "chai": "^4.3.4", "deep-equal": "^2.0.5", "mocha": "^9.0.0", "nock": "^13.1.1", "nyc": "^15.1.0", "typescript": "^4.9.4"}, "dependencies": {"axios": "^1.6.8"}}