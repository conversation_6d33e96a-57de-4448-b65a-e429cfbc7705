{"author": "HackerOne", "name": "react-datepicker", "description": "A simple and reusable datepicker component for React", "version": "6.9.0", "license": "MIT", "homepage": "https://github.com/Hacker0x01/react-datepicker", "main": "dist/index.js", "browser": "dist/react-datepicker.min.js", "module": "dist/es/index.js", "unpkg": "dist/react-datepicker.min.js", "style": "dist/react-datepicker.min.css", "files": ["*.md", "dist", "lib", "es", "src/stylesheets"], "sideEffects": ["**/*.css"], "keywords": ["react", "datepicker", "calendar", "date", "react-component"], "repository": {"type": "git", "url": "git://github.com/Hacker0x01/react-datepicker.git"}, "bugs": {"url": "https://github.com/Hacker0x01/react-datepicker/issues"}, "devDependencies": {"@babel/core": "^7.22.8", "@babel/eslint-parser": "^7.22.7", "@babel/helpers": "^7.22.6", "@babel/plugin-external-helpers": "^7.22.5", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/preset-env": "^7.22.10", "@babel/preset-react": "^7.22.5", "@react-docgen/cli": "^2.0.3", "@rollup/plugin-babel": "^6.0.4", "@rollup/plugin-commonjs": "^25.0.7", "@rollup/plugin-node-resolve": "^15.2.3", "@rollup/plugin-terser": "^0.4.4", "@testing-library/react": "^15.0.0", "@types/jest": "^29.5.4", "@types/node": "20", "axe-core": "^4.4.1", "babel-jest": "^29.6.4", "babel-plugin-transform-react-remove-prop-types": "^0.4.24", "core-js": "^3.29.1", "eslint": "^8.44.0", "eslint-plugin-react": "^7.32.2", "eslint-plugin-unused-imports": "^3.0.0", "husky": "9", "jest": "^29.6.4", "jest-canvas-mock": "^2.5.2", "jest-environment-jsdom": "^29.6.4", "lint-staged": "^15.0.1", "lodash": "^4.17.21", "prettier": "^3.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "rollup": "^4.13.0", "rollup-plugin-filesize": "^10.0.0", "sass": "1.75.0", "slugify": "^1.6.6", "stylelint": "^16.2.1", "stylelint-config-standard": "^36.0.0", "stylelint-config-standard-scss": "^13.0.0", "stylelint-scss": "^6.2.1", "ts-jest": "^29.1.1"}, "peerDependencies": {"react": "^16.9.0 || ^17 || ^18", "react-dom": "^16.9.0 || ^17 || ^18"}, "dependencies": {"@floating-ui/react": "^0.26.2", "clsx": "^2.1.0", "date-fns": "^3.3.1", "prop-types": "^15.7.2", "react-onclickoutside": "^6.13.0"}, "scripts": {"eslint": "eslint --ext .js,.jsx src test", "precommit": "lint-staged --allow-empty", "sass-lint": "stylelint 'src/stylesheets/*.scss'", "lint": "yarn run eslint && yarn run sass-lint", "prettier": "prettier --write '**/*.{js,jsx}'", "prettier:check": "prettier --check '**/*.{js,jsx}'", "start": "yarn --cwd docs-site install && yarn --cwd docs-site start", "test": "NODE_ENV=test jest", "test:ci": "NODE_ENV=test jest --ci --coverage", "test:watch": "NODE_OPTIONS=--openssl-legacy-provider NODE_ENV=test jest --watch", "build": "NODE_ENV=production yarn run build:js && NODE_ENV=production yarn run css:prod && NODE_ENV=production yarn run css:modules:dev && NODE_ENV=production yarn run css:dev && NODE_ENV=production yarn run css:modules:dev", "build-dev": "NODE_ENV=development yarn run js:dev && NODE_ENV=development yarn run css:dev && NODE_ENV=development yarn run css:modules:dev", "css:prod": "sass --style compressed src/stylesheets/datepicker.scss > dist/react-datepicker.min.css", "css:modules:prod": "sass --style compressed src/stylesheets/datepicker-cssmodules.scss | tee dist/react-datepicker-cssmodules.min.css dist/react-datepicker-min.module.css", "css:dev": "sass --style expanded src/stylesheets/datepicker.scss > dist/react-datepicker.css", "css:modules:dev": "sass --style expanded src/stylesheets/datepicker-cssmodules.scss | tee dist/react-datepicker-cssmodules.css dist/react-datepicker.module.css", "build:js": "rollup -c", "js:dev": "rollup -cw", "prepare": "husky install"}, "lint-staged": {"*.{js,jsx,json,css,scss,md}": ["prettier --write", "git add"]}}