{"version": 3, "names": ["_buffer", "require", "n", "_t", "generatorFunctions", "isExpression", "isFunction", "isStatement", "isClassBody", "isTSInterfaceBody", "isTSEnumDeclaration", "SCIENTIFIC_NOTATION", "ZERO_DECIMAL_INTEGER", "HAS_NEWLINE", "HAS_NEWLINE_OR_BlOCK_COMMENT_END", "commentIsNewline", "c", "type", "test", "value", "needsParens", "Printer", "constructor", "format", "map", "inForStatementInit", "tokenContext", "_currentNode", "_indent", "_indentRepeat", "_insideAux", "_noLineTerminator", "_noLineTerminatorAfterNode", "_printAuxAfterOnNextUserNode", "_printedComments", "Set", "_endsWithInteger", "_endsWithWord", "_endsWithDiv", "_lastCommentLine", "_endsWithInnerRaw", "_indentInnerComments", "indent", "style", "length", "_inputMap", "_buf", "<PERSON><PERSON><PERSON>", "enterForStatementInit", "enterDelimited", "oldInForStatementInit", "oldNoLineTerminatorAfterNode", "generate", "ast", "print", "_maybe<PERSON>dd<PERSON>uxComment", "get", "compact", "concise", "dedent", "semicolon", "force", "_appendChar", "_queue", "rightBrace", "node", "minified", "removeLastSemicolon", "sourceWithOffset", "loc", "token", "rightParens", "space", "_space", "<PERSON><PERSON><PERSON><PERSON>", "lastCp", "getLastChar", "word", "str", "noLineTerminatorAfter", "_maybePrintInnerComments", "charCodeAt", "_append", "number", "isNonDecimalLiteral", "secondChar", "Number", "isInteger", "maybeNewline", "lastChar", "str<PERSON><PERSON><PERSON>", "tokenChar", "char", "newline", "i", "retainLines", "getNewlineCount", "j", "_newline", "endsWith", "endsWithCharAndNewline", "removeTrailingNewline", "exactSource", "cb", "_catchUp", "source", "prop", "columnOffset", "sourceIdentifierName", "identifierName", "pos", "_canMarkIdName", "sourcePosition", "_sourcePosition", "identifierNamePos", "_maybeIndent", "append", "appendChar", "queue", "firstChar", "queueIndentation", "_getIndent", "_shouldIndent", "catchUp", "line", "count", "getCurrentLine", "_loc$prop", "printTerminatorless", "trailingCommentsLineOffset", "_node$extra", "_node$leadingComments", "_node$leadingComments2", "nodeType", "oldConcise", "_compact", "printMethod", "undefined", "ReferenceError", "JSON", "stringify", "name", "parent", "oldInAux", "parenthesized", "extra", "shouldPrintParens", "retainFunctionParens", "leadingComments", "parentType", "callee", "indentParenthesized", "some", "start", "oldInForStatementInitWasTrue", "isLastChild", "_node$trailingComment", "trailingComments", "_printLeadingComments", "bind", "_printTrailingComments", "enteredPositionlessNode", "_printAuxBeforeComment", "_printAuxAfterComment", "comment", "auxiliaryCommentBefore", "_printComment", "auxiliaryCommentAfter", "getPossibleRaw", "raw", "rawValue", "printJoin", "nodes", "opts", "_nodes$0$loc", "startLine", "newlineOpts", "addNewlines", "nextNodeStartLine", "separator", "len", "statement", "_printNewline", "iterator", "_node$trailingComment2", "_nextNode$loc", "nextNode", "printAndIndentOnComments", "printBlock", "body", "lineOffset", "innerComments", "_printComments", "comments", "printInnerComments", "hasSpace", "printedCommentsCount", "size", "noIndentInnerCommentsHere", "printSequence", "_opts$indent", "printList", "items", "commaSeparator", "newLine", "lastCommentLine", "offset", "_should<PERSON>rintComment", "ignore", "has", "add", "shouldPrintComment", "skipNewLines", "noLineTerminator", "isBlockComment", "printNewLines", "lastCharCode", "val", "adjustMultilineComment", "_comment$loc", "column", "newlineRegex", "RegExp", "replace", "indentSize", "getCurrentColumn", "repeat", "nodeLoc", "hasLoc", "nodeStartLine", "nodeEndLine", "end", "lastLine", "leadingCommentNewline", "should<PERSON><PERSON>t", "commentStartLine", "commentEndLine", "Math", "max", "min", "singleLine", "shouldSkipNewline", "properties", "Object", "assign", "prototype", "Noop", "_default", "exports", "default"], "sources": ["../src/printer.ts"], "sourcesContent": ["import Buffer, { type Po<PERSON> } from \"./buffer.ts\";\nimport type { Loc } from \"./buffer.ts\";\nimport * as n from \"./node/index.ts\";\nimport type * as t from \"@babel/types\";\nimport {\n  isExpression,\n  isFunction,\n  isStatement,\n  isClassBody,\n  isTSInterfaceBody,\n  isTSEnumDeclaration,\n} from \"@babel/types\";\nimport type { Opts as jsescOptions } from \"jsesc\";\n\nimport type { GeneratorOptions } from \"./index.ts\";\nimport * as generatorFunctions from \"./generators/index.ts\";\nimport type SourceMap from \"./source-map.ts\";\nimport type { TraceMap } from \"@jridgewell/trace-mapping\";\n\n// We inline this package\n// eslint-disable-next-line import/no-extraneous-dependencies\nimport * as charCodes from \"charcodes\";\n\nconst SCIENTIFIC_NOTATION = /e/i;\nconst ZERO_DECIMAL_INTEGER = /\\.0+$/;\nconst HAS_NEWLINE = /[\\n\\r\\u2028\\u2029]/;\nconst HAS_NEWLINE_OR_BlOCK_COMMENT_END = /[\\n\\r\\u2028\\u2029]|\\*\\//;\n\nfunction commentIsNewline(c: t.Comment) {\n  return c.type === \"CommentLine\" || HAS_NEWLINE.test(c.value);\n}\n\nconst { needsParens } = n;\n\nconst enum COMMENT_TYPE {\n  LEADING,\n  INNER,\n  TRAILING,\n}\n\nconst enum COMMENT_SKIP_NEWLINE {\n  DEFAULT,\n  ALL,\n  LEADING,\n  TRAILING,\n}\n\nconst enum PRINT_COMMENT_HINT {\n  SKIP,\n  ALLOW,\n  DEFER,\n}\n\nexport type Format = {\n  shouldPrintComment: (comment: string) => boolean;\n  retainLines: boolean;\n  retainFunctionParens: boolean;\n  comments: boolean;\n  auxiliaryCommentBefore: string;\n  auxiliaryCommentAfter: string;\n  compact: boolean | \"auto\";\n  minified: boolean;\n  concise: boolean;\n  indent: {\n    adjustMultilineComment: boolean;\n    style: string;\n  };\n  /**\n   * @deprecated Removed in Babel 8, syntax type is always 'hash'\n   */\n  recordAndTupleSyntaxType?: GeneratorOptions[\"recordAndTupleSyntaxType\"];\n  jsescOption: jsescOptions;\n  /**\n   * @deprecated Removed in Babel 8, use `jsescOption` instead\n   */\n  jsonCompatibleStrings?: boolean;\n  /**\n   * For use with the Hack-style pipe operator.\n   * Changes what token is used for pipe bodies’ topic references.\n   */\n  topicToken?: GeneratorOptions[\"topicToken\"];\n  /**\n   * @deprecated Removed in Babel 8\n   */\n  decoratorsBeforeExport?: boolean;\n  /**\n   * The import attributes syntax style:\n   * - \"with\"        : `import { a } from \"b\" with { type: \"json\" };`\n   * - \"assert\"      : `import { a } from \"b\" assert { type: \"json\" };`\n   * - \"with-legacy\" : `import { a } from \"b\" with type: \"json\";`\n   */\n  importAttributesKeyword?: \"with\" | \"assert\" | \"with-legacy\";\n};\n\ninterface AddNewlinesOptions {\n  addNewlines(leading: boolean, node: t.Node): number;\n  nextNodeStartLine: number;\n}\n\ninterface PrintSequenceOptions extends Partial<AddNewlinesOptions> {\n  statement?: boolean;\n  indent?: boolean;\n  trailingCommentsLineOffset?: number;\n}\n\ninterface PrintListOptions {\n  separator?: (this: Printer) => void;\n  iterator?: (node: t.Node, index: number) => void;\n  statement?: boolean;\n  indent?: boolean;\n}\n\nexport type PrintJoinOptions = PrintListOptions & PrintSequenceOptions;\nclass Printer {\n  constructor(format: Format, map: SourceMap) {\n    this.format = format;\n\n    this._indentRepeat = format.indent.style.length;\n\n    this._inputMap = map?._inputMap;\n\n    this._buf = new Buffer(map, format.indent.style[0]);\n  }\n  declare _inputMap: TraceMap;\n\n  declare format: Format;\n\n  inForStatementInit: boolean = false;\n  enterForStatementInit() {\n    if (this.inForStatementInit) return () => {};\n    this.inForStatementInit = true;\n    return () => {\n      this.inForStatementInit = false;\n    };\n  }\n\n  enterDelimited() {\n    const oldInForStatementInit = this.inForStatementInit;\n    const oldNoLineTerminatorAfterNode = this._noLineTerminatorAfterNode;\n    if (\n      oldInForStatementInit === false &&\n      oldNoLineTerminatorAfterNode === null\n    ) {\n      return () => {};\n    }\n    this.inForStatementInit = false;\n    this._noLineTerminatorAfterNode = null;\n    return () => {\n      this.inForStatementInit = oldInForStatementInit;\n      this._noLineTerminatorAfterNode = oldNoLineTerminatorAfterNode;\n    };\n  }\n\n  tokenContext: number = 0;\n\n  declare _buf: Buffer;\n  _currentNode: t.Node = null;\n  _indent: number = 0;\n  _indentRepeat: number = 0;\n  _insideAux: boolean = false;\n  _noLineTerminator: boolean = false;\n  _noLineTerminatorAfterNode: t.Node | null = null;\n  _printAuxAfterOnNextUserNode: boolean = false;\n  _printedComments = new Set<t.Comment>();\n  _endsWithInteger = false;\n  _endsWithWord = false;\n  _endsWithDiv = false;\n  _lastCommentLine = 0;\n  _endsWithInnerRaw: boolean = false;\n  _indentInnerComments: boolean = true;\n\n  generate(ast: t.Node) {\n    this.print(ast);\n    this._maybeAddAuxComment();\n\n    return this._buf.get();\n  }\n\n  /**\n   * Increment indent size.\n   */\n\n  indent(): void {\n    if (this.format.compact || this.format.concise) return;\n\n    this._indent++;\n  }\n\n  /**\n   * Decrement indent size.\n   */\n\n  dedent(): void {\n    if (this.format.compact || this.format.concise) return;\n\n    this._indent--;\n  }\n\n  /**\n   * Add a semicolon to the buffer.\n   */\n\n  semicolon(force: boolean = false): void {\n    this._maybeAddAuxComment();\n    if (force) {\n      this._appendChar(charCodes.semicolon);\n    } else {\n      this._queue(charCodes.semicolon);\n    }\n    this._noLineTerminator = false;\n  }\n\n  /**\n   * Add a right brace to the buffer.\n   */\n\n  rightBrace(node: t.Node): void {\n    if (this.format.minified) {\n      this._buf.removeLastSemicolon();\n    }\n    this.sourceWithOffset(\"end\", node.loc, -1);\n    this.token(\"}\");\n  }\n\n  rightParens(node: t.Node): void {\n    this.sourceWithOffset(\"end\", node.loc, -1);\n    this.token(\")\");\n  }\n\n  /**\n   * Add a space to the buffer unless it is compact.\n   */\n\n  space(force: boolean = false): void {\n    if (this.format.compact) return;\n\n    if (force) {\n      this._space();\n    } else if (this._buf.hasContent()) {\n      const lastCp = this.getLastChar();\n      if (lastCp !== charCodes.space && lastCp !== charCodes.lineFeed) {\n        this._space();\n      }\n    }\n  }\n\n  /**\n   * Writes a token that can't be safely parsed without taking whitespace into account.\n   */\n\n  word(str: string, noLineTerminatorAfter: boolean = false): void {\n    this.tokenContext = 0;\n\n    this._maybePrintInnerComments();\n\n    // prevent concatenating words and creating // comment out of division and regex\n    if (\n      this._endsWithWord ||\n      (this._endsWithDiv && str.charCodeAt(0) === charCodes.slash)\n    ) {\n      this._space();\n    }\n\n    this._maybeAddAuxComment();\n    this._append(str, false);\n\n    this._endsWithWord = true;\n    this._noLineTerminator = noLineTerminatorAfter;\n  }\n\n  /**\n   * Writes a number token so that we can validate if it is an integer.\n   */\n\n  number(str: string, number?: number): void {\n    // const NON_DECIMAL_LITERAL = /^0[box]/;\n    function isNonDecimalLiteral(str: string) {\n      if (str.length > 2 && str.charCodeAt(0) === charCodes.digit0) {\n        const secondChar = str.charCodeAt(1);\n        return (\n          secondChar === charCodes.lowercaseB ||\n          secondChar === charCodes.lowercaseO ||\n          secondChar === charCodes.lowercaseX\n        );\n      }\n      return false;\n    }\n    this.word(str);\n\n    // Integer tokens need special handling because they cannot have '.'s inserted\n    // immediately after them.\n    this._endsWithInteger =\n      Number.isInteger(number) &&\n      !isNonDecimalLiteral(str) &&\n      !SCIENTIFIC_NOTATION.test(str) &&\n      !ZERO_DECIMAL_INTEGER.test(str) &&\n      str.charCodeAt(str.length - 1) !== charCodes.dot;\n  }\n\n  /**\n   * Writes a simple token.\n   */\n  token(str: string, maybeNewline = false): void {\n    this.tokenContext = 0;\n\n    this._maybePrintInnerComments();\n\n    const lastChar = this.getLastChar();\n    const strFirst = str.charCodeAt(0);\n    if (\n      (lastChar === charCodes.exclamationMark &&\n        // space is mandatory to avoid outputting <!--\n        // http://javascript.spec.whatwg.org/#comment-syntax\n        (str === \"--\" ||\n          // Needs spaces to avoid changing a! == 0 to a!== 0\n          strFirst === charCodes.equalsTo)) ||\n      // Need spaces for operators of the same kind to avoid: `a+++b`\n      (strFirst === charCodes.plusSign && lastChar === charCodes.plusSign) ||\n      (strFirst === charCodes.dash && lastChar === charCodes.dash) ||\n      // Needs spaces to avoid changing '34' to '34.', which would still be a valid number.\n      (strFirst === charCodes.dot && this._endsWithInteger)\n    ) {\n      this._space();\n    }\n\n    this._maybeAddAuxComment();\n    this._append(str, maybeNewline);\n    this._noLineTerminator = false;\n  }\n\n  tokenChar(char: number): void {\n    this.tokenContext = 0;\n\n    this._maybePrintInnerComments();\n\n    const lastChar = this.getLastChar();\n    if (\n      // Need spaces for operators of the same kind to avoid: `a+++b`\n      (char === charCodes.plusSign && lastChar === charCodes.plusSign) ||\n      (char === charCodes.dash && lastChar === charCodes.dash) ||\n      // Needs spaces to avoid changing '34' to '34.', which would still be a valid number.\n      (char === charCodes.dot && this._endsWithInteger)\n    ) {\n      this._space();\n    }\n\n    this._maybeAddAuxComment();\n    this._appendChar(char);\n    this._noLineTerminator = false;\n  }\n\n  /**\n   * Add a newline (or many newlines), maintaining formatting.\n   * This function checks the number of newlines in the queue and subtracts them.\n   * It currently has some limitations.\n   * @see {Buffer#getNewlineCount}\n   */\n  newline(i: number = 1, force?: boolean): void {\n    if (i <= 0) return;\n\n    if (!force) {\n      if (this.format.retainLines || this.format.compact) return;\n\n      if (this.format.concise) {\n        this.space();\n        return;\n      }\n    }\n\n    if (i > 2) i = 2; // Max two lines\n\n    i -= this._buf.getNewlineCount();\n\n    for (let j = 0; j < i; j++) {\n      this._newline();\n    }\n\n    return;\n  }\n\n  endsWith(char: number): boolean {\n    return this.getLastChar() === char;\n  }\n\n  getLastChar(): number {\n    return this._buf.getLastChar();\n  }\n\n  endsWithCharAndNewline(): number {\n    return this._buf.endsWithCharAndNewline();\n  }\n\n  removeTrailingNewline(): void {\n    this._buf.removeTrailingNewline();\n  }\n\n  exactSource(loc: Loc | undefined, cb: () => void) {\n    if (!loc) {\n      cb();\n      return;\n    }\n\n    this._catchUp(\"start\", loc);\n\n    this._buf.exactSource(loc, cb);\n  }\n\n  source(prop: \"start\" | \"end\", loc: Loc | undefined): void {\n    if (!loc) return;\n\n    this._catchUp(prop, loc);\n\n    this._buf.source(prop, loc);\n  }\n\n  sourceWithOffset(\n    prop: \"start\" | \"end\",\n    loc: Loc | undefined,\n    columnOffset: number,\n  ): void {\n    if (!loc) return;\n\n    this._catchUp(prop, loc);\n\n    this._buf.sourceWithOffset(prop, loc, columnOffset);\n  }\n\n  sourceIdentifierName(identifierName: string, pos?: Pos): void {\n    if (!this._buf._canMarkIdName) return;\n\n    const sourcePosition = this._buf._sourcePosition;\n    sourcePosition.identifierNamePos = pos;\n    sourcePosition.identifierName = identifierName;\n  }\n\n  _space(): void {\n    this._queue(charCodes.space);\n  }\n\n  _newline(): void {\n    this._queue(charCodes.lineFeed);\n  }\n\n  _append(str: string, maybeNewline: boolean): void {\n    this._maybeIndent(str.charCodeAt(0));\n\n    this._buf.append(str, maybeNewline);\n\n    // callers are expected to then set these to `true` when needed\n    this._endsWithWord = false;\n    this._endsWithInteger = false;\n    this._endsWithDiv = false;\n  }\n\n  _appendChar(char: number): void {\n    this._maybeIndent(char);\n\n    this._buf.appendChar(char);\n\n    // callers are expected to then set these to `true` when needed\n    this._endsWithWord = false;\n    this._endsWithInteger = false;\n    this._endsWithDiv = false;\n  }\n\n  _queue(char: number) {\n    this._maybeIndent(char);\n\n    this._buf.queue(char);\n\n    this._endsWithWord = false;\n    this._endsWithInteger = false;\n  }\n\n  _maybeIndent(firstChar: number): void {\n    // we've got a newline before us so prepend on the indentation\n    if (\n      this._indent &&\n      firstChar !== charCodes.lineFeed &&\n      this.endsWith(charCodes.lineFeed)\n    ) {\n      this._buf.queueIndentation(this._getIndent());\n    }\n  }\n\n  _shouldIndent(firstChar: number) {\n    // we've got a newline before us so prepend on the indentation\n    if (\n      this._indent &&\n      firstChar !== charCodes.lineFeed &&\n      this.endsWith(charCodes.lineFeed)\n    ) {\n      return true;\n    }\n  }\n\n  catchUp(line: number) {\n    if (!this.format.retainLines) return;\n\n    // catch up to this nodes newline if we're behind\n    const count = line - this._buf.getCurrentLine();\n\n    for (let i = 0; i < count; i++) {\n      this._newline();\n    }\n  }\n\n  _catchUp(prop: \"start\" | \"end\", loc?: Loc) {\n    if (!this.format.retainLines) return;\n\n    // catch up to this nodes newline if we're behind\n    const line = loc?.[prop]?.line;\n    if (line != null) {\n      const count = line - this._buf.getCurrentLine();\n\n      for (let i = 0; i < count; i++) {\n        this._newline();\n      }\n    }\n  }\n\n  /**\n   * Get the current indent.\n   */\n\n  _getIndent(): number {\n    return this._indentRepeat * this._indent;\n  }\n\n  printTerminatorless(node: t.Node) {\n    /**\n     * Set some state that will be modified if a newline has been inserted before any\n     * non-space characters.\n     *\n     * This is to prevent breaking semantics for terminatorless separator nodes. eg:\n     *\n     *   return foo;\n     *\n     * returns `foo`. But if we do:\n     *\n     *   return\n     *   foo;\n     *\n     *  `undefined` will be returned and not `foo` due to the terminator.\n     */\n    this._noLineTerminator = true;\n    this.print(node);\n  }\n\n  print(\n    node: t.Node | null,\n    noLineTerminatorAfter?: boolean,\n    // trailingCommentsLineOffset also used to check if called from printJoin\n    // it will be ignored if `noLineTerminatorAfter||this._noLineTerminator`\n    trailingCommentsLineOffset?: number,\n  ) {\n    if (!node) return;\n\n    this._endsWithInnerRaw = false;\n\n    const nodeType = node.type;\n    const format = this.format;\n\n    const oldConcise = format.concise;\n    if (\n      // @ts-expect-error document _compact AST properties\n      node._compact\n    ) {\n      format.concise = true;\n    }\n\n    const printMethod =\n      this[\n        nodeType as Exclude<\n          t.Node[\"type\"],\n          // removed\n          | \"Noop\"\n          // renamed\n          | t.DeprecatedAliases[\"type\"]\n        >\n      ];\n    if (printMethod === undefined) {\n      throw new ReferenceError(\n        `unknown node of type ${JSON.stringify(\n          nodeType,\n        )} with constructor ${JSON.stringify(node.constructor.name)}`,\n      );\n    }\n\n    const parent = this._currentNode;\n    this._currentNode = node;\n\n    const oldInAux = this._insideAux;\n    this._insideAux = node.loc == null;\n    this._maybeAddAuxComment(this._insideAux && !oldInAux);\n\n    const parenthesized = node.extra?.parenthesized as boolean | undefined;\n    let shouldPrintParens =\n      (parenthesized &&\n        format.retainFunctionParens &&\n        nodeType === \"FunctionExpression\") ||\n      needsParens(node, parent, this.tokenContext, this.inForStatementInit);\n\n    if (\n      !shouldPrintParens &&\n      parenthesized &&\n      node.leadingComments?.length &&\n      node.leadingComments[0].type === \"CommentBlock\"\n    ) {\n      const parentType = parent?.type;\n      switch (parentType) {\n        case \"ExpressionStatement\":\n        case \"VariableDeclarator\":\n        case \"AssignmentExpression\":\n        case \"ReturnStatement\":\n          break;\n        case \"CallExpression\":\n        case \"OptionalCallExpression\":\n        case \"NewExpression\":\n          if (parent.callee !== node) break;\n        // falls through\n        default:\n          shouldPrintParens = true;\n      }\n    }\n\n    let indentParenthesized = false;\n    if (\n      !shouldPrintParens &&\n      this._noLineTerminator &&\n      (node.leadingComments?.some(commentIsNewline) ||\n        (this.format.retainLines &&\n          node.loc &&\n          node.loc.start.line > this._buf.getCurrentLine()))\n    ) {\n      shouldPrintParens = true;\n      indentParenthesized = true;\n    }\n\n    let oldNoLineTerminatorAfterNode;\n    let oldInForStatementInitWasTrue;\n    if (!shouldPrintParens) {\n      noLineTerminatorAfter ||=\n        parent &&\n        this._noLineTerminatorAfterNode === parent &&\n        n.isLastChild(parent, node);\n      if (noLineTerminatorAfter) {\n        if (node.trailingComments?.some(commentIsNewline)) {\n          if (isExpression(node)) shouldPrintParens = true;\n        } else {\n          oldNoLineTerminatorAfterNode = this._noLineTerminatorAfterNode;\n          this._noLineTerminatorAfterNode = node;\n        }\n      }\n    }\n\n    if (shouldPrintParens) {\n      this.token(\"(\");\n      if (indentParenthesized) this.indent();\n      this._endsWithInnerRaw = false;\n      if (this.inForStatementInit) {\n        oldInForStatementInitWasTrue = true;\n        this.inForStatementInit = false;\n      }\n      oldNoLineTerminatorAfterNode = this._noLineTerminatorAfterNode;\n      this._noLineTerminatorAfterNode = null;\n    }\n\n    this._lastCommentLine = 0;\n\n    this._printLeadingComments(node, parent);\n\n    const loc = nodeType === \"Program\" || nodeType === \"File\" ? null : node.loc;\n\n    this.exactSource(\n      loc,\n      // @ts-expect-error Expected 1 arguments, but got 3.\n      printMethod.bind(this, node, parent),\n    );\n\n    if (shouldPrintParens) {\n      this._printTrailingComments(node, parent);\n      if (indentParenthesized) {\n        this.dedent();\n        this.newline();\n      }\n      this.token(\")\");\n      this._noLineTerminator = noLineTerminatorAfter;\n      if (oldInForStatementInitWasTrue) this.inForStatementInit = true;\n    } else if (noLineTerminatorAfter && !this._noLineTerminator) {\n      this._noLineTerminator = true;\n      this._printTrailingComments(node, parent);\n    } else {\n      this._printTrailingComments(node, parent, trailingCommentsLineOffset);\n    }\n\n    // end\n    this._currentNode = parent;\n    format.concise = oldConcise;\n    this._insideAux = oldInAux;\n\n    if (oldNoLineTerminatorAfterNode !== undefined) {\n      this._noLineTerminatorAfterNode = oldNoLineTerminatorAfterNode;\n    }\n\n    this._endsWithInnerRaw = false;\n  }\n\n  _maybeAddAuxComment(enteredPositionlessNode?: boolean) {\n    if (enteredPositionlessNode) this._printAuxBeforeComment();\n    if (!this._insideAux) this._printAuxAfterComment();\n  }\n\n  _printAuxBeforeComment() {\n    if (this._printAuxAfterOnNextUserNode) return;\n    this._printAuxAfterOnNextUserNode = true;\n\n    const comment = this.format.auxiliaryCommentBefore;\n    if (comment) {\n      this._printComment(\n        {\n          type: \"CommentBlock\",\n          value: comment,\n        },\n        COMMENT_SKIP_NEWLINE.DEFAULT,\n      );\n    }\n  }\n\n  _printAuxAfterComment() {\n    if (!this._printAuxAfterOnNextUserNode) return;\n    this._printAuxAfterOnNextUserNode = false;\n\n    const comment = this.format.auxiliaryCommentAfter;\n    if (comment) {\n      this._printComment(\n        {\n          type: \"CommentBlock\",\n          value: comment,\n        },\n        COMMENT_SKIP_NEWLINE.DEFAULT,\n      );\n    }\n  }\n\n  getPossibleRaw(\n    node:\n      | t.StringLiteral\n      | t.NumericLiteral\n      | t.BigIntLiteral\n      | t.DirectiveLiteral\n      | t.JSXText,\n  ): string | undefined {\n    const extra = node.extra;\n    if (\n      extra?.raw != null &&\n      extra.rawValue != null &&\n      node.value === extra.rawValue\n    ) {\n      // @ts-expect-error: The extra.raw of these AST node types must be a string\n      return extra.raw;\n    }\n  }\n\n  printJoin(\n    nodes: Array<t.Node> | undefined | null,\n    opts: PrintJoinOptions = {},\n  ) {\n    if (!nodes?.length) return;\n\n    let { indent } = opts;\n\n    if (indent == null && this.format.retainLines) {\n      const startLine = nodes[0].loc?.start.line;\n      if (startLine != null && startLine !== this._buf.getCurrentLine()) {\n        indent = true;\n      }\n    }\n\n    if (indent) this.indent();\n\n    const newlineOpts: AddNewlinesOptions = {\n      addNewlines: opts.addNewlines,\n      nextNodeStartLine: 0,\n    };\n\n    const separator = opts.separator ? opts.separator.bind(this) : null;\n\n    const len = nodes.length;\n    for (let i = 0; i < len; i++) {\n      const node = nodes[i];\n      if (!node) continue;\n\n      if (opts.statement) this._printNewline(i === 0, newlineOpts);\n\n      this.print(node, undefined, opts.trailingCommentsLineOffset || 0);\n\n      opts.iterator?.(node, i);\n\n      if (i < len - 1) separator?.();\n\n      if (opts.statement) {\n        if (!node.trailingComments?.length) {\n          this._lastCommentLine = 0;\n        }\n\n        if (i + 1 === len) {\n          this.newline(1);\n        } else {\n          const nextNode = nodes[i + 1];\n          newlineOpts.nextNodeStartLine = nextNode.loc?.start.line || 0;\n\n          this._printNewline(true, newlineOpts);\n        }\n      }\n    }\n\n    if (indent) this.dedent();\n  }\n\n  printAndIndentOnComments(node: t.Node) {\n    const indent = node.leadingComments && node.leadingComments.length > 0;\n    if (indent) this.indent();\n    this.print(node);\n    if (indent) this.dedent();\n  }\n\n  printBlock(parent: Extract<t.Node, { body: t.Statement }>) {\n    const node = parent.body;\n\n    if (node.type !== \"EmptyStatement\") {\n      this.space();\n    }\n\n    this.print(node);\n  }\n\n  _printTrailingComments(node: t.Node, parent?: t.Node, lineOffset?: number) {\n    const { innerComments, trailingComments } = node;\n    // We print inner comments here, so that if for some reason they couldn't\n    // be printed in earlier locations they are still printed *somewhere*,\n    // even if at the end of the node.\n    if (innerComments?.length) {\n      this._printComments(\n        COMMENT_TYPE.TRAILING,\n        innerComments,\n        node,\n        parent,\n        lineOffset,\n      );\n    }\n    if (trailingComments?.length) {\n      this._printComments(\n        COMMENT_TYPE.TRAILING,\n        trailingComments,\n        node,\n        parent,\n        lineOffset,\n      );\n    }\n  }\n\n  _printLeadingComments(node: t.Node, parent: t.Node) {\n    const comments = node.leadingComments;\n    if (!comments?.length) return;\n    this._printComments(COMMENT_TYPE.LEADING, comments, node, parent);\n  }\n\n  _maybePrintInnerComments() {\n    if (this._endsWithInnerRaw) this.printInnerComments();\n    this._endsWithInnerRaw = true;\n    this._indentInnerComments = true;\n  }\n\n  printInnerComments() {\n    const node = this._currentNode;\n    const comments = node.innerComments;\n    if (!comments?.length) return;\n\n    const hasSpace = this.endsWith(charCodes.space);\n    const indent = this._indentInnerComments;\n    const printedCommentsCount = this._printedComments.size;\n    if (indent) this.indent();\n    this._printComments(COMMENT_TYPE.INNER, comments, node);\n    if (hasSpace && printedCommentsCount !== this._printedComments.size) {\n      this.space();\n    }\n    if (indent) this.dedent();\n  }\n\n  noIndentInnerCommentsHere() {\n    this._indentInnerComments = false;\n  }\n\n  printSequence(nodes: t.Node[], opts: PrintSequenceOptions = {}) {\n    opts.statement = true;\n    opts.indent ??= false;\n    this.printJoin(nodes, opts);\n  }\n\n  printList(items: t.Node[], opts: PrintListOptions = {}) {\n    if (opts.separator == null) {\n      opts.separator = commaSeparator;\n    }\n\n    this.printJoin(items, opts);\n  }\n\n  _printNewline(newLine: boolean, opts: AddNewlinesOptions) {\n    const format = this.format;\n\n    // Fast path since 'this.newline' does nothing when not tracking lines.\n    if (format.retainLines || format.compact) return;\n\n    // Fast path for concise since 'this.newline' just inserts a space when\n    // concise formatting is in use.\n    if (format.concise) {\n      this.space();\n      return;\n    }\n\n    if (!newLine) {\n      return;\n    }\n\n    const startLine = opts.nextNodeStartLine;\n    const lastCommentLine = this._lastCommentLine;\n    if (startLine > 0 && lastCommentLine > 0) {\n      const offset = startLine - lastCommentLine;\n      if (offset >= 0) {\n        this.newline(offset || 1);\n        return;\n      }\n    }\n\n    // don't add newlines at the beginning of the file\n    if (this._buf.hasContent()) {\n      // Here is the logic of the original line wrapping according to the node layout, we are not using it now.\n      // We currently add at most one newline to each node in the list, ignoring `opts.addNewlines`.\n\n      // let lines = 0;\n      // if (!leading) lines++; // always include at least a single line after\n      // if (opts.addNewlines) lines += opts.addNewlines(leading, node) || 0;\n\n      // const needs = leading ? needsWhitespaceBefore : needsWhitespaceAfter;\n      // if (needs(node, parent)) lines++;\n\n      // this.newline(Math.min(2, lines));\n\n      this.newline(1);\n    }\n  }\n\n  // Returns `PRINT_COMMENT_HINT.DEFER` if the comment cannot be printed in this position due to\n  // line terminators, signaling that the print comments loop can stop and\n  // resume printing comments at the next possible position. This happens when\n  // printing inner comments, since if we have an inner comment with a multiline\n  // there is at least one inner position where line terminators are allowed.\n  _shouldPrintComment(comment: t.Comment): PRINT_COMMENT_HINT {\n    // Some plugins (such as flow-strip-types) use this to mark comments as removed using the AST-root 'comments' property,\n    // where they can't manually mutate the AST node comment lists.\n    if (comment.ignore) return PRINT_COMMENT_HINT.SKIP;\n\n    if (this._printedComments.has(comment)) return PRINT_COMMENT_HINT.SKIP;\n\n    if (\n      this._noLineTerminator &&\n      HAS_NEWLINE_OR_BlOCK_COMMENT_END.test(comment.value)\n    ) {\n      return PRINT_COMMENT_HINT.DEFER;\n    }\n\n    this._printedComments.add(comment);\n\n    if (!this.format.shouldPrintComment(comment.value)) {\n      return PRINT_COMMENT_HINT.SKIP;\n    }\n\n    return PRINT_COMMENT_HINT.ALLOW;\n  }\n\n  _printComment(comment: t.Comment, skipNewLines: COMMENT_SKIP_NEWLINE) {\n    const noLineTerminator = this._noLineTerminator;\n    const isBlockComment = comment.type === \"CommentBlock\";\n\n    // Add a newline before and after a block comment, unless explicitly\n    // disallowed\n    const printNewLines =\n      isBlockComment &&\n      skipNewLines !== COMMENT_SKIP_NEWLINE.ALL &&\n      !this._noLineTerminator;\n\n    if (\n      printNewLines &&\n      this._buf.hasContent() &&\n      skipNewLines !== COMMENT_SKIP_NEWLINE.LEADING\n    ) {\n      this.newline(1);\n    }\n\n    const lastCharCode = this.getLastChar();\n    if (\n      lastCharCode !== charCodes.leftSquareBracket &&\n      lastCharCode !== charCodes.leftCurlyBrace &&\n      lastCharCode !== charCodes.leftParenthesis\n    ) {\n      this.space();\n    }\n\n    let val;\n    if (isBlockComment) {\n      val = `/*${comment.value}*/`;\n      if (this.format.indent.adjustMultilineComment) {\n        const offset = comment.loc?.start.column;\n        if (offset) {\n          const newlineRegex = new RegExp(\"\\\\n\\\\s{1,\" + offset + \"}\", \"g\");\n          val = val.replace(newlineRegex, \"\\n\");\n        }\n        if (this.format.concise) {\n          val = val.replace(/\\n(?!$)/g, `\\n`);\n        } else {\n          let indentSize = this.format.retainLines\n            ? 0\n            : this._buf.getCurrentColumn();\n\n          if (this._shouldIndent(charCodes.slash) || this.format.retainLines) {\n            indentSize += this._getIndent();\n          }\n\n          val = val.replace(/\\n(?!$)/g, `\\n${\" \".repeat(indentSize)}`);\n        }\n      }\n    } else if (!noLineTerminator) {\n      val = `//${comment.value}`;\n    } else {\n      // It was a single-line comment, so it's guaranteed to not\n      // contain newlines and it can be safely printed as a block\n      // comment.\n      val = `/*${comment.value}*/`;\n    }\n\n    // Avoid converting a / operator into a line comment by appending /* to it\n    if (this._endsWithDiv) this._space();\n\n    this.source(\"start\", comment.loc);\n    this._append(val, isBlockComment);\n\n    if (!isBlockComment && !noLineTerminator) {\n      this.newline(1, true);\n    }\n\n    if (printNewLines && skipNewLines !== COMMENT_SKIP_NEWLINE.TRAILING) {\n      this.newline(1);\n    }\n  }\n\n  _printComments(\n    type: COMMENT_TYPE,\n    comments: readonly t.Comment[],\n    node: t.Node,\n    parent?: t.Node,\n    lineOffset: number = 0,\n  ) {\n    const nodeLoc = node.loc;\n    const len = comments.length;\n    let hasLoc = !!nodeLoc;\n    const nodeStartLine = hasLoc ? nodeLoc.start.line : 0;\n    const nodeEndLine = hasLoc ? nodeLoc.end.line : 0;\n    let lastLine = 0;\n    let leadingCommentNewline = 0;\n\n    const maybeNewline = this._noLineTerminator\n      ? function () {}\n      : this.newline.bind(this);\n\n    for (let i = 0; i < len; i++) {\n      const comment = comments[i];\n\n      const shouldPrint = this._shouldPrintComment(comment);\n      if (shouldPrint === PRINT_COMMENT_HINT.DEFER) {\n        hasLoc = false;\n        break;\n      }\n      if (hasLoc && comment.loc && shouldPrint === PRINT_COMMENT_HINT.ALLOW) {\n        const commentStartLine = comment.loc.start.line;\n        const commentEndLine = comment.loc.end.line;\n        if (type === COMMENT_TYPE.LEADING) {\n          let offset = 0;\n          if (i === 0) {\n            // Because currently we cannot handle blank lines before leading comments,\n            // we always wrap before and after multi-line comments.\n            if (\n              this._buf.hasContent() &&\n              (comment.type === \"CommentLine\" ||\n                commentStartLine !== commentEndLine)\n            ) {\n              offset = leadingCommentNewline = 1;\n            }\n          } else {\n            offset = commentStartLine - lastLine;\n          }\n          lastLine = commentEndLine;\n\n          maybeNewline(offset);\n          this._printComment(comment, COMMENT_SKIP_NEWLINE.ALL);\n\n          if (i + 1 === len) {\n            maybeNewline(\n              Math.max(nodeStartLine - lastLine, leadingCommentNewline),\n            );\n            lastLine = nodeStartLine;\n          }\n        } else if (type === COMMENT_TYPE.INNER) {\n          const offset =\n            commentStartLine - (i === 0 ? nodeStartLine : lastLine);\n          lastLine = commentEndLine;\n\n          maybeNewline(offset);\n          this._printComment(comment, COMMENT_SKIP_NEWLINE.ALL);\n\n          if (i + 1 === len) {\n            maybeNewline(Math.min(1, nodeEndLine - lastLine)); // TODO: Improve here when inner comments processing is stronger\n            lastLine = nodeEndLine;\n          }\n        } else {\n          const offset =\n            commentStartLine - (i === 0 ? nodeEndLine - lineOffset : lastLine);\n          lastLine = commentEndLine;\n\n          maybeNewline(offset);\n          this._printComment(comment, COMMENT_SKIP_NEWLINE.ALL);\n        }\n      } else {\n        hasLoc = false;\n        if (shouldPrint !== PRINT_COMMENT_HINT.ALLOW) {\n          continue;\n        }\n\n        if (len === 1) {\n          const singleLine = comment.loc\n            ? comment.loc.start.line === comment.loc.end.line\n            : !HAS_NEWLINE.test(comment.value);\n\n          const shouldSkipNewline =\n            singleLine &&\n            !isStatement(node) &&\n            !isClassBody(parent) &&\n            !isTSInterfaceBody(parent) &&\n            !isTSEnumDeclaration(parent);\n\n          if (type === COMMENT_TYPE.LEADING) {\n            this._printComment(\n              comment,\n              (shouldSkipNewline && node.type !== \"ObjectExpression\") ||\n                (singleLine && isFunction(parent, { body: node }))\n                ? COMMENT_SKIP_NEWLINE.ALL\n                : COMMENT_SKIP_NEWLINE.DEFAULT,\n            );\n          } else if (shouldSkipNewline && type === COMMENT_TYPE.TRAILING) {\n            this._printComment(comment, COMMENT_SKIP_NEWLINE.ALL);\n          } else {\n            this._printComment(comment, COMMENT_SKIP_NEWLINE.DEFAULT);\n          }\n        } else if (\n          type === COMMENT_TYPE.INNER &&\n          !(node.type === \"ObjectExpression\" && node.properties.length > 1) &&\n          node.type !== \"ClassBody\" &&\n          node.type !== \"TSInterfaceBody\"\n        ) {\n          // class X {\n          //   /*:: a: number*/\n          //   /*:: b: ?string*/\n          // }\n\n          this._printComment(\n            comment,\n            i === 0\n              ? COMMENT_SKIP_NEWLINE.LEADING\n              : i === len - 1\n                ? COMMENT_SKIP_NEWLINE.TRAILING\n                : COMMENT_SKIP_NEWLINE.DEFAULT,\n          );\n        } else {\n          this._printComment(comment, COMMENT_SKIP_NEWLINE.DEFAULT);\n        }\n      }\n    }\n\n    if (type === COMMENT_TYPE.TRAILING && hasLoc && lastLine) {\n      this._lastCommentLine = lastLine;\n    }\n  }\n}\n\n// Expose the node type functions and helpers on the prototype for easy usage.\nObject.assign(Printer.prototype, generatorFunctions);\n\nif (!process.env.BABEL_8_BREAKING) {\n  // @ts-ignore(Babel 7 vs Babel 8) Babel 7 has Noop print method\n  Printer.prototype.Noop = function Noop(this: Printer) {};\n}\n\ntype GeneratorFunctions = typeof generatorFunctions;\n// eslint-disable-next-line @typescript-eslint/no-empty-object-type\ninterface Printer extends GeneratorFunctions {}\nexport default Printer;\n\nfunction commaSeparator(this: Printer) {\n  this.token(\",\");\n  this.space();\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,OAAA,GAAAC,OAAA;AAEA,IAAAC,CAAA,GAAAD,OAAA;AAEA,IAAAE,EAAA,GAAAF,OAAA;AAWA,IAAAG,kBAAA,GAAAH,OAAA;AAA4D;EAV1DI,YAAY;EACZC,UAAU;EACVC,WAAW;EACXC,WAAW;EACXC,iBAAiB;EACjBC;AAAmB,IAAAP,EAAA;AAarB,MAAMQ,mBAAmB,GAAG,IAAI;AAChC,MAAMC,oBAAoB,GAAG,OAAO;AACpC,MAAMC,WAAW,GAAG,oBAAoB;AACxC,MAAMC,gCAAgC,GAAG,yBAAyB;AAElE,SAASC,gBAAgBA,CAACC,CAAY,EAAE;EACtC,OAAOA,CAAC,CAACC,IAAI,KAAK,aAAa,IAAIJ,WAAW,CAACK,IAAI,CAACF,CAAC,CAACG,KAAK,CAAC;AAC9D;AAEA,MAAM;EAAEC;AAAY,CAAC,GAAGlB,CAAC;AAiFzB,MAAMmB,OAAO,CAAC;EACZC,WAAWA,CAACC,MAAc,EAAEC,GAAc,EAAE;IAAA,KAa5CC,kBAAkB,GAAY,KAAK;IAAA,KA0BnCC,YAAY,GAAW,CAAC;IAAA,KAGxBC,YAAY,GAAW,IAAI;IAAA,KAC3BC,OAAO,GAAW,CAAC;IAAA,KACnBC,aAAa,GAAW,CAAC;IAAA,KACzBC,UAAU,GAAY,KAAK;IAAA,KAC3BC,iBAAiB,GAAY,KAAK;IAAA,KAClCC,0BAA0B,GAAkB,IAAI;IAAA,KAChDC,4BAA4B,GAAY,KAAK;IAAA,KAC7CC,gBAAgB,GAAG,IAAIC,GAAG,CAAY,CAAC;IAAA,KACvCC,gBAAgB,GAAG,KAAK;IAAA,KACxBC,aAAa,GAAG,KAAK;IAAA,KACrBC,YAAY,GAAG,KAAK;IAAA,KACpBC,gBAAgB,GAAG,CAAC;IAAA,KACpBC,iBAAiB,GAAY,KAAK;IAAA,KAClCC,oBAAoB,GAAY,IAAI;IAtDlC,IAAI,CAAClB,MAAM,GAAGA,MAAM;IAEpB,IAAI,CAACM,aAAa,GAAGN,MAAM,CAACmB,MAAM,CAACC,KAAK,CAACC,MAAM;IAE/C,IAAI,CAACC,SAAS,GAAGrB,GAAG,oBAAHA,GAAG,CAAEqB,SAAS;IAE/B,IAAI,CAACC,IAAI,GAAG,IAAIC,eAAM,CAACvB,GAAG,EAAED,MAAM,CAACmB,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC;EACrD;EAMAK,qBAAqBA,CAAA,EAAG;IACtB,IAAI,IAAI,CAACvB,kBAAkB,EAAE,OAAO,MAAM,CAAC,CAAC;IAC5C,IAAI,CAACA,kBAAkB,GAAG,IAAI;IAC9B,OAAO,MAAM;MACX,IAAI,CAACA,kBAAkB,GAAG,KAAK;IACjC,CAAC;EACH;EAEAwB,cAAcA,CAAA,EAAG;IACf,MAAMC,qBAAqB,GAAG,IAAI,CAACzB,kBAAkB;IACrD,MAAM0B,4BAA4B,GAAG,IAAI,CAACnB,0BAA0B;IACpE,IACEkB,qBAAqB,KAAK,KAAK,IAC/BC,4BAA4B,KAAK,IAAI,EACrC;MACA,OAAO,MAAM,CAAC,CAAC;IACjB;IACA,IAAI,CAAC1B,kBAAkB,GAAG,KAAK;IAC/B,IAAI,CAACO,0BAA0B,GAAG,IAAI;IACtC,OAAO,MAAM;MACX,IAAI,CAACP,kBAAkB,GAAGyB,qBAAqB;MAC/C,IAAI,CAAClB,0BAA0B,GAAGmB,4BAA4B;IAChE,CAAC;EACH;EAoBAC,QAAQA,CAACC,GAAW,EAAE;IACpB,IAAI,CAACC,KAAK,CAACD,GAAG,CAAC;IACf,IAAI,CAACE,mBAAmB,CAAC,CAAC;IAE1B,OAAO,IAAI,CAACT,IAAI,CAACU,GAAG,CAAC,CAAC;EACxB;EAMAd,MAAMA,CAAA,EAAS;IACb,IAAI,IAAI,CAACnB,MAAM,CAACkC,OAAO,IAAI,IAAI,CAAClC,MAAM,CAACmC,OAAO,EAAE;IAEhD,IAAI,CAAC9B,OAAO,EAAE;EAChB;EAMA+B,MAAMA,CAAA,EAAS;IACb,IAAI,IAAI,CAACpC,MAAM,CAACkC,OAAO,IAAI,IAAI,CAAClC,MAAM,CAACmC,OAAO,EAAE;IAEhD,IAAI,CAAC9B,OAAO,EAAE;EAChB;EAMAgC,SAASA,CAACC,KAAc,GAAG,KAAK,EAAQ;IACtC,IAAI,CAACN,mBAAmB,CAAC,CAAC;IAC1B,IAAIM,KAAK,EAAE;MACT,IAAI,CAACC,WAAW,GAAoB,CAAC;IACvC,CAAC,MAAM;MACL,IAAI,CAACC,MAAM,GAAoB,CAAC;IAClC;IACA,IAAI,CAAChC,iBAAiB,GAAG,KAAK;EAChC;EAMAiC,UAAUA,CAACC,IAAY,EAAQ;IAC7B,IAAI,IAAI,CAAC1C,MAAM,CAAC2C,QAAQ,EAAE;MACxB,IAAI,CAACpB,IAAI,CAACqB,mBAAmB,CAAC,CAAC;IACjC;IACA,IAAI,CAACC,gBAAgB,CAAC,KAAK,EAAEH,IAAI,CAACI,GAAG,EAAE,CAAC,CAAC,CAAC;IAC1C,IAAI,CAACC,SAAK,IAAI,CAAC;EACjB;EAEAC,WAAWA,CAACN,IAAY,EAAQ;IAC9B,IAAI,CAACG,gBAAgB,CAAC,KAAK,EAAEH,IAAI,CAACI,GAAG,EAAE,CAAC,CAAC,CAAC;IAC1C,IAAI,CAACC,SAAK,GAAI,CAAC;EACjB;EAMAE,KAAKA,CAACX,KAAc,GAAG,KAAK,EAAQ;IAClC,IAAI,IAAI,CAACtC,MAAM,CAACkC,OAAO,EAAE;IAEzB,IAAII,KAAK,EAAE;MACT,IAAI,CAACY,MAAM,CAAC,CAAC;IACf,CAAC,MAAM,IAAI,IAAI,CAAC3B,IAAI,CAAC4B,UAAU,CAAC,CAAC,EAAE;MACjC,MAAMC,MAAM,GAAG,IAAI,CAACC,WAAW,CAAC,CAAC;MACjC,IAAID,MAAM,OAAoB,IAAIA,MAAM,OAAuB,EAAE;QAC/D,IAAI,CAACF,MAAM,CAAC,CAAC;MACf;IACF;EACF;EAMAI,IAAIA,CAACC,GAAW,EAAEC,qBAA8B,GAAG,KAAK,EAAQ;IAC9D,IAAI,CAACrD,YAAY,GAAG,CAAC;IAErB,IAAI,CAACsD,wBAAwB,CAAC,CAAC;IAG/B,IACE,IAAI,CAAC3C,aAAa,IACjB,IAAI,CAACC,YAAY,IAAIwC,GAAG,CAACG,UAAU,CAAC,CAAC,CAAC,OAAqB,EAC5D;MACA,IAAI,CAACR,MAAM,CAAC,CAAC;IACf;IAEA,IAAI,CAAClB,mBAAmB,CAAC,CAAC;IAC1B,IAAI,CAAC2B,OAAO,CAACJ,GAAG,EAAE,KAAK,CAAC;IAExB,IAAI,CAACzC,aAAa,GAAG,IAAI;IACzB,IAAI,CAACN,iBAAiB,GAAGgD,qBAAqB;EAChD;EAMAI,MAAMA,CAACL,GAAW,EAAEK,MAAe,EAAQ;IAEzC,SAASC,mBAAmBA,CAACN,GAAW,EAAE;MACxC,IAAIA,GAAG,CAAClC,MAAM,GAAG,CAAC,IAAIkC,GAAG,CAACG,UAAU,CAAC,CAAC,CAAC,OAAqB,EAAE;QAC5D,MAAMI,UAAU,GAAGP,GAAG,CAACG,UAAU,CAAC,CAAC,CAAC;QACpC,OACEI,UAAU,OAAyB,IACnCA,UAAU,QAAyB,IACnCA,UAAU,QAAyB;MAEvC;MACA,OAAO,KAAK;IACd;IACA,IAAI,CAACR,IAAI,CAACC,GAAG,CAAC;IAId,IAAI,CAAC1C,gBAAgB,GACnBkD,MAAM,CAACC,SAAS,CAACJ,MAAM,CAAC,IACxB,CAACC,mBAAmB,CAACN,GAAG,CAAC,IACzB,CAACnE,mBAAmB,CAACO,IAAI,CAAC4D,GAAG,CAAC,IAC9B,CAAClE,oBAAoB,CAACM,IAAI,CAAC4D,GAAG,CAAC,IAC/BA,GAAG,CAACG,UAAU,CAACH,GAAG,CAAClC,MAAM,GAAG,CAAC,CAAC,OAAkB;EACpD;EAKA0B,KAAKA,CAACQ,GAAW,EAAEU,YAAY,GAAG,KAAK,EAAQ;IAC7C,IAAI,CAAC9D,YAAY,GAAG,CAAC;IAErB,IAAI,CAACsD,wBAAwB,CAAC,CAAC;IAE/B,MAAMS,QAAQ,GAAG,IAAI,CAACb,WAAW,CAAC,CAAC;IACnC,MAAMc,QAAQ,GAAGZ,GAAG,CAACG,UAAU,CAAC,CAAC,CAAC;IAClC,IACGQ,QAAQ,OAA8B,KAGpCX,GAAG,KAAK,IAAI,IAEXY,QAAQ,OAAuB,CAAC,IAEnCA,QAAQ,OAAuB,IAAID,QAAQ,OAAwB,IACnEC,QAAQ,OAAmB,IAAID,QAAQ,OAAoB,IAE3DC,QAAQ,OAAkB,IAAI,IAAI,CAACtD,gBAAiB,EACrD;MACA,IAAI,CAACqC,MAAM,CAAC,CAAC;IACf;IAEA,IAAI,CAAClB,mBAAmB,CAAC,CAAC;IAC1B,IAAI,CAAC2B,OAAO,CAACJ,GAAG,EAAEU,YAAY,CAAC;IAC/B,IAAI,CAACzD,iBAAiB,GAAG,KAAK;EAChC;EAEA4D,SAASA,CAACC,IAAY,EAAQ;IAC5B,IAAI,CAAClE,YAAY,GAAG,CAAC;IAErB,IAAI,CAACsD,wBAAwB,CAAC,CAAC;IAE/B,MAAMS,QAAQ,GAAG,IAAI,CAACb,WAAW,CAAC,CAAC;IACnC,IAEGgB,IAAI,OAAuB,IAAIH,QAAQ,OAAuB,IAC9DG,IAAI,OAAmB,IAAIH,QAAQ,OAAoB,IAEvDG,IAAI,OAAkB,IAAI,IAAI,CAACxD,gBAAiB,EACjD;MACA,IAAI,CAACqC,MAAM,CAAC,CAAC;IACf;IAEA,IAAI,CAAClB,mBAAmB,CAAC,CAAC;IAC1B,IAAI,CAACO,WAAW,CAAC8B,IAAI,CAAC;IACtB,IAAI,CAAC7D,iBAAiB,GAAG,KAAK;EAChC;EAQA8D,OAAOA,CAACC,CAAS,GAAG,CAAC,EAAEjC,KAAe,EAAQ;IAC5C,IAAIiC,CAAC,IAAI,CAAC,EAAE;IAEZ,IAAI,CAACjC,KAAK,EAAE;MACV,IAAI,IAAI,CAACtC,MAAM,CAACwE,WAAW,IAAI,IAAI,CAACxE,MAAM,CAACkC,OAAO,EAAE;MAEpD,IAAI,IAAI,CAAClC,MAAM,CAACmC,OAAO,EAAE;QACvB,IAAI,CAACc,KAAK,CAAC,CAAC;QACZ;MACF;IACF;IAEA,IAAIsB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC;IAEhBA,CAAC,IAAI,IAAI,CAAChD,IAAI,CAACkD,eAAe,CAAC,CAAC;IAEhC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,CAAC,EAAEG,CAAC,EAAE,EAAE;MAC1B,IAAI,CAACC,QAAQ,CAAC,CAAC;IACjB;IAEA;EACF;EAEAC,QAAQA,CAACP,IAAY,EAAW;IAC9B,OAAO,IAAI,CAAChB,WAAW,CAAC,CAAC,KAAKgB,IAAI;EACpC;EAEAhB,WAAWA,CAAA,EAAW;IACpB,OAAO,IAAI,CAAC9B,IAAI,CAAC8B,WAAW,CAAC,CAAC;EAChC;EAEAwB,sBAAsBA,CAAA,EAAW;IAC/B,OAAO,IAAI,CAACtD,IAAI,CAACsD,sBAAsB,CAAC,CAAC;EAC3C;EAEAC,qBAAqBA,CAAA,EAAS;IAC5B,IAAI,CAACvD,IAAI,CAACuD,qBAAqB,CAAC,CAAC;EACnC;EAEAC,WAAWA,CAACjC,GAAoB,EAAEkC,EAAc,EAAE;IAChD,IAAI,CAAClC,GAAG,EAAE;MACRkC,EAAE,CAAC,CAAC;MACJ;IACF;IAEA,IAAI,CAACC,QAAQ,CAAC,OAAO,EAAEnC,GAAG,CAAC;IAE3B,IAAI,CAACvB,IAAI,CAACwD,WAAW,CAACjC,GAAG,EAAEkC,EAAE,CAAC;EAChC;EAEAE,MAAMA,CAACC,IAAqB,EAAErC,GAAoB,EAAQ;IACxD,IAAI,CAACA,GAAG,EAAE;IAEV,IAAI,CAACmC,QAAQ,CAACE,IAAI,EAAErC,GAAG,CAAC;IAExB,IAAI,CAACvB,IAAI,CAAC2D,MAAM,CAACC,IAAI,EAAErC,GAAG,CAAC;EAC7B;EAEAD,gBAAgBA,CACdsC,IAAqB,EACrBrC,GAAoB,EACpBsC,YAAoB,EACd;IACN,IAAI,CAACtC,GAAG,EAAE;IAEV,IAAI,CAACmC,QAAQ,CAACE,IAAI,EAAErC,GAAG,CAAC;IAExB,IAAI,CAACvB,IAAI,CAACsB,gBAAgB,CAACsC,IAAI,EAAErC,GAAG,EAAEsC,YAAY,CAAC;EACrD;EAEAC,oBAAoBA,CAACC,cAAsB,EAAEC,GAAS,EAAQ;IAC5D,IAAI,CAAC,IAAI,CAAChE,IAAI,CAACiE,cAAc,EAAE;IAE/B,MAAMC,cAAc,GAAG,IAAI,CAAClE,IAAI,CAACmE,eAAe;IAChDD,cAAc,CAACE,iBAAiB,GAAGJ,GAAG;IACtCE,cAAc,CAACH,cAAc,GAAGA,cAAc;EAChD;EAEApC,MAAMA,CAAA,EAAS;IACb,IAAI,CAACV,MAAM,GAAgB,CAAC;EAC9B;EAEAmC,QAAQA,CAAA,EAAS;IACf,IAAI,CAACnC,MAAM,GAAmB,CAAC;EACjC;EAEAmB,OAAOA,CAACJ,GAAW,EAAEU,YAAqB,EAAQ;IAChD,IAAI,CAAC2B,YAAY,CAACrC,GAAG,CAACG,UAAU,CAAC,CAAC,CAAC,CAAC;IAEpC,IAAI,CAACnC,IAAI,CAACsE,MAAM,CAACtC,GAAG,EAAEU,YAAY,CAAC;IAGnC,IAAI,CAACnD,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACD,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACE,YAAY,GAAG,KAAK;EAC3B;EAEAwB,WAAWA,CAAC8B,IAAY,EAAQ;IAC9B,IAAI,CAACuB,YAAY,CAACvB,IAAI,CAAC;IAEvB,IAAI,CAAC9C,IAAI,CAACuE,UAAU,CAACzB,IAAI,CAAC;IAG1B,IAAI,CAACvD,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACD,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACE,YAAY,GAAG,KAAK;EAC3B;EAEAyB,MAAMA,CAAC6B,IAAY,EAAE;IACnB,IAAI,CAACuB,YAAY,CAACvB,IAAI,CAAC;IAEvB,IAAI,CAAC9C,IAAI,CAACwE,KAAK,CAAC1B,IAAI,CAAC;IAErB,IAAI,CAACvD,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACD,gBAAgB,GAAG,KAAK;EAC/B;EAEA+E,YAAYA,CAACI,SAAiB,EAAQ;IAEpC,IACE,IAAI,CAAC3F,OAAO,IACZ2F,SAAS,OAAuB,IAChC,IAAI,CAACpB,QAAQ,GAAmB,CAAC,EACjC;MACA,IAAI,CAACrD,IAAI,CAAC0E,gBAAgB,CAAC,IAAI,CAACC,UAAU,CAAC,CAAC,CAAC;IAC/C;EACF;EAEAC,aAAaA,CAACH,SAAiB,EAAE;IAE/B,IACE,IAAI,CAAC3F,OAAO,IACZ2F,SAAS,OAAuB,IAChC,IAAI,CAACpB,QAAQ,GAAmB,CAAC,EACjC;MACA,OAAO,IAAI;IACb;EACF;EAEAwB,OAAOA,CAACC,IAAY,EAAE;IACpB,IAAI,CAAC,IAAI,CAACrG,MAAM,CAACwE,WAAW,EAAE;IAG9B,MAAM8B,KAAK,GAAGD,IAAI,GAAG,IAAI,CAAC9E,IAAI,CAACgF,cAAc,CAAC,CAAC;IAE/C,KAAK,IAAIhC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG+B,KAAK,EAAE/B,CAAC,EAAE,EAAE;MAC9B,IAAI,CAACI,QAAQ,CAAC,CAAC;IACjB;EACF;EAEAM,QAAQA,CAACE,IAAqB,EAAErC,GAAS,EAAE;IAAA,IAAA0D,SAAA;IACzC,IAAI,CAAC,IAAI,CAACxG,MAAM,CAACwE,WAAW,EAAE;IAG9B,MAAM6B,IAAI,GAAGvD,GAAG,aAAA0D,SAAA,GAAH1D,GAAG,CAAGqC,IAAI,CAAC,qBAAXqB,SAAA,CAAaH,IAAI;IAC9B,IAAIA,IAAI,IAAI,IAAI,EAAE;MAChB,MAAMC,KAAK,GAAGD,IAAI,GAAG,IAAI,CAAC9E,IAAI,CAACgF,cAAc,CAAC,CAAC;MAE/C,KAAK,IAAIhC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG+B,KAAK,EAAE/B,CAAC,EAAE,EAAE;QAC9B,IAAI,CAACI,QAAQ,CAAC,CAAC;MACjB;IACF;EACF;EAMAuB,UAAUA,CAAA,EAAW;IACnB,OAAO,IAAI,CAAC5F,aAAa,GAAG,IAAI,CAACD,OAAO;EAC1C;EAEAoG,mBAAmBA,CAAC/D,IAAY,EAAE;IAgBhC,IAAI,CAAClC,iBAAiB,GAAG,IAAI;IAC7B,IAAI,CAACuB,KAAK,CAACW,IAAI,CAAC;EAClB;EAEAX,KAAKA,CACHW,IAAmB,EACnBc,qBAA+B,EAG/BkD,0BAAmC,EACnC;IAAA,IAAAC,WAAA,EAAAC,qBAAA,EAAAC,sBAAA;IACA,IAAI,CAACnE,IAAI,EAAE;IAEX,IAAI,CAACzB,iBAAiB,GAAG,KAAK;IAE9B,MAAM6F,QAAQ,GAAGpE,IAAI,CAAChD,IAAI;IAC1B,MAAMM,MAAM,GAAG,IAAI,CAACA,MAAM;IAE1B,MAAM+G,UAAU,GAAG/G,MAAM,CAACmC,OAAO;IACjC,IAEEO,IAAI,CAACsE,QAAQ,EACb;MACAhH,MAAM,CAACmC,OAAO,GAAG,IAAI;IACvB;IAEA,MAAM8E,WAAW,GACf,IAAI,CACFH,QAAQ,CAOT;IACH,IAAIG,WAAW,KAAKC,SAAS,EAAE;MAC7B,MAAM,IAAIC,cAAc,CACtB,wBAAwBC,IAAI,CAACC,SAAS,CACpCP,QACF,CAAC,qBAAqBM,IAAI,CAACC,SAAS,CAAC3E,IAAI,CAAC3C,WAAW,CAACuH,IAAI,CAAC,EAC7D,CAAC;IACH;IAEA,MAAMC,MAAM,GAAG,IAAI,CAACnH,YAAY;IAChC,IAAI,CAACA,YAAY,GAAGsC,IAAI;IAExB,MAAM8E,QAAQ,GAAG,IAAI,CAACjH,UAAU;IAChC,IAAI,CAACA,UAAU,GAAGmC,IAAI,CAACI,GAAG,IAAI,IAAI;IAClC,IAAI,CAACd,mBAAmB,CAAC,IAAI,CAACzB,UAAU,IAAI,CAACiH,QAAQ,CAAC;IAEtD,MAAMC,aAAa,IAAAd,WAAA,GAAGjE,IAAI,CAACgF,KAAK,qBAAVf,WAAA,CAAYc,aAAoC;IACtE,IAAIE,iBAAiB,GAClBF,aAAa,IACZzH,MAAM,CAAC4H,oBAAoB,IAC3Bd,QAAQ,KAAK,oBAAoB,IACnCjH,WAAW,CAAC6C,IAAI,EAAE6E,MAAM,EAAE,IAAI,CAACpH,YAAY,EAAE,IAAI,CAACD,kBAAkB,CAAC;IAEvE,IACE,CAACyH,iBAAiB,IAClBF,aAAa,KAAAb,qBAAA,GACblE,IAAI,CAACmF,eAAe,aAApBjB,qBAAA,CAAsBvF,MAAM,IAC5BqB,IAAI,CAACmF,eAAe,CAAC,CAAC,CAAC,CAACnI,IAAI,KAAK,cAAc,EAC/C;MACA,MAAMoI,UAAU,GAAGP,MAAM,oBAANA,MAAM,CAAE7H,IAAI;MAC/B,QAAQoI,UAAU;QAChB,KAAK,qBAAqB;QAC1B,KAAK,oBAAoB;QACzB,KAAK,sBAAsB;QAC3B,KAAK,iBAAiB;UACpB;QACF,KAAK,gBAAgB;QACrB,KAAK,wBAAwB;QAC7B,KAAK,eAAe;UAClB,IAAIP,MAAM,CAACQ,MAAM,KAAKrF,IAAI,EAAE;QAE9B;UACEiF,iBAAiB,GAAG,IAAI;MAC5B;IACF;IAEA,IAAIK,mBAAmB,GAAG,KAAK;IAC/B,IACE,CAACL,iBAAiB,IAClB,IAAI,CAACnH,iBAAiB,KACrB,CAAAqG,sBAAA,GAAAnE,IAAI,CAACmF,eAAe,aAApBhB,sBAAA,CAAsBoB,IAAI,CAACzI,gBAAgB,CAAC,IAC1C,IAAI,CAACQ,MAAM,CAACwE,WAAW,IACtB9B,IAAI,CAACI,GAAG,IACRJ,IAAI,CAACI,GAAG,CAACoF,KAAK,CAAC7B,IAAI,GAAG,IAAI,CAAC9E,IAAI,CAACgF,cAAc,CAAC,CAAE,CAAC,EACtD;MACAoB,iBAAiB,GAAG,IAAI;MACxBK,mBAAmB,GAAG,IAAI;IAC5B;IAEA,IAAIpG,4BAA4B;IAChC,IAAIuG,4BAA4B;IAChC,IAAI,CAACR,iBAAiB,EAAE;MACtBnE,qBAAqB,KAArBA,qBAAqB,GACnB+D,MAAM,IACN,IAAI,CAAC9G,0BAA0B,KAAK8G,MAAM,IAC1C5I,CAAC,CAACyJ,WAAW,CAACb,MAAM,EAAE7E,IAAI,CAAC;MAC7B,IAAIc,qBAAqB,EAAE;QAAA,IAAA6E,qBAAA;QACzB,KAAAA,qBAAA,GAAI3F,IAAI,CAAC4F,gBAAgB,aAArBD,qBAAA,CAAuBJ,IAAI,CAACzI,gBAAgB,CAAC,EAAE;UACjD,IAAIV,YAAY,CAAC4D,IAAI,CAAC,EAAEiF,iBAAiB,GAAG,IAAI;QAClD,CAAC,MAAM;UACL/F,4BAA4B,GAAG,IAAI,CAACnB,0BAA0B;UAC9D,IAAI,CAACA,0BAA0B,GAAGiC,IAAI;QACxC;MACF;IACF;IAEA,IAAIiF,iBAAiB,EAAE;MACrB,IAAI,CAAC5E,SAAK,GAAI,CAAC;MACf,IAAIiF,mBAAmB,EAAE,IAAI,CAAC7G,MAAM,CAAC,CAAC;MACtC,IAAI,CAACF,iBAAiB,GAAG,KAAK;MAC9B,IAAI,IAAI,CAACf,kBAAkB,EAAE;QAC3BiI,4BAA4B,GAAG,IAAI;QACnC,IAAI,CAACjI,kBAAkB,GAAG,KAAK;MACjC;MACA0B,4BAA4B,GAAG,IAAI,CAACnB,0BAA0B;MAC9D,IAAI,CAACA,0BAA0B,GAAG,IAAI;IACxC;IAEA,IAAI,CAACO,gBAAgB,GAAG,CAAC;IAEzB,IAAI,CAACuH,qBAAqB,CAAC7F,IAAI,EAAE6E,MAAM,CAAC;IAExC,MAAMzE,GAAG,GAAGgE,QAAQ,KAAK,SAAS,IAAIA,QAAQ,KAAK,MAAM,GAAG,IAAI,GAAGpE,IAAI,CAACI,GAAG;IAE3E,IAAI,CAACiC,WAAW,CACdjC,GAAG,EAEHmE,WAAW,CAACuB,IAAI,CAAC,IAAI,EAAE9F,IAAI,EAAE6E,MAAM,CACrC,CAAC;IAED,IAAII,iBAAiB,EAAE;MACrB,IAAI,CAACc,sBAAsB,CAAC/F,IAAI,EAAE6E,MAAM,CAAC;MACzC,IAAIS,mBAAmB,EAAE;QACvB,IAAI,CAAC5F,MAAM,CAAC,CAAC;QACb,IAAI,CAACkC,OAAO,CAAC,CAAC;MAChB;MACA,IAAI,CAACvB,SAAK,GAAI,CAAC;MACf,IAAI,CAACvC,iBAAiB,GAAGgD,qBAAqB;MAC9C,IAAI2E,4BAA4B,EAAE,IAAI,CAACjI,kBAAkB,GAAG,IAAI;IAClE,CAAC,MAAM,IAAIsD,qBAAqB,IAAI,CAAC,IAAI,CAAChD,iBAAiB,EAAE;MAC3D,IAAI,CAACA,iBAAiB,GAAG,IAAI;MAC7B,IAAI,CAACiI,sBAAsB,CAAC/F,IAAI,EAAE6E,MAAM,CAAC;IAC3C,CAAC,MAAM;MACL,IAAI,CAACkB,sBAAsB,CAAC/F,IAAI,EAAE6E,MAAM,EAAEb,0BAA0B,CAAC;IACvE;IAGA,IAAI,CAACtG,YAAY,GAAGmH,MAAM;IAC1BvH,MAAM,CAACmC,OAAO,GAAG4E,UAAU;IAC3B,IAAI,CAACxG,UAAU,GAAGiH,QAAQ;IAE1B,IAAI5F,4BAA4B,KAAKsF,SAAS,EAAE;MAC9C,IAAI,CAACzG,0BAA0B,GAAGmB,4BAA4B;IAChE;IAEA,IAAI,CAACX,iBAAiB,GAAG,KAAK;EAChC;EAEAe,mBAAmBA,CAAC0G,uBAAiC,EAAE;IACrD,IAAIA,uBAAuB,EAAE,IAAI,CAACC,sBAAsB,CAAC,CAAC;IAC1D,IAAI,CAAC,IAAI,CAACpI,UAAU,EAAE,IAAI,CAACqI,qBAAqB,CAAC,CAAC;EACpD;EAEAD,sBAAsBA,CAAA,EAAG;IACvB,IAAI,IAAI,CAACjI,4BAA4B,EAAE;IACvC,IAAI,CAACA,4BAA4B,GAAG,IAAI;IAExC,MAAMmI,OAAO,GAAG,IAAI,CAAC7I,MAAM,CAAC8I,sBAAsB;IAClD,IAAID,OAAO,EAAE;MACX,IAAI,CAACE,aAAa,CAChB;QACErJ,IAAI,EAAE,cAAc;QACpBE,KAAK,EAAEiJ;MACT,CAAC,GAEH,CAAC;IACH;EACF;EAEAD,qBAAqBA,CAAA,EAAG;IACtB,IAAI,CAAC,IAAI,CAAClI,4BAA4B,EAAE;IACxC,IAAI,CAACA,4BAA4B,GAAG,KAAK;IAEzC,MAAMmI,OAAO,GAAG,IAAI,CAAC7I,MAAM,CAACgJ,qBAAqB;IACjD,IAAIH,OAAO,EAAE;MACX,IAAI,CAACE,aAAa,CAChB;QACErJ,IAAI,EAAE,cAAc;QACpBE,KAAK,EAAEiJ;MACT,CAAC,GAEH,CAAC;IACH;EACF;EAEAI,cAAcA,CACZvG,IAKa,EACO;IACpB,MAAMgF,KAAK,GAAGhF,IAAI,CAACgF,KAAK;IACxB,IACE,CAAAA,KAAK,oBAALA,KAAK,CAAEwB,GAAG,KAAI,IAAI,IAClBxB,KAAK,CAACyB,QAAQ,IAAI,IAAI,IACtBzG,IAAI,CAAC9C,KAAK,KAAK8H,KAAK,CAACyB,QAAQ,EAC7B;MAEA,OAAOzB,KAAK,CAACwB,GAAG;IAClB;EACF;EAEAE,SAASA,CACPC,KAAuC,EACvCC,IAAsB,GAAG,CAAC,CAAC,EAC3B;IACA,IAAI,EAACD,KAAK,YAALA,KAAK,CAAEhI,MAAM,GAAE;IAEpB,IAAI;MAAEF;IAAO,CAAC,GAAGmI,IAAI;IAErB,IAAInI,MAAM,IAAI,IAAI,IAAI,IAAI,CAACnB,MAAM,CAACwE,WAAW,EAAE;MAAA,IAAA+E,YAAA;MAC7C,MAAMC,SAAS,IAAAD,YAAA,GAAGF,KAAK,CAAC,CAAC,CAAC,CAACvG,GAAG,qBAAZyG,YAAA,CAAcrB,KAAK,CAAC7B,IAAI;MAC1C,IAAImD,SAAS,IAAI,IAAI,IAAIA,SAAS,KAAK,IAAI,CAACjI,IAAI,CAACgF,cAAc,CAAC,CAAC,EAAE;QACjEpF,MAAM,GAAG,IAAI;MACf;IACF;IAEA,IAAIA,MAAM,EAAE,IAAI,CAACA,MAAM,CAAC,CAAC;IAEzB,MAAMsI,WAA+B,GAAG;MACtCC,WAAW,EAAEJ,IAAI,CAACI,WAAW;MAC7BC,iBAAiB,EAAE;IACrB,CAAC;IAED,MAAMC,SAAS,GAAGN,IAAI,CAACM,SAAS,GAAGN,IAAI,CAACM,SAAS,CAACpB,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI;IAEnE,MAAMqB,GAAG,GAAGR,KAAK,CAAChI,MAAM;IACxB,KAAK,IAAIkD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsF,GAAG,EAAEtF,CAAC,EAAE,EAAE;MAC5B,MAAM7B,IAAI,GAAG2G,KAAK,CAAC9E,CAAC,CAAC;MACrB,IAAI,CAAC7B,IAAI,EAAE;MAEX,IAAI4G,IAAI,CAACQ,SAAS,EAAE,IAAI,CAACC,aAAa,CAACxF,CAAC,KAAK,CAAC,EAAEkF,WAAW,CAAC;MAE5D,IAAI,CAAC1H,KAAK,CAACW,IAAI,EAAEwE,SAAS,EAAEoC,IAAI,CAAC5C,0BAA0B,IAAI,CAAC,CAAC;MAEjE4C,IAAI,CAACU,QAAQ,YAAbV,IAAI,CAACU,QAAQ,CAAGtH,IAAI,EAAE6B,CAAC,CAAC;MAExB,IAAIA,CAAC,GAAGsF,GAAG,GAAG,CAAC,EAAED,SAAS,YAATA,SAAS,CAAG,CAAC;MAE9B,IAAIN,IAAI,CAACQ,SAAS,EAAE;QAAA,IAAAG,sBAAA;QAClB,IAAI,GAAAA,sBAAA,GAACvH,IAAI,CAAC4F,gBAAgB,aAArB2B,sBAAA,CAAuB5I,MAAM,GAAE;UAClC,IAAI,CAACL,gBAAgB,GAAG,CAAC;QAC3B;QAEA,IAAIuD,CAAC,GAAG,CAAC,KAAKsF,GAAG,EAAE;UACjB,IAAI,CAACvF,OAAO,CAAC,CAAC,CAAC;QACjB,CAAC,MAAM;UAAA,IAAA4F,aAAA;UACL,MAAMC,QAAQ,GAAGd,KAAK,CAAC9E,CAAC,GAAG,CAAC,CAAC;UAC7BkF,WAAW,CAACE,iBAAiB,GAAG,EAAAO,aAAA,GAAAC,QAAQ,CAACrH,GAAG,qBAAZoH,aAAA,CAAchC,KAAK,CAAC7B,IAAI,KAAI,CAAC;UAE7D,IAAI,CAAC0D,aAAa,CAAC,IAAI,EAAEN,WAAW,CAAC;QACvC;MACF;IACF;IAEA,IAAItI,MAAM,EAAE,IAAI,CAACiB,MAAM,CAAC,CAAC;EAC3B;EAEAgI,wBAAwBA,CAAC1H,IAAY,EAAE;IACrC,MAAMvB,MAAM,GAAGuB,IAAI,CAACmF,eAAe,IAAInF,IAAI,CAACmF,eAAe,CAACxG,MAAM,GAAG,CAAC;IACtE,IAAIF,MAAM,EAAE,IAAI,CAACA,MAAM,CAAC,CAAC;IACzB,IAAI,CAACY,KAAK,CAACW,IAAI,CAAC;IAChB,IAAIvB,MAAM,EAAE,IAAI,CAACiB,MAAM,CAAC,CAAC;EAC3B;EAEAiI,UAAUA,CAAC9C,MAA8C,EAAE;IACzD,MAAM7E,IAAI,GAAG6E,MAAM,CAAC+C,IAAI;IAExB,IAAI5H,IAAI,CAAChD,IAAI,KAAK,gBAAgB,EAAE;MAClC,IAAI,CAACuD,KAAK,CAAC,CAAC;IACd;IAEA,IAAI,CAAClB,KAAK,CAACW,IAAI,CAAC;EAClB;EAEA+F,sBAAsBA,CAAC/F,IAAY,EAAE6E,MAAe,EAAEgD,UAAmB,EAAE;IACzE,MAAM;MAAEC,aAAa;MAAElC;IAAiB,CAAC,GAAG5F,IAAI;IAIhD,IAAI8H,aAAa,YAAbA,aAAa,CAAEnJ,MAAM,EAAE;MACzB,IAAI,CAACoJ,cAAc,IAEjBD,aAAa,EACb9H,IAAI,EACJ6E,MAAM,EACNgD,UACF,CAAC;IACH;IACA,IAAIjC,gBAAgB,YAAhBA,gBAAgB,CAAEjH,MAAM,EAAE;MAC5B,IAAI,CAACoJ,cAAc,IAEjBnC,gBAAgB,EAChB5F,IAAI,EACJ6E,MAAM,EACNgD,UACF,CAAC;IACH;EACF;EAEAhC,qBAAqBA,CAAC7F,IAAY,EAAE6E,MAAc,EAAE;IAClD,MAAMmD,QAAQ,GAAGhI,IAAI,CAACmF,eAAe;IACrC,IAAI,EAAC6C,QAAQ,YAARA,QAAQ,CAAErJ,MAAM,GAAE;IACvB,IAAI,CAACoJ,cAAc,IAAuBC,QAAQ,EAAEhI,IAAI,EAAE6E,MAAM,CAAC;EACnE;EAEA9D,wBAAwBA,CAAA,EAAG;IACzB,IAAI,IAAI,CAACxC,iBAAiB,EAAE,IAAI,CAAC0J,kBAAkB,CAAC,CAAC;IACrD,IAAI,CAAC1J,iBAAiB,GAAG,IAAI;IAC7B,IAAI,CAACC,oBAAoB,GAAG,IAAI;EAClC;EAEAyJ,kBAAkBA,CAAA,EAAG;IACnB,MAAMjI,IAAI,GAAG,IAAI,CAACtC,YAAY;IAC9B,MAAMsK,QAAQ,GAAGhI,IAAI,CAAC8H,aAAa;IACnC,IAAI,EAACE,QAAQ,YAARA,QAAQ,CAAErJ,MAAM,GAAE;IAEvB,MAAMuJ,QAAQ,GAAG,IAAI,CAAChG,QAAQ,GAAgB,CAAC;IAC/C,MAAMzD,MAAM,GAAG,IAAI,CAACD,oBAAoB;IACxC,MAAM2J,oBAAoB,GAAG,IAAI,CAAClK,gBAAgB,CAACmK,IAAI;IACvD,IAAI3J,MAAM,EAAE,IAAI,CAACA,MAAM,CAAC,CAAC;IACzB,IAAI,CAACsJ,cAAc,IAAqBC,QAAQ,EAAEhI,IAAI,CAAC;IACvD,IAAIkI,QAAQ,IAAIC,oBAAoB,KAAK,IAAI,CAAClK,gBAAgB,CAACmK,IAAI,EAAE;MACnE,IAAI,CAAC7H,KAAK,CAAC,CAAC;IACd;IACA,IAAI9B,MAAM,EAAE,IAAI,CAACiB,MAAM,CAAC,CAAC;EAC3B;EAEA2I,yBAAyBA,CAAA,EAAG;IAC1B,IAAI,CAAC7J,oBAAoB,GAAG,KAAK;EACnC;EAEA8J,aAAaA,CAAC3B,KAAe,EAAEC,IAA0B,GAAG,CAAC,CAAC,EAAE;IAAA,IAAA2B,YAAA;IAC9D3B,IAAI,CAACQ,SAAS,GAAG,IAAI;IACrB,CAAAmB,YAAA,GAAA3B,IAAI,CAACnI,MAAM,YAAA8J,YAAA,GAAX3B,IAAI,CAACnI,MAAM,GAAK,KAAK;IACrB,IAAI,CAACiI,SAAS,CAACC,KAAK,EAAEC,IAAI,CAAC;EAC7B;EAEA4B,SAASA,CAACC,KAAe,EAAE7B,IAAsB,GAAG,CAAC,CAAC,EAAE;IACtD,IAAIA,IAAI,CAACM,SAAS,IAAI,IAAI,EAAE;MAC1BN,IAAI,CAACM,SAAS,GAAGwB,cAAc;IACjC;IAEA,IAAI,CAAChC,SAAS,CAAC+B,KAAK,EAAE7B,IAAI,CAAC;EAC7B;EAEAS,aAAaA,CAACsB,OAAgB,EAAE/B,IAAwB,EAAE;IACxD,MAAMtJ,MAAM,GAAG,IAAI,CAACA,MAAM;IAG1B,IAAIA,MAAM,CAACwE,WAAW,IAAIxE,MAAM,CAACkC,OAAO,EAAE;IAI1C,IAAIlC,MAAM,CAACmC,OAAO,EAAE;MAClB,IAAI,CAACc,KAAK,CAAC,CAAC;MACZ;IACF;IAEA,IAAI,CAACoI,OAAO,EAAE;MACZ;IACF;IAEA,MAAM7B,SAAS,GAAGF,IAAI,CAACK,iBAAiB;IACxC,MAAM2B,eAAe,GAAG,IAAI,CAACtK,gBAAgB;IAC7C,IAAIwI,SAAS,GAAG,CAAC,IAAI8B,eAAe,GAAG,CAAC,EAAE;MACxC,MAAMC,MAAM,GAAG/B,SAAS,GAAG8B,eAAe;MAC1C,IAAIC,MAAM,IAAI,CAAC,EAAE;QACf,IAAI,CAACjH,OAAO,CAACiH,MAAM,IAAI,CAAC,CAAC;QACzB;MACF;IACF;IAGA,IAAI,IAAI,CAAChK,IAAI,CAAC4B,UAAU,CAAC,CAAC,EAAE;MAa1B,IAAI,CAACmB,OAAO,CAAC,CAAC,CAAC;IACjB;EACF;EAOAkH,mBAAmBA,CAAC3C,OAAkB,EAAsB;IAG1D,IAAIA,OAAO,CAAC4C,MAAM,EAAE;IAEpB,IAAI,IAAI,CAAC9K,gBAAgB,CAAC+K,GAAG,CAAC7C,OAAO,CAAC,EAAE;IAExC,IACE,IAAI,CAACrI,iBAAiB,IACtBjB,gCAAgC,CAACI,IAAI,CAACkJ,OAAO,CAACjJ,KAAK,CAAC,EACpD;MACA;IACF;IAEA,IAAI,CAACe,gBAAgB,CAACgL,GAAG,CAAC9C,OAAO,CAAC;IAElC,IAAI,CAAC,IAAI,CAAC7I,MAAM,CAAC4L,kBAAkB,CAAC/C,OAAO,CAACjJ,KAAK,CAAC,EAAE;MAClD;IACF;IAEA;EACF;EAEAmJ,aAAaA,CAACF,OAAkB,EAAEgD,YAAkC,EAAE;IACpE,MAAMC,gBAAgB,GAAG,IAAI,CAACtL,iBAAiB;IAC/C,MAAMuL,cAAc,GAAGlD,OAAO,CAACnJ,IAAI,KAAK,cAAc;IAItD,MAAMsM,aAAa,GACjBD,cAAc,IACdF,YAAY,MAA6B,IACzC,CAAC,IAAI,CAACrL,iBAAiB;IAEzB,IACEwL,aAAa,IACb,IAAI,CAACzK,IAAI,CAAC4B,UAAU,CAAC,CAAC,IACtB0I,YAAY,MAAiC,EAC7C;MACA,IAAI,CAACvH,OAAO,CAAC,CAAC,CAAC;IACjB;IAEA,MAAM2H,YAAY,GAAG,IAAI,CAAC5I,WAAW,CAAC,CAAC;IACvC,IACE4I,YAAY,OAAgC,IAC5CA,YAAY,QAA6B,IACzCA,YAAY,OAA8B,EAC1C;MACA,IAAI,CAAChJ,KAAK,CAAC,CAAC;IACd;IAEA,IAAIiJ,GAAG;IACP,IAAIH,cAAc,EAAE;MAClBG,GAAG,GAAG,KAAKrD,OAAO,CAACjJ,KAAK,IAAI;MAC5B,IAAI,IAAI,CAACI,MAAM,CAACmB,MAAM,CAACgL,sBAAsB,EAAE;QAAA,IAAAC,YAAA;QAC7C,MAAMb,MAAM,IAAAa,YAAA,GAAGvD,OAAO,CAAC/F,GAAG,qBAAXsJ,YAAA,CAAalE,KAAK,CAACmE,MAAM;QACxC,IAAId,MAAM,EAAE;UACV,MAAMe,YAAY,GAAG,IAAIC,MAAM,CAAC,WAAW,GAAGhB,MAAM,GAAG,GAAG,EAAE,GAAG,CAAC;UAChEW,GAAG,GAAGA,GAAG,CAACM,OAAO,CAACF,YAAY,EAAE,IAAI,CAAC;QACvC;QACA,IAAI,IAAI,CAACtM,MAAM,CAACmC,OAAO,EAAE;UACvB+J,GAAG,GAAGA,GAAG,CAACM,OAAO,CAAC,UAAU,EAAE,IAAI,CAAC;QACrC,CAAC,MAAM;UACL,IAAIC,UAAU,GAAG,IAAI,CAACzM,MAAM,CAACwE,WAAW,GACpC,CAAC,GACD,IAAI,CAACjD,IAAI,CAACmL,gBAAgB,CAAC,CAAC;UAEhC,IAAI,IAAI,CAACvG,aAAa,GAAgB,CAAC,IAAI,IAAI,CAACnG,MAAM,CAACwE,WAAW,EAAE;YAClEiI,UAAU,IAAI,IAAI,CAACvG,UAAU,CAAC,CAAC;UACjC;UAEAgG,GAAG,GAAGA,GAAG,CAACM,OAAO,CAAC,UAAU,EAAE,KAAK,GAAG,CAACG,MAAM,CAACF,UAAU,CAAC,EAAE,CAAC;QAC9D;MACF;IACF,CAAC,MAAM,IAAI,CAACX,gBAAgB,EAAE;MAC5BI,GAAG,GAAG,KAAKrD,OAAO,CAACjJ,KAAK,EAAE;IAC5B,CAAC,MAAM;MAILsM,GAAG,GAAG,KAAKrD,OAAO,CAACjJ,KAAK,IAAI;IAC9B;IAGA,IAAI,IAAI,CAACmB,YAAY,EAAE,IAAI,CAACmC,MAAM,CAAC,CAAC;IAEpC,IAAI,CAACgC,MAAM,CAAC,OAAO,EAAE2D,OAAO,CAAC/F,GAAG,CAAC;IACjC,IAAI,CAACa,OAAO,CAACuI,GAAG,EAAEH,cAAc,CAAC;IAEjC,IAAI,CAACA,cAAc,IAAI,CAACD,gBAAgB,EAAE;MACxC,IAAI,CAACxH,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC;IACvB;IAEA,IAAI0H,aAAa,IAAIH,YAAY,MAAkC,EAAE;MACnE,IAAI,CAACvH,OAAO,CAAC,CAAC,CAAC;IACjB;EACF;EAEAmG,cAAcA,CACZ/K,IAAkB,EAClBgL,QAA8B,EAC9BhI,IAAY,EACZ6E,MAAe,EACfgD,UAAkB,GAAG,CAAC,EACtB;IACA,MAAMqC,OAAO,GAAGlK,IAAI,CAACI,GAAG;IACxB,MAAM+G,GAAG,GAAGa,QAAQ,CAACrJ,MAAM;IAC3B,IAAIwL,MAAM,GAAG,CAAC,CAACD,OAAO;IACtB,MAAME,aAAa,GAAGD,MAAM,GAAGD,OAAO,CAAC1E,KAAK,CAAC7B,IAAI,GAAG,CAAC;IACrD,MAAM0G,WAAW,GAAGF,MAAM,GAAGD,OAAO,CAACI,GAAG,CAAC3G,IAAI,GAAG,CAAC;IACjD,IAAI4G,QAAQ,GAAG,CAAC;IAChB,IAAIC,qBAAqB,GAAG,CAAC;IAE7B,MAAMjJ,YAAY,GAAG,IAAI,CAACzD,iBAAiB,GACvC,YAAY,CAAC,CAAC,GACd,IAAI,CAAC8D,OAAO,CAACkE,IAAI,CAAC,IAAI,CAAC;IAE3B,KAAK,IAAIjE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsF,GAAG,EAAEtF,CAAC,EAAE,EAAE;MAC5B,MAAMsE,OAAO,GAAG6B,QAAQ,CAACnG,CAAC,CAAC;MAE3B,MAAM4I,WAAW,GAAG,IAAI,CAAC3B,mBAAmB,CAAC3C,OAAO,CAAC;MACrD,IAAIsE,WAAW,MAA6B,EAAE;QAC5CN,MAAM,GAAG,KAAK;QACd;MACF;MACA,IAAIA,MAAM,IAAIhE,OAAO,CAAC/F,GAAG,IAAIqK,WAAW,MAA6B,EAAE;QACrE,MAAMC,gBAAgB,GAAGvE,OAAO,CAAC/F,GAAG,CAACoF,KAAK,CAAC7B,IAAI;QAC/C,MAAMgH,cAAc,GAAGxE,OAAO,CAAC/F,GAAG,CAACkK,GAAG,CAAC3G,IAAI;QAC3C,IAAI3G,IAAI,MAAyB,EAAE;UACjC,IAAI6L,MAAM,GAAG,CAAC;UACd,IAAIhH,CAAC,KAAK,CAAC,EAAE;YAGX,IACE,IAAI,CAAChD,IAAI,CAAC4B,UAAU,CAAC,CAAC,KACrB0F,OAAO,CAACnJ,IAAI,KAAK,aAAa,IAC7B0N,gBAAgB,KAAKC,cAAc,CAAC,EACtC;cACA9B,MAAM,GAAG2B,qBAAqB,GAAG,CAAC;YACpC;UACF,CAAC,MAAM;YACL3B,MAAM,GAAG6B,gBAAgB,GAAGH,QAAQ;UACtC;UACAA,QAAQ,GAAGI,cAAc;UAEzBpJ,YAAY,CAACsH,MAAM,CAAC;UACpB,IAAI,CAACxC,aAAa,CAACF,OAAO,GAA0B,CAAC;UAErD,IAAItE,CAAC,GAAG,CAAC,KAAKsF,GAAG,EAAE;YACjB5F,YAAY,CACVqJ,IAAI,CAACC,GAAG,CAACT,aAAa,GAAGG,QAAQ,EAAEC,qBAAqB,CAC1D,CAAC;YACDD,QAAQ,GAAGH,aAAa;UAC1B;QACF,CAAC,MAAM,IAAIpN,IAAI,MAAuB,EAAE;UACtC,MAAM6L,MAAM,GACV6B,gBAAgB,IAAI7I,CAAC,KAAK,CAAC,GAAGuI,aAAa,GAAGG,QAAQ,CAAC;UACzDA,QAAQ,GAAGI,cAAc;UAEzBpJ,YAAY,CAACsH,MAAM,CAAC;UACpB,IAAI,CAACxC,aAAa,CAACF,OAAO,GAA0B,CAAC;UAErD,IAAItE,CAAC,GAAG,CAAC,KAAKsF,GAAG,EAAE;YACjB5F,YAAY,CAACqJ,IAAI,CAACE,GAAG,CAAC,CAAC,EAAET,WAAW,GAAGE,QAAQ,CAAC,CAAC;YACjDA,QAAQ,GAAGF,WAAW;UACxB;QACF,CAAC,MAAM;UACL,MAAMxB,MAAM,GACV6B,gBAAgB,IAAI7I,CAAC,KAAK,CAAC,GAAGwI,WAAW,GAAGxC,UAAU,GAAG0C,QAAQ,CAAC;UACpEA,QAAQ,GAAGI,cAAc;UAEzBpJ,YAAY,CAACsH,MAAM,CAAC;UACpB,IAAI,CAACxC,aAAa,CAACF,OAAO,GAA0B,CAAC;QACvD;MACF,CAAC,MAAM;QACLgE,MAAM,GAAG,KAAK;QACd,IAAIM,WAAW,MAA6B,EAAE;UAC5C;QACF;QAEA,IAAItD,GAAG,KAAK,CAAC,EAAE;UACb,MAAM4D,UAAU,GAAG5E,OAAO,CAAC/F,GAAG,GAC1B+F,OAAO,CAAC/F,GAAG,CAACoF,KAAK,CAAC7B,IAAI,KAAKwC,OAAO,CAAC/F,GAAG,CAACkK,GAAG,CAAC3G,IAAI,GAC/C,CAAC/G,WAAW,CAACK,IAAI,CAACkJ,OAAO,CAACjJ,KAAK,CAAC;UAEpC,MAAM8N,iBAAiB,GACrBD,UAAU,IACV,CAACzO,WAAW,CAAC0D,IAAI,CAAC,IAClB,CAACzD,WAAW,CAACsI,MAAM,CAAC,IACpB,CAACrI,iBAAiB,CAACqI,MAAM,CAAC,IAC1B,CAACpI,mBAAmB,CAACoI,MAAM,CAAC;UAE9B,IAAI7H,IAAI,MAAyB,EAAE;YACjC,IAAI,CAACqJ,aAAa,CAChBF,OAAO,EACN6E,iBAAiB,IAAIhL,IAAI,CAAChD,IAAI,KAAK,kBAAkB,IACnD+N,UAAU,IAAI1O,UAAU,CAACwI,MAAM,EAAE;cAAE+C,IAAI,EAAE5H;YAAK,CAAC,CAAE,QAGtD,CAAC;UACH,CAAC,MAAM,IAAIgL,iBAAiB,IAAIhO,IAAI,MAA0B,EAAE;YAC9D,IAAI,CAACqJ,aAAa,CAACF,OAAO,GAA0B,CAAC;UACvD,CAAC,MAAM;YACL,IAAI,CAACE,aAAa,CAACF,OAAO,GAA8B,CAAC;UAC3D;QACF,CAAC,MAAM,IACLnJ,IAAI,MAAuB,IAC3B,EAAEgD,IAAI,CAAChD,IAAI,KAAK,kBAAkB,IAAIgD,IAAI,CAACiL,UAAU,CAACtM,MAAM,GAAG,CAAC,CAAC,IACjEqB,IAAI,CAAChD,IAAI,KAAK,WAAW,IACzBgD,IAAI,CAAChD,IAAI,KAAK,iBAAiB,EAC/B;UAMA,IAAI,CAACqJ,aAAa,CAChBF,OAAO,EACPtE,CAAC,KAAK,CAAC,OAEHA,CAAC,KAAKsF,GAAG,GAAG,CAAC,QAGnB,CAAC;QACH,CAAC,MAAM;UACL,IAAI,CAACd,aAAa,CAACF,OAAO,GAA8B,CAAC;QAC3D;MACF;IACF;IAEA,IAAInJ,IAAI,MAA0B,IAAImN,MAAM,IAAII,QAAQ,EAAE;MACxD,IAAI,CAACjM,gBAAgB,GAAGiM,QAAQ;IAClC;EACF;AACF;AAGAW,MAAM,CAACC,MAAM,CAAC/N,OAAO,CAACgO,SAAS,EAAEjP,kBAAkB,CAAC;AAEjB;EAEjCiB,OAAO,CAACgO,SAAS,CAACC,IAAI,GAAG,SAASA,IAAIA,CAAA,EAAgB,CAAC,CAAC;AAC1D;AAAC,IAAAC,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAKcpO,OAAO;AAEtB,SAASsL,cAAcA,CAAA,EAAgB;EACrC,IAAI,CAACrI,SAAK,GAAI,CAAC;EACf,IAAI,CAACE,KAAK,CAAC,CAAC;AACd", "ignoreList": []}